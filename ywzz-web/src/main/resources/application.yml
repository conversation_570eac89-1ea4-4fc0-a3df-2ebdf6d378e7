server:
  port: 10011
  tomcat:
    uri-encoding: UTF-8
  servlet:
    context-path: /ywzz

spring:
  application:
    name: ywzz-service
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************************************************************************************
          username: ywzz_dev
          password: dEZUZZ*jl#$C62
  flyway:
    enabled: true
    url: ${spring.datasource.dynamic.datasource.master.url}
    user: ${spring.datasource.dynamic.datasource.master.username}
    password: ${spring.datasource.dynamic.datasource.master.password}
    locations: classpath:db/migration/ywzz

  data:
    redis:
      host: 127.0.0.1
      port: 6379
      timeout: 10s
      password: E9KwtIjK3WuTPUE335Lt
      database: 15
      lettuce:
        pool:
          max-active: 10
          max-wait: -1
          max-idle: 16
          min-idle: 8
  encrypt:
    enabled: true
    # 到期时间 单位 s
    expire: 86400
  servlet:
    multipart:
      # 单文件大小
      max-file-size: 50MB
      # 总大小
      max-request-size: 100MB

mybatis-plus:
  mapperPackage: com.ctyk.ywzz.mapper
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: token
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 7200
  # token风格
  token-style: random-32
  # 是否尝试从 header 里读取 Token
  is-read-header: true
  # 是否开启自动续签
  auto-renew: false
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时同端互斥)
  is-concurrent: false
  is-log: true


# 大模型中台接口信息
large-model:
  # 报告解读
  report-interpretation:
    upload-url: http://***********:5566/upload
    # 请求、响应超时时间，毫秒
    timeout: 500000
    # 缓存时间，秒
    cache-time: 3600
  # 伴随症状
  accompany-symptoms:
    enable: false
    url: http://172.17.0.111:6202/symptoms/
  # V2.0版本
  v2-info:
    access-token: 048c112404d8aad5604ca77125f1eb4890822a98216fc38bc326b2c548efb6f9fdbbc69dc6bcd110242cc07c909e10a8361a30b07e6df100325834798d1ab8bb67
    api-key: sk-g4jBb9NLJ8BLUoBy7zSsQMV6EA-NuYb5triXdtcRLFM
    home-id: HOME1733187003718
    source: external
    # 智能体
    agent-url: http://**********:16002/agentapp/v2/open/agent/runAgent
    # 智慧分诊
    smart-triage-code: CUCT6KiX
    # 智慧分诊-deepseek
    smart-triage-ds-code: eR7SBolV
    # 意图识别
    intent-recognizer-code: gACSnkJT
    # 智能问药
    smart-medicine-code: HJTAENAn
    # 知识库
    knowledge-url: http://**********:16002/kg/v2/open/knowledge/retrieval
    # 信息咨询
    consult-code: shc_bdy_xczx
    # 模型服务接口-非流式
    model-server-url: http://**********:16002/infer/v2/open/model/chat
    # 没有思考模型编码
    no-think-model-code: Qwen-25-32B
    # 含有思考模型编码
    think-model-code: Deepseek-R1-Distill-Qwen-32B

# 医院相关配置
hospital:
  # 提示相关配置
  tip:
    message: '根据您的描述，暂时无法为您推荐科室。如您想获取挂号科室信息，可以尝试进一步描述症状持续时间或者其他症状。'
    special-chars: '@,#,$,%,^,&,*'
    knowledge-message: "很抱歉，您的问题我暂时还无法回答，您可以试着问我:\n·咳嗽该看什么科？\n·头痛发热三天，去哪个科室？\n·被猫抓伤，挂什么科室？\n我也会努力补充新知识为您服务。"
  api:
    # 支持挂号的院区配置以 , 隔开
    hospital-id: '0003,0001,0002'
    # 号源查询开关,false为关闭不查询
    source-enable: false
    # 线下挂号科室编码
    offline-dept-code:
    # 预约挂号科室编码(线下)
    book-dept-code:
    # 查询未来n天号源
    future-day: 7
    # 排班信息接口地址
    scheduleInfo-url: http://**********:8086/tyfw/services/standardTyWsService
    # 就诊人信息接口地址
    visitInfo-url: http://**********:8086/tyfw/services/standardTyWsService
    # 报告接口地址
    report-url: http://**********:8086/tyfw/services/standardTyWsService
    # 科室接口地址
    dept-url: http://**********:8086/tyfw/services/standardTyWsService
    # 查询分院接口
    hospital-url: http://**********:8086/tyfw/services/standardTyWsService
    # 目标机构（）
    object-system: 12140100405752534L
    # 来源（授权编码）
    source-system: znfz#12140100405752534L
    # 授权码
    access-key: xnz0gxhw
    # 请求名称，通过action区别不同的业务调用。
    action:
      # 查询门诊科室
      dept_info: getOutpatientDept
      # 获取医生排班
      doctor-scheduling: getOutpatientScheduling
      # 查询医疗机构
      medical-institution: getHospitalInfo
    # 版本
    version: 1.0
    # 编码，固定值：ConvenientService
    code: ConvenientService

  # 挂号链接配置
  register:
    base-url: https://fwcbj.linkingcloud.cn/app/unattended/#/outpatientService/guahao/register
    # 占位符格式：${paramName}
    url-template: "?deptCode=${hospitalId}_${groupCode}_${deptCode}&curDeptName=${curDeptName}&dataSource=${dataSource}&hospitalID=${hospitalId}&titleName=${titleName}"
    default-params:
      # 参数默认值配置,如固定参数，会覆盖数据库中查询的值
      dataSource: ''
    # 编码策略配置,编码顺序：先对关键字进行编码，再对urlTemplate、baseUrl进行编码
    encoding:
      # NONE:不编码;
      # FULL:全编码,特殊字符也编码
      # PARTIAL:不会对以下字符编码：a-z, A-Z, 0-9 - _ . *
      # FRAGMENT: 不会对以下字符进行编码：a-z, A-Z, 0-9 - _ . ! ~ * ' ( ) / ? ; : @ & = + , $
      # 可选参数 [titleName, hospitalId, curDeptName,  department, dataSource, deptCode, groupCode]
      keywords:
      deptCode: PARTIAL
      curDeptName: PARTIAL
      dataSource: PARTIAL
      hospitalId: PARTIAL
      titleName: PARTIAL
    # url编码
    url-template-encode: FRAGMENT
    base-url-encode: NONE

url:
  # 放行的url
  exclude:
    - /api/users/token
    - /api/wx
  # 拦截的url
  include:
    - /api/**
third-system:
  app-code: YWZZ
  secret-key: aD3cD4eF5gH6iJ7kL8hN

# 提示词
prompt-file-path:
  assistant: classpath:assistantPrompt.txt
  assistant-ds: classpath:assistantPrompt-ds.txt
common:
  license:
    endpoint: http://*************:8888/li/api/license/verify
# 语音文本互转
tts:
  enable: ai
  # 云之声
  yzs:
    host: https://af-asr.hivoice.cn
    app-key: dtll53wwkcohg2f6qosebarckh6c3k24dxpz5faa
    app-secret: cb52925c8db5ff7676135f7a90eaea7b
    wss-host: wss://ws-stts.hivoice.cn/v1/tts?
    # 发音人列表 https://ai.unisound.com/doc/ttsshort/WebAPI.html#%E5%8F%91%E9%9F%B3%E4%BA%BA%E5%88%97%E8%A1%A8
    vcn: chengcheng-neutral-plus
    # 文本转语音超时时间,单位秒
    text-to-speech-timeout: 60
    # 语音转文本重试次数
    retry-times: 50
    # 重试间隔时间，毫秒
    retry-interval: 1000
    # 语音转文本没有内容默认文本
    empty-text: 您好，请问有什么需要帮助的吗？
    # 长文本转语音
    ltts-host: https://ltts.hivoice.cn
    # 长文本转语音等待时间时间,单位毫秒
    max-wait-time: 30000
  # ai公司语言相关配置
  ai:
    protocol: ws://
#    base-url: openapi.teleagi.cn:443
    base-url: ***********:8111
    id: b63b7630ee334f68a68c2545d99f6084
    key: c42157c6d0a54095944ff20d28265b9e
    origin-name: teleai-cloud-auth-v1
    region: BJ
    voice: yixiaobei
    # 授权过期时间，秒
    expiration: 1800
    # 转换等待时间，秒
    wait-time: 60

logging:
  level:
    com.ctyk.ywzz: debug
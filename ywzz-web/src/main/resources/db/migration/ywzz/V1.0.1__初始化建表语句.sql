create table shc_conversation_record
(
    record_id   bigint       not null comment '对话记录ID'
        primary key,
    user_id     varchar(255) not null comment '用户ID',
    start_time  datetime     not null comment '对话开始时间',
    departments text         null comment '推荐科室;对话结论：大模型推荐科室列表'
)
    comment '对话记录';

create table shc_conversation_record_detail
(
    record_detail_id bigint       not null comment '对话记录详细ID'
        primary key,
    record_id        bigint       not null comment '对话记录ID',
    health_status    varchar(900) not null comment '健康状态;用户提问内容',
    recommendations  text not null comment '健康建议;大模型回答内容',
    departments      text         null comment '推荐科室;大模型推荐科室列表',
    operate_time     datetime     not null comment '操作时间',
    type             tinyint      null comment '模型类型 0:Rag 1:llm'
)
    comment '对话记录详细';

create table shc_health_care_template
(
    template_id bigint       not null comment '模板序列号'
        primary key,
    content     varchar(900) not null comment '模板内容',
    page_name   varchar(50)  null comment '页面名称',
    seq_no      varchar(90)  not null comment '序号'
)
    comment '咨询模板库';

create table shc_medical_report
(
    medical_report_id bigint       not null comment '体检报告主键'
        primary key,
    id_card           varchar(20)  not null comment '身份证',
    patient_no        varchar(32)  not null comment '患者id',
    name              varchar(255) null comment '患者姓名',
    sex               varchar(255) null comment '性别',
    age               int          null comment '年龄',
    exam_date         datetime     null comment '体检日期;YYYY-MM-DD',
    adm_id            bigint       null comment '就诊ID',
    company           varchar(255) null comment '公司单位',
    report_status     varchar(255) null comment '报告状态',
    pe_status         varchar(255) null comment '体检状态',
    pre_date          datetime     null comment '预约日期;YYYY-MM-DD',
    ord_name          varchar(255) null comment '套餐名称',
    create_time       datetime     not null comment '创建时间',
    create_by         varchar(32)  not null comment '创建者'
) comment '体检报告列表';

create table shc_medical_report_detail
(
    medical_report_detail_id bigint       not null comment '报告详细主键'
        primary key,
    medical_report_id        bigint       not null comment '体检报告主键',
    summary_doctor           varchar(255) null comment '总检医生',
    summary_date             datetime     null comment '总检时间',
    audit_doctor             varchar(255) null comment '审核医生',
    audit_date               varchar(255) null comment '审核时间',
    summaryList              text         null comment '建议列表',
    create_time              datetime     not null comment '创建时间',
    create_by                varchar(32)  not null comment '创建者'
) comment '体检报告详细';

create table shc_organization_map
(
    organization_map_id bigint       not null comment '组织机构映射ID',
    source              varchar(255) null comment '源',
    target              varchar(255) null comment '目标',
    create_time         datetime     null comment '创建时间',
    create_by           varchar(32)  null comment '创建人'
) comment '组织机构映射表';

create table sys_dict_organization
(
    org_id       bigint            not null comment '组织编号'
        primary key,
    org_code     varchar(32)       not null comment '组织编码',
    org_name     varchar(255)      not null comment '组织名称',
    group_code   varchar(32)       null comment '组编码',
    group_name   varchar(255)      null comment '组名称',
    hospital_id  varchar(32)       null comment '医院编码',
    seq_no       int     default 1 not null comment '顺序号',
    enable_flag  tinyint default 1 not null comment '启用标识;1：启用 0：停用',
    remark       varchar(1024)     null comment '备注',
    update_count int     default 1 not null comment '更新次数',
    delete_flag  tinyint default 0 not null comment '删除标识',
    create_by    varchar(32)       not null comment '创建人',
    create_time  datetime          not null comment '创建时间',
    update_by    varchar(32)       not null comment '更新人',
    update_time  datetime          not null comment '更新时间'
)comment '组织机构表';



create table sys_robot_account
(
    account_id   bigint            not null comment '客户端主键'
        primary key,
    system_id    varchar(255)      not null comment '系统ID',
    auth_type    varchar(32)       not null comment '认证类型;01：ak/sk  02 :jwt',
    access_key   varchar(255)      null comment '客户端标识',
    secret_key   varchar(255)      null comment '客户端密钥',
    ip_list      varchar(2048)     null comment 'IP白名单',
    ip_blacklist varchar(2048)     null comment 'IP黑名单',
    expiry_date  datetime          null comment '有效期',
    enable_flag  tinyint           not null comment '启用标识;1:启用  0： 停用',
    field1       varchar(32)       null comment '扩展字段',
    field2       varchar(32)       null comment '扩展字段',
    field3       varchar(32)       null comment '扩展字段',
    update_count int     default 1 not null comment '更新次数',
    delete_flag  tinyint default 0 not null comment '删除标识',
    create_by    varchar(32)       not null comment '创建人',
    create_time  datetime          not null comment '创建时间',
    update_by    varchar(32)       not null comment '更新人',
    update_time  datetime          not null comment '更新时间'
) comment '机器人账号表';

create table shc_conversation_feedback
(
    feedback_id   bigint       not null comment '主键'
        primary key,
    record_id     bigint       not null comment '记录ID',
    option_type   tinyint      not null comment '反馈类型，0-点踩；1-点赞',
    record_scene  int          not null comment '对话场景，1-智慧分诊；0-信息咨询',
    option_id     varchar(255) not null comment '反馈选项ID',
    other_content varchar(512) null comment '其他反馈内容',
    operate_time  datetime     not null comment '操作时间'
)
    comment '对话反馈表';

create table shc_feedback_option
(
    option_id   bigint        not null comment '主键'
        primary key,
    option_name varchar(50)   null comment '反馈名称',
    seq         int default 0 not null comment '序号'
)
    comment '反馈选项表';

create table diagnosis_report
(
    id             bigint       not null comment '主键'
        primary key,
    user_id        varchar(255) not null comment '用户id',
    record_id      bigint       not null comment '对话记录id',
    report_content text         null,
    created_time   datetime     null comment '创建时间',
    updated_time   datetime     null comment '修改时间'
)
    comment '预问诊报告';

create index idx_record_id
    on diagnosis_report (record_id);

create index idx_user_id
    on diagnosis_report (user_id);



<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctyk.ywzz.mapper.SysRobotAccountMapper">

    <select id="getSysRobotAccountInfo" parameterType="java.lang.String"
            resultType="com.ctyk.ywzz.entity.po.SysRobotAccountPO">
        SELECT access_key,
               account_id,
               auth_type,
               create_by,
               create_time,
               expiry_date,
               ip_blacklist,
               ip_list,
               secret_key,
               system_id
        FROM sys_robot_account
        WHERE delete_flag = 0
          AND enable_flag = 1
          AND access_key = #{accessKey}
          AND secret_key = #{secretKey}
    </select>



</mapper>
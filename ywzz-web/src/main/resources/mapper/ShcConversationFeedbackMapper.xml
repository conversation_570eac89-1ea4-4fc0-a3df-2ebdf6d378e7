<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctyk.ywzz.mapper.ShcConversationFeedbackMapper">
    
    <sql id="Feedback_Column_List">
        feedback_id, record_id, option_type, record_scene, option_id, other_content, operate_time
    </sql>
    
    <insert id="saveFeedback">
        insert into shc_conversation_feedback (<include refid="Feedback_Column_List"/>)
        values (#{po.feedbackId}, #{po.recordId}, #{po.optionType}, #{po.recordScene}, #{po.optionId},
        #{po.otherContent}, #{po.operateTime})
    </insert>
    
    <update id="updateFeedback">
        update shc_conversation_feedback
        set option_type   = #{po.optionType},
            option_id     = #{po.optionId},
            other_content = #{po.otherContent},
            operate_time  = #{po.operateTime}
        where record_id = #{po.recordId}
    </update>
    <delete id="deleteFeedback">
        delete from shc_conversation_feedback where record_id=#{recordId}
    </delete>
    
    <select id="listOptions" resultType="com.ctyk.ywzz.entity.po.ShcFeedbackOptionPO">
        select option_id, option_name, seq
        from shc_feedback_option
    </select>
    <select id="countFeedbackByRecordId" resultType="java.lang.Integer">
        select count(1) from shc_conversation_feedback where record_id = #{recordId}
    </select>
    <select id="getFeedback" resultType="com.ctyk.ywzz.entity.po.ShcConversationFeedbackPO">
        select
        <include refid="Feedback_Column_List"/>
        from shc_conversation_feedback where record_id = #{recordId}
    </select>
</mapper>
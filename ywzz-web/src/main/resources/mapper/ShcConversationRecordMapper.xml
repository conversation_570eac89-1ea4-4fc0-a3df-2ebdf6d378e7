<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctyk.ywzz.mapper.ShcConversationRecordMapper">

    <insert id="insertShcConversationRecord" parameterType="com.ctyk.ywzz.entity.po.ShcConversationRecordPO">
        INSERT INTO shc_conversation_record (record_id, user_id, departments, start_time)
        VALUES (#{recordId}, #{userId}, #{departments}, #{startTime})
    </insert>
    
    <select id="getLastRecordIdByUserIdAndModelType" resultType="java.lang.String">
        select re.record_id
        from shc_conversation_record re
                 left join shc_conversation_record_detail de on re.record_id = de.record_id
        where re.user_id = #{userId}
          and de.type = #{modelType}
        order by re.start_time desc
        limit 1
    </select>

</mapper>
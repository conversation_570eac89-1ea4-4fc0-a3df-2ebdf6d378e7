<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctyk.ywzz.mapper.ShcOrganizationMapMapper">

    <select id="getShcOrganizationMapList" resultType="com.ctyk.ywzz.entity.dto.ShcOrganizationMapDTO">
        SELECT t1.source,
               t1.target,
               t1.organization_map_id,
               t2.org_id,
               t2.org_name,
               t2.group_code,
               t2.group_name,
               t2.hospital_id,
               t2.org_code
        FROM shc_organization_map t1, sys_dict_organization t2
        WHERE t1.source in
        <foreach item="name" collection="deptNameList" open="(" separator="," close=")">
            #{name}
        </foreach>
        AND t2.hospital_id in
        <foreach item="hospitalId" collection="hospitalList" open="(" separator="," close=")">
            #{hospitalId}
        </foreach>
        AND t1.target = t2.org_code
          AND t2.enable_flag = 1
    </select>

    <select id="getShcOrganizationMapListByDeptCode" resultType="com.ctyk.ywzz.entity.dto.ShcOrganizationMapDTO">
        SELECT t1.source ,
        t1.target ,
        t1.organization_map_id ,
        t2.org_id ,
        t2.org_name ,
        t2.group_code ,
        t2.group_name ,
        t2.hospital_id ,
        t2.org_code
        FROM shc_organization_map t1, sys_dict_organization t2
        WHERE t2.org_code in
        <foreach item="code" collection="list" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND t1.target = t2.org_code
        AND t2.enable_flag = 1
    </select>


</mapper>
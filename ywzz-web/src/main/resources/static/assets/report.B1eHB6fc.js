import{ao as r,ap as s,aq as a}from"./index-BKmNCtIL.js";function e(s){return r({url:"/api/visit/users",params:s},{isTransformResponse:!1})}function n(s){return r({url:"/api/reports",params:s},{isTransformResponse:!1})}function o(s){return r({url:"/api/reports/report-detail",params:s},{isTransformResponse:!1})}function p(r){return s({url:"/api/reports/upload-pdf",filePath:r,name:"file"},{isTransformResponse:!1})}function i(r){return a({url:"/api/reports/parse-result",params:r},{isTransformResponse:!1})}export{o as a,i as b,n as c,e as g,p as u};

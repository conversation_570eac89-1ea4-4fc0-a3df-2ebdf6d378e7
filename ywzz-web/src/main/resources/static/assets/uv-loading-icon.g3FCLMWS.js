var e,t;import{p as o,q as i,aj as a,ak as r,a as n,c as l,w as d,u as s,s as u,f as c,F as v,h as m,m as y,e as f,t as p,i as g,x as h}from"./index-BKmNCtIL.js";import{_ as w}from"./uv-icon.lsvsjwja.js";const S=w({name:"uv-loading-icon",mixins:[o,i,{props:{show:{type:Boolean,default:!0},color:{type:String,default:"#909193"},textColor:{type:String,default:"#909193"},vertical:{type:Boolean,default:!1},mode:{type:String,default:"spinner"},size:{type:[String,Number],default:24},textSize:{type:[String,Number],default:15},textStyle:{type:Object,default:()=>({})},text:{type:[String,Number],default:""},timingFunction:{type:String,default:"linear"},duration:{type:[String,Number],default:1200},inactiveColor:{type:String,default:""},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.loadingIcon}}],data:()=>({array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}),computed:{otherBorderColor(){const e=a(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show(e){}},mounted(){this.init()},methods:{init(){setTimeout((()=>{}),20)},addEventListenerToWebview(){const e=r(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",(()=>{this.webviewHide=!0})),t.addEventListener("show",(()=>{this.webviewHide=!1}))}}},[["render",function(e,t,o,i,a,r){const w=g,S=h;return e.show?(n(),l(w,{key:0,class:s(["uv-loading-icon",[e.vertical&&"uv-loading-icon--vertical"]]),style:u([e.$uv.addStyle(e.customStyle)])},{default:d((()=>[a.webviewHide?y("",!0):(n(),l(w,{key:0,class:s(["uv-loading-icon__spinner",[`uv-loading-icon__spinner--${e.mode}`]]),ref:"ani",style:u({color:e.color,width:e.$uv.addUnit(e.size),height:e.$uv.addUnit(e.size),borderTopColor:e.color,borderBottomColor:r.otherBorderColor,borderLeftColor:r.otherBorderColor,borderRightColor:r.otherBorderColor,"animation-duration":`${e.duration}ms`,"animation-timing-function":"semicircle"===e.mode||"circle"===e.mode?e.timingFunction:""})},{default:d((()=>["spinner"===e.mode?(n(!0),c(v,{key:0},m(a.array12,((e,t)=>(n(),l(w,{key:t,class:"uv-loading-icon__dot"})))),128)):y("",!0)])),_:1},8,["class","style"])),e.text?(n(),l(S,{key:1,class:"uv-loading-icon__text",style:u([{fontSize:e.$uv.addUnit(e.textSize),color:e.textColor},e.$uv.addStyle(e.textStyle)])},{default:d((()=>[f(p(e.text),1)])),_:1},8,["style"])):y("",!0)])),_:1},8,["style","class"])):y("",!0)}],["__scopeId","data-v-d9c6b36a"]]);export{S as _};

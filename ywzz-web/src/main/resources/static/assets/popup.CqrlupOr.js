var t,e,a,i;import{p as s,q as n,a as o,c as l,s as r,i as A,a6 as c,w as u,_ as m,u as d,m as p,j as g,k as h,v as y,l as f,e as v,t as C,x as k,f as B,b as w,a7 as F,a8 as S,a9 as I,r as z,o as U,G as N,aa as b,F as Q,h as O,ab as q,ac as x,n as P,d as X,ad as D,ae as V,af as R}from"./index-BKmNCtIL.js";import{_ as E,a as H}from"./uv-icon.lsvsjwja.js";import{_ as W}from"./uv-loading-icon.g3FCLMWS.js";const Y=E({name:"uv-gap",mixins:[s,n,{props:{bgColor:{type:String,default:"transparent"},height:{type:[String,Number],default:20},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.gap}}],computed:{gapStyle(){const t={backgroundColor:this.bgColor,height:this.$uv.addUnit(this.height),marginTop:this.$uv.addUnit(this.marginTop),marginBottom:this.$uv.addUnit(this.marginBottom)};return this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}}},[["render",function(t,e,a,i,s,n){const c=A;return o(),l(c,{class:"uv-gap",style:r([n.gapStyle])},null,8,["style"])}]]);class M{constructor(t,e){this.options=t,this.animation=c({...t}),this.currentStepAnimates={},this.next=0,this.$=e}_nvuePushAnimates(t,e){let a=this.currentStepAnimates[this.next],i={};if(i=a||{styles:{},config:{}},T.includes(t)){i.styles.transform||(i.styles.transform="");let a="";"rotate"===t&&(a="deg"),i.styles.transform+=`${t}(${e+a}) `}else i.styles[t]=`${e}`;this.currentStepAnimates[this.next]=i}_animateRun(t={},e={}){let a=this.$.$refs.ani.ref;if(a)return new Promise(((i,s)=>{nvueAnimation.transition(a,{styles:t,...e},(t=>{i()}))}))}_nvueNextAnimate(t,e=0,a){let i=t[e];if(i){let{styles:s,config:n}=i;this._animateRun(s,n).then((()=>{e+=1,this._nvueNextAnimate(t,e,a)}))}else this.currentStepAnimates={},"function"==typeof a&&a(),this.isEnd=!0}step(t={}){return this.animation.step(t),this}run(t){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((()=>{"function"==typeof t&&t()}),this.$.durationTime)}}const T=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];function K(t,e){if(e)return clearTimeout(e.timer),new M(t,e)}T.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((t=>{M.prototype[t]=function(...e){return this.animation[t](...e),this}}));const G=E({name:"uv-transition",mixins:[s,n],emits:["click","change"],props:{show:{type:Boolean,default:!1},mode:{type:[Array,String,null],default:()=>"fade"},duration:{type:[String,Number],default:300},timingFunction:{type:String,default:"ease-out"},customClass:{type:String,default:""},cellChild:{type:Boolean,default:!1}},data:()=>({isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}),watch:{show:{handler(t){t?this.open():this.isShow&&this.close()},immediate:!0}},computed:{transformStyles(){const t={transform:this.transform,opacity:this.opacity,...this.$uv.addStyle(this.customStyle),"transition-duration":this.duration/1e3+"s"};return this.$uv.addStyle(t,"string")}},created(){this.config={duration:this.duration,timingFunction:this.timingFunction,transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(t={}){t.duration&&(this.durationTime=t.duration),this.animation=K(Object.assign(this.config,t),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(t,e={}){if(this.animation){for(let e in t)try{"object"==typeof t[e]?this.animation[e](...t[e]):this.animation[e](t[e])}catch(a){}return this.animation.step(e),this}},run(t){this.animation&&this.animation.run(t)},open(){clearTimeout(this.timer),this.transform="",this.isShow=!0;let{opacity:t,transform:e}=this.styleInit(!1);void 0!==t&&(this.opacity=t),this.transform=e,this.$nextTick((()=>{this.timer=setTimeout((()=>{this.animation=K(this.config,this),this.tranfromInit(!1).step(),this.animation.run(),this.opacity=1,this.$emit("change",{detail:this.isShow}),this.transform=""}),20)}))},close(t){this.animation&&this.tranfromInit(!0).step().run((()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:t,transform:e}=this.styleInit(!1);this.opacity=t||1,this.transform=e,this.$emit("change",{detail:this.isShow})}))},styleInit(t){let e={transform:""},a=(t,a)=>{"fade"===a?e.opacity=this.animationType(t)[a]:e.transform+=this.animationType(t)[a]+" "};return"string"==typeof this.mode?a(t,this.mode):this.mode.forEach((e=>{a(t,e)})),e},tranfromInit(t){let e=(t,e)=>{let a=null;"fade"===e?a=t?0:1:(a=t?"-100%":"0","zoom-in"===e&&(a=t?.8:1),"zoom-out"===e&&(a=t?1.2:1),"slide-right"===e&&(a=t?"100%":"0"),"slide-bottom"===e&&(a=t?"100%":"0")),this.animation[this.animationMode()[e]](a)};return"string"==typeof this.mode?e(t,this.mode):this.mode.forEach((a=>{e(t,a)})),this.animation},animationType:t=>({fade:t?1:0,"slide-top":`translateY(${t?"0":"-100%"})`,"slide-right":`translateX(${t?"0":"100%"})`,"slide-bottom":`translateY(${t?"0":"100%"})`,"slide-left":`translateX(${t?"0":"-100%"})`,"zoom-in":`scaleX(${t?1:.8}) scaleY(${t?1:.8})`,"zoom-out":`scaleX(${t?1:1.2}) scaleY(${t?1:1.2})`}),animationMode:()=>({fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}),toLine:t=>t.replace(/([A-Z])/g,"-$1").toLowerCase()}},[["render",function(t,e,a,i,s,n){const c=A;return s.isShow?(o(),l(c,{key:0,ref:"ani",animation:s.animationData,class:d(a.customClass),style:r(n.transformStyles),onClick:n.onClick},{default:u((()=>[m(t.$slots,"default")])),_:3},8,["animation","class","style","onClick"])):p("",!0)}]]);const j=E({name:"uv-overlay",emits:["click"],mixins:[s,n,{props:{show:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:10070},duration:{type:[String,Number],default:300},opacity:{type:[String,Number],default:.5},...null==(i=null==(a=uni.$uv)?void 0:a.props)?void 0:i.overlay}}],watch:{show(t){document.querySelector("body").style.overflow=t?"hidden":""}},computed:{overlayStyle(){const t={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(){this.$emit("click")},clear(){}}},[["render",function(t,e,a,i,s,n){const r=g(h("uv-transition"),G);return o(),l(r,{show:t.show,mode:"fade","custom-class":"uv-overlay",duration:t.duration,"custom-style":n.overlayStyle,onClick:n.clickHandler,onTouchmove:y(n.clear,["stop","prevent"])},{default:u((()=>[m(t.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}],["__scopeId","data-v-9cc2c1b3"]]);const Z=E({name:"uv-toast",mixins:[s,n],data:()=>({isShow:!1,timer:null,config:{message:"",type:"",duration:2e3,icon:!0,position:"center",complete:null,overlay:!1,loading:!1},tmpConfig:{}}),computed:{iconName(){return this.tmpConfig.icon&&"none"!=this.tmpConfig.icon&&["error","warning","success","primary"].includes(this.tmpConfig.type)?this.$uv.type2icon(this.tmpConfig.type):""},overlayStyle(){const t={justifyContent:"center",alignItems:"center",display:"flex",backgroundColor:"rgba(0, 0, 0, 0)"};return t},iconStyle(){const t={marginRight:"4px"};return t},contentStyle(){const t=this.$uv.sys().windowHeight,e={};let a=0;return"top"===this.tmpConfig.position?a=.25*-t:"bottom"===this.tmpConfig.position&&(a=.25*t),e.transform=`translateY(${a}px)`,e}},created(){["primary","success","error","warning","default","loading"].map((t=>{this[t]=e=>this.show({type:t,message:e})}))},methods:{show(t){this.tmpConfig=this.$uv.deepMerge(this.config,t),this.clearTimer(),this.isShow=!0,this.timer=setTimeout((()=>{this.clearTimer(),"function"==typeof this.tmpConfig.complete&&this.tmpConfig.complete()}),this.tmpConfig.duration)},hide(){this.clearTimer()},clearTimer(){this.isShow=!1,clearTimeout(this.timer),this.timer=null}},unmounted(){this.clearTimer()}},[["render",function(t,e,a,i,s,n){const c=g(h("uv-loading-icon"),W),m=g(h("uv-icon"),H),y=g(h("uv-gap"),Y),B=k,w=A,F=g(h("uv-overlay"),j);return o(),l(w,{class:"uv-toast"},{default:u((()=>[f(F,{show:s.isShow,"custom-style":n.overlayStyle},{default:u((()=>[f(w,{class:d(["uv-toast__content",["uv-type-"+s.tmpConfig.type,"loading"===s.tmpConfig.type||s.tmpConfig.loading?"uv-toast__content--loading":""]]),style:r([n.contentStyle])},{default:u((()=>["loading"===s.tmpConfig.type?(o(),l(c,{key:0,mode:"circle",color:"rgb(255, 255, 255)",inactiveColor:"rgb(120, 120, 120)",size:"25"})):"defalut"!==s.tmpConfig.type&&n.iconName?(o(),l(m,{key:1,name:n.iconName,size:"17",color:s.tmpConfig.type,customStyle:n.iconStyle},null,8,["name","color","customStyle"])):p("",!0),"loading"===s.tmpConfig.type||s.tmpConfig.loading?(o(),l(y,{key:2,height:"12",bgColor:"transparent"})):p("",!0),f(B,{class:d(["uv-toast__content__text",["uv-toast__content__text--"+s.tmpConfig.type]]),style:{"max-width":"400rpx"}},{default:u((()=>[v(C(s.tmpConfig.message),1)])),_:1},8,["class"])])),_:1},8,["style","class"])])),_:1},8,["show","custom-style"])])),_:1})}],["__scopeId","data-v-4fdbf213"]]),L=E({__name:"question",props:{content:{type:String,default:""}},setup:t=>(e,a)=>{const i=A;return o(),l(i,{class:"question-content"},{default:u((()=>[t.content.includes(".pdf")?(o(),B("div",{key:0,class:"content"},[w("img",{class:"pdf-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAsCAYAAAANUxr1AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAANKSURBVFiF7ZhdaxNZGICfMzltPpvWaoNd0DVUbVGhsuyKsCpYFcFdVBBREUTUO0Vv/AGCl94pXgiyLitCvV1EhKL2YumuqNS2uK1oKaVobS39yEc7aTJzvBhKap3JDNrECnlgCPOe95zzkLwneSdgg1LqllpE0nqm80Mi0Wi310I0h/gaL5O9ouvZzRndeOxFyklo0VGmqvciJQEGBiZqZITmuaCpVI0mRDGldtZFo68chWSQKxjqzFwwmzXwV8pFF/IipQEIQXF2LyCV1Y0H6XS63lboW2CYas3U9Gzb+2QytiSEAAxDbTT13KP5UiUREgUOyEKpktRONBxA+jSUUk4pG4P+ymvAkZII+XwaVeGAW1oMvnEN2VEWcqMs5MYnp8yXTBHse0PFu2GYngHHU/qVCCAcgvVx2LwBqqOfC4W7XlLd3oE2oxfJwob/X0PbP/B7C2z9KS9Ufa+tNtLTWzqR+aSn4e49+DAeARDq/KVN+LQnKBUqOPHYAQj6YUaHvn7ofAnBABzcC/4KyOZgbAKedMJkwpqz61eILc+v8bADRsfs15cyw2z2Z4kmzrnKAGxphstXLYnDv0EuB29HoDEOV/+0ctbH4cIp+OMuDA1DUwN0PIfBt9Z4IuW8fi7nRxPnJNDiKjPH+KT1+rwb4qstIcPMx//rtN7BfS1w444VS6by4+60aECDZyEAKWFTIwy9sx/v64f4qvx901r4pdm63GmQQAqIumUCcPYE+Cuhq9eqodoa9zlVEau+vJGSoLpBbPOUfv0v95ymBhgYyt8/fQFvBj36qG4NtNses91Z+yPs3wP3H3/hAtptiWG2mqHARS2TWVcw164wTdO6zp6ACmkd+5utMDxqjSfT3j+uZdXDjE22CoCRF69u1f794KT0fhoWl7paOH20Xays2ykBsvUxRo8fIvrvM0I9vWiZ2dKIBPzW99vu7VAVBub9lpnhEJO7d5DYtoUVM9NU6hlw7oG/DiEgEoYfYhAKfjL0WU9tBgKo+joo0pOrG0uuHyoLuVEWcqMs5Mb3IaSK9vxTkBw4/B0zPpWmQvqKurtAvF+xLHJsXqjPUcg0FZlZz13eFwqhCyHaF8aXZg0pU42UemOlsO1rPwKzNH+Ihpff9gAAAABJRU5ErkJggg==",alt:""}),v(" "+C(t.content),1)])):(o(),B("div",{key:1,class:"content"},C(t.content),1))])),_:1})}},[["__scopeId","data-v-0342aef4"]]),J=F({id:"app-ds",state:()=>({dsFlag:!1}),getters:{getDeepSeek:t=>t.dsFlag},actions:{setDeepSeek(t){this.dsFlag=t}}});function _(){return J(S)}const $=E({__name:"chatInput",props:{type:{type:Number,default:1},question:{type:Boolean,default:""},tab:{type:Number,default:0}},emits:["chat"],setup(t,{expose:e,emit:a}){const i=_(),s=I(),n=a,c=z([]),m=z(!0);U((async()=>{c.value=[{templateId:"1",name:"智慧分诊",ico:"1",type:"1",url:null,seqNo:"1"},{templateId:"2",name:"报告解读",ico:"2",type:"1",url:null,seqNo:"2"},{templateId:"7",name:"信息咨询",ico:"6",type:"6",url:null,seqNo:"7"},{templateId:"3",name:"智能问药",ico:"3",type:"6",url:null,seqNo:"3"}]}));const y=z(""),k=z(),F=()=>{if(""===y.value.trim())return k.value.show({type:"default",message:"请输入问题"});n("chat",{type:"question",content:y.value}),y.value=""};return e({handleInput:(t,e,a)=>{n("chat",{type:"question",content:t,templateId:e,pageName:a})}}),(e,a)=>{const S=A,I=q,z=g(h("uv-toast"),Z);return o(),B(Q,null,[f(S,{class:"chat-input",style:r(N(b).isDeepSeek?"":{height:"154rpx",padding:"32rpx 44rpx"})},{default:u((()=>[N(b).isDeepSeek?(o(),l(S,{key:0,class:d(N(i).dsFlag?"ds-btn":"ds-btn-no"),onClick:a[0]||(a[0]=t=>{i.setDeepSeek(!i.dsFlag)})},{default:u((()=>[N(i).dsFlag?(o(),B("img",{key:0,class:"deep-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAVWSURBVFiF7ZdfiFR1FMc/53fvTBtNs2uuyz7swy4UGBTkg4Rk6IOSgtEmO2YoZBTYg+AuurZ/iq7k7larqJDggw89GBqzlaFhUoHChkiBQS+BwQr5sKyzqbt3YWfm3t/p4c4MM7N3xJ7swQPD3Pv7nXO+33t+58+98Egeskj9Qrd3p8UtmF5RXkKkCZgW5HIQFL775rPltx7E6dYDsx2um3xN0fWqtAIYI5POoh4/cySda0gg482kKDT9CqyM8RsAFyA8mB1d9nsccGbozgvgfARsAdwYlT+LyXDNOW/Z3fKCqdkuPL4XWKnoz1bs6qINu0JjNyocQcgB3eBc3zY0f2brgdmO6ifeNjR/BpzrQHekq8dDYzcWbdgF4SpFfwZWugXT2zgCw/M/orrBil399UjLb9V7u7yppoXC8u0gH4N2gPqo9AoEKhwDWhCmVfkwlZw9/YXXtVht//rQ/HMu+gciP2VHntxYXq8Nk1UXgTDUmnMCKDn8Ypc3ddYvLDskmL0IpzTaDhR7RBIFb8Jr82NCj9rAxzgRRpXUHIEKtwASmLgcqBARKyeB6oS8pWpOZRuA1/gUqXm4GgJi9VqkY15p5Cgz/M9ajFwFOkEvgX4PdBrhamb4n7WN7Co+rb3SkIBblLNAoEa37/KmmuqdbB24uwF1LgKtip64nUxvuZ1Mdyt8CrSgzsWewbkt9Xa7vKkmFd0JBKENzjUkENWonENpX1h86t16cMeYiyApVfonRpv3XPYkuOxJMDGaHlDVPpCUCN/Wk/DzT70HtAIX6ntJbRkCEIwA4MhwxptJQRR2x8i3gKuqfRNj6cP1VhNjzcciErgieqZ8HBlvJiVG3o+0woP1dks6IUDP4L3TIrJDRI5paE+UzrxVlf4yeKbfb9eE3QEgxcUvs+Nt0yXbXhE5Cty1yhrHyG5V7QW+zo6mex6IwNYDsx2Om/gDSBFle6eiJyZGm/eUwUna6yjtACjTBGZVdjwVkRiaOyywD+UWQjvgh0Hx+bhWHnMEUFLsI+oTnaCXcsl0pYOpa3dWwKPHqEQDIJd8cgD0e4SOko++RnMklsASUdG6hWCpTl00VZzKtdVUI9exBEp9/ijRALqJsGlF3j9a3k8U5DTCdMVAmJbAnC7fthX9cYRNwE0gwDC2feBO5wMTMG5iDGhR7HGsbgRyiO7pGZzbD1G5FhPhs6rap0o/harzH7zXW0q6u1bZDHoCJBUadzwOa0kSlkbqdYRpEovPZL02PzM8txblIpCKyrD5WJyzqgrwETZnR9KTGW8mRbHpBkp73JCLiYA7HP3rSLm3Z0fSk2rCV4FARI72DM3tuw94oMqb2ZH0JEDWa/PV6qcARs3wfSNQKr8phNwTidmu+pHa88Gd9WKd80AKlc9vP5bqA2jL++Mq2gv4WM1kP2n+odquNMr/BlrCoNhVXRE1EXCM2w24qH5VDw4wcWjZZYTNlHJiRX7+/Ir8/PkSeA5hcz04lEe5fgm4JYyK1I5jIy8CYFnipCzZkfQkVtdQqo5ytlvl5XLYY6Xs05h1DQmI0gFQxP7ZyE/Gm0mpyG6go2q5Q+Cd8uyIk4pP1daGBDASADiO1ChBdI7bhv23yDfdEGE/4ANvl36+CPvJN914Y3B+d9woF+OmqjHKUvN6pNZeEZENxjpjmYGFgdAxOaPh00Z1y0JetyO2HQFUvwrDYH85mTL9/g/qhodFZIdFTy7kl3vbBufPWpELVpy/nNC2ovYTBFTtLzXEqm8exmt5/IdJ3uwRY9ZFL6mSw9oroQ3O/ZcPE8e43RizDtVWjASovVZM2sPV4I/kfyH/ArRflRnlEteVAAAAAElFTkSuQmCC",alt:""})):(o(),B("img",{key:1,class:"deep-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAVKSURBVFiF7ZdPaBz3Fcc/7zdrowWtMN49WEaHsWnBXdplNSMlmKYkB5vE4LaitMUlgRB68EUQmdg04JYmtMENcbANNQSaQw8Odg6laZ3ihCYQg0pI7RktCiyhKXQgglVAgnRZooBmfi+HmRGzq1njntJD3ml/f977vv2+f7+Br+UrFhndaLfb+4wxS8B3RWQCWFfV9+I4/svq6ura/RhttVozlUrlhyLyCNDItpeBK0EQbIx1oNlsTlar1TvAkRK7MfBmkiTPdzqdThlwu91uO47za+AkUCm58lGSJEc7nc5n+YYpnk5MTDwNHFHVd62180mSHLLWHgdeBjaABcdxVjzPu95qtWaK/9jzvOuO46wAC9ndK9ba40mSHDLGzKrqu8CRjN1yBjzP+7uIHLPWzq+srNwtnrmuO7F///5TIvIbYEZVB8aYJVWNgcvAPmAd+NXm5ua1KIq+KOrPzc19W1U/VNV3wjA8nu8P0SQiFQBVHYoTQBRFX0RR9EfXdW/U6/XfisjTqvpqdhwDL29tbT3X7XYHu4gH4jgeOI6zg5HLUAhEZA3AGFOWAzuOqOorQDEh14BXx4GP2Bz6c0MOWGs/yC4/Os6Q7/sPicj7gAu8Dfwt+/2+7/sP3cOBRwFU9fZYB0TkBhCr6inXdSdGjczOzh5T1VtAQ1Wv1mq1k7VabQF4Edinqrc8zzs5que67oSqPgHEcRy/UTxzioter/f5wYMHW8AD1Wr1016v988iuDHmVtYbzoVh+MsoimwURbbX670zPT39XxH5gYj8dHp6eqXX6/0r1z18+PCiiPwI+Gun0/nDWAYAkiR5IWPjfLPZnIQd2v8MVFT1TBAEF0f1wjC8rKpnSBP7eh6OZrM5KSK/yGw/P6q3qxNmgNeAx1X1MnA1i3kDOJeDz8/PH7DWPg5gjHntzp076wCe5y2JyCXgM+Coqp4WkSVV/VMYhj++LwdardbMnj17PgQmVXVNRFxVvRqG4WIBfAU4kKmsG2Nmcyd8378IPENaHQeAwfb29nfKWvmuEACsrq6u5XSKiAu8PTU1tdPBrLVPFMABdtgAqNVqz5JWxwxZ2MbNkVIHRkVVdWQdl1yTkTs7CW6MmRxnu9SBVqs1k8UxBiIReazf71/aQRK5Rtp2c1k3xlzLF4PB4CUReQyIgNhae6Hdbrv37cDevXsvkPb2K6p6HNgQkUXf988CBEGwkSTJt7IwnSvG3/O8JVVdIk3CE6RJPOk4zktlWGXvgXY21da3tra+2e12B1lJ3SJNyjNhGF4uM1aogAFwIgiC5WzEf0yaJ7uG3C4GHMc5D6CqL+S9PQiCZRH5PhCLyCXf95+5B3isqj8LgmAZoNvtDlT1RQBjzPl7MpCV33+Ajc3NzUMlI/URVb2ZMfH7qampM5DGPKN9oKo/CcPwraKe67oT9Xr9E2Df9vb2oWJFDDFQqVQWSDvZ66PgAHfv3n2PNK4bIrLY7/dv9vv9mxn4BnBiFBzSCQq8lkJUFopnQw4YYx4EUNVdRnIJgmBZVY+SVUch27+X014muU0ReXisA6o6A2Ct/Wicoay3nyZtMrnMAD/PZ0eZFGw2ivujDsSZl0OXII2j7/tPZhl9ljTeT6nqU6RZf7ZarX48Nzd3umyUVyqVySLGzn5xISK3gWMicsHzvGezp9k3HMc5qaqnyNqvqr4ex/HZPJnm5+ffstZeJB1gr9Tr9ecajcaNJEneBP4tIg1r7e9EBGPMP4Ywi4uv4lle+mHiOM6iqj6cPSA3VPV2HMdv/I8fJgtZwjUy2j+w1l4sgn8t/xfyJT6EoWLQxeaHAAAAAElFTkSuQmCC",alt:""})),v(" 深度思考(R1) ")])),_:1},8,["class"])):p("",!0),w("div",{style:{display:"flex"}},[m.value?(o(),B("div",{key:0,class:"uni-input"},[w("div",{class:d(2===t.tab?"uni-input-disable":"uni-input-in")},[f(I,{disabled:2===t.tab,modelValue:y.value,"onUpdate:modelValue":a[1]||(a[1]=t=>y.value=t),placeholder:"有什么需要问我的吗~"},null,8,["disabled","modelValue"]),t.question?(o(),B("img",{key:0,class:"send",src:"/ywzz/assets/send-cO2C-Ctl.gif",alt:""})):(o(),B("img",{key:1,class:"send",onClick:F,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABElJREFUWEfNmF9oHFUUxr9zN8Sa7k4kMT7Uxv8VpO2TYEUftFSKEUopu7PRQiI2acW+SCAvOxvoomamPtT2RSlaQ5FicWeCSCwqKhZBoX0QweqDqQ/VasAouLsxaTbZOTKzO8ns7OzsbnYbM0/D3nvP/c253z3fYQkb7KENxoP/DyiVErH86G4B7k6rYR0gtpKz7kD7k/O97ebSC0w4RKC77RNiknUtYqwbkCz/2G4+eNc+YZrDINoLQLilwhBPGWr4y5sOdGAs91DI5CECBgH0+OmVwdeM9pP3IZUybwqQfPTPMN22KW4CFshjHog8gCmArgP8cum4jula5BVnXss0FE1kdhHRMJj7iSjiAblC4Ikci3OfaJFZWclaeokCMBcKy/d+/HrXry0BOpDIdofAA0Q0BGBHOQRnGXSeqG1CH++47Iz1JWZ6wrT5OoB2MD7VNanPva7xDKVSQs6P7mEUhsG0nwi3OAGZwUT4GiTevTGbnZx6e8u8VzdxJTPCoDdsvRDF0uORyTUB+V7X1Ui/A3wWJs7qxzuvBhVbWcn+UMrmLKZ/26rr2y1drTyBGapxXW2BmhyaEFcvfKbr8UKtqi8n5x8BL1+ySw9wwlClUe8aX6Aa17VMoLUg3OMxJXuagBet3/K8tP0jrfunACCmeCI3aBKOVF5Xf4H6wcjJf/ph0nGQOKOrkXFnzr4jf3Rsun3zDEASA98aqvS43/qVDMWVXJzBHzQiUG9AWckcBNN7IIQA/KKr0gPOHDmZGbDHin41lFaliUCgaGJ+l8DyN6Vg1rIx9xfWOpoyGEaBBT1njEf0FSAlexHAE8yco8ziFv2tO+YCgaxBWZk7CDaLX8gogHhQVzvfbxQGJAZ1Nbyyrn/sxv2FQn6ayLb0M4YqHa4Ws0LUjUJ5M+OFKX5o5jWAkta7yW2PTmod9k2rmaHV9NaXqbpg5HQI256+BuBOAFd0VdoZlPGqdagiU6BnnZ6l9NWrAraPt/yYnE2jiX+fEVS4YNceFiOGFj61JiC3phiYIebdThWuJzMuMdtGyozFxTy2Tp2Q/lozkA2VyMXA5vdrgXEbKYPThtrZX+uCNGSujWTG2thtpIDYq6vhz1sG5Cl6YPBpQ+18KWgDx0i9XWFTR1YhYCdajTrlNlIwlXWFTQFFlVxcgM/bjblVgYneIebDtYqny0grusKmgOSRTBdupWkAXQz8vWCa2zpEW19QRXcbqV9X2BSQtTiWzB0l5jeLtYROGVpkJKiilxmpT1fYNNCTqa/aehYf/g6EnWAsLQM7PtSkn6tBySUjBeDbFTYNZGdJmdtDML+w3gliIK2Gz7mL56qmzGPM4tWSkfp2hS0BsoJElewhAt9DC1lNP9m74Ot9rt2qdYUtAwquOS5DLvbMVbvCdQHyHh+DnzfUTrtDbORpyDrqCVzsPAu9uhaedP5iqWedM6flQI1s7jd3wwH9BwlQXENyPtO3AAAAAElFTkSuQmCC",alt:""}))],2)])):(o(),B("div",{key:1,class:"uni-input"},[w("div",{class:"uni-record-in"},[w("div",{class:"record-btn"},"按住说话")])]))]),w("div",{class:d(["tabs",1===t.tab||2===t.tab||3===t.tab||6===t.tab?"tabScorll":""])},[(o(!0),B(Q,null,O(c.value,(e=>(o(),B("div",{class:"btn",style:r(e.disabled?{pointerEvents:"none"}:""),key:e.templateId,onClick:t=>(t=>{if("1"===t.type&&"1"===t.templateId){if("/pages/consult/index"===s.path)return void n("chat",{type:"zhfz"});x({url:"/pages/consult/index?type=zhfz"})}else if("1"===t.type&&"2"===t.templateId){if("/pages/consult/report"===s.path)return void n("chat",{type:"bgjd"});x({url:"/pages/consult/report?type=bgjd"})}else if("6"===t.type&&"3"===t.templateId){if("/pages/consult/medication"===s.path)return void n("chat",{type:"znwy"});x({url:"/pages/consult/medication?type=znwy"})}else if("2"===t.type)window.location.href=t.url;else if("3"===t.type)n("chat",{type:"question",content:t.name});else if("6"===t.type&&"7"===t.templateId){if("/pages/consult/info"===s.path)return void n("chat",{type:"xxzx"});P({url:"/pages/consult/info?type=xxzx"})}})(e)},["1"===e.ico?(o(),B("img",{key:0,class:d(1===t.tab?"":"gray"),src:"data:image/png;base64,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",alt:""},null,2)):p("",!0),"2"===e.ico?(o(),B("img",{key:1,class:d(2===t.tab?"":"gray"),src:"data:image/png;base64,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",alt:""},null,2)):p("",!0),"6"===e.ico?(o(),B("img",{key:2,class:d(6===t.tab?"":"gray"),src:"data:image/png;base64,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",alt:""},null,2)):p("",!0),"3"===e.ico?(o(),B("img",{key:3,class:d(3===t.tab?"":"gray"),src:"data:image/png;base64,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",alt:""},null,2)):p("",!0),w("span",{class:d(1==t.tab&&"智慧分诊"==e.name||2==t.tab&&"报告解读"==e.name||3==t.tab&&"智能问药"==e.name||6==t.tab&&"信息咨询"==e.name?"":"gray")},C(e.name),3)],12,["onClick"])))),128))],2)])),_:1},8,["style"]),f(z,{ref_key:"toast",ref:k},null,512)],64)}}},[["__scopeId","data-v-2472f7d2"]]),tt=E(X({__name:"popup",setup(t){const e=z(!1),a=()=>{V(R,!0),e.value=!1};return U((()=>{D()||(e.value=!0)})),(t,i)=>{const s=A;return e.value?(o(),l(s,{key:0,class:"agreement-content"},{default:u((()=>[w("div",{class:"wrap"},[w("div",{class:"bg"},[w("div",{class:"title"}," 用户协议"),w("div",{class:"content"},"您一旦使用本服务，即表示同意我们在本次自助健康咨询服务中，获取并使用您的就诊信息，专为提升本次自助健康咨询体验。我们郑重承诺，您的数据将严格保密，仅服务于本次咨询。 "),w("div",{class:"content"},"健康路上，我们携手前行，让每一份信任都转化为安心与便捷！"),w("div",{class:"btns"},[w("div",{class:"agree",onClick:a},"同意"),w("div",{class:"close",onClick:a},"取消")])])])])),_:1})):p("",!0)}}}),[["__scopeId","data-v-95187e41"]]);export{$ as C,tt as P,L as Q,Z as _,j as a,G as b,_ as u};

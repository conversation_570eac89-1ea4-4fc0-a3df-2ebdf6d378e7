import{a as s,_ as a}from"./uv-icon.lsvsjwja.js";import{d as t,r as e,o as c,j as i,k as r,a as o,c as n,w as l,b as d,f as p,F as m,h as u,t as g,l as v,i as _}from"./index-BKmNCtIL.js";import{a as f}from"./report.B1eHB6fc.js";const I="/ywzz/assets/bgjd-icon1-C3F8mORn.png",h=a(t({__name:"report",props:{reportId:{type:String,default:""}},emits:["check"],setup(a,{emit:t}){const h=t,k=a,y=e({});return c((async()=>{if(k.reportId){const s=await f({admId:k.reportId});y.value=s.data.summaryItemList}})),(a,t)=>{const e=i(r("uv-icon"),s),c=_;return o(),n(c,{class:"suggest-content"},{default:l((()=>[d("div",{class:"suggest-content__list"},[d("div",{class:"header"},[d("img",{class:"consultation_icon",src:I,alt:""}),d("div",{class:"title"},"总检情况 ")]),d("div",{class:"text"},[(o(!0),p(m,null,u(y.value,((s,a)=>(o(),p("div",{class:"item",key:a},[d("span",{class:"count"},g(a+1),1),d("span",{class:"content",onClick:a=>h("check",`体检情况异常指标为${s.item}，该去哪个科室?`)},[d("span",null,g(s.item),1),d("div",{class:"right-arrow"},[v(e,{name:"arrow-right",color:"#557AF2",size:"12",style:{padding:"3rpx 0 0 3rpx"}})])],8,["onClick"])])))),128)),d("div",{class:"message"}," 为方便您的报告解读咨询，以上您的体检结果帮您提前列出来，您可以点击向AI提问，或自行输入需要提问的内容。 ")])])])),_:1})}}}),[["__scopeId","data-v-e2e991ac"]]);export{h as R,I as _};

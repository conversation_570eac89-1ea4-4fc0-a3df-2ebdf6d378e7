import{_ as s}from"./uv-button.BP6GkdBe.js";import{d as a,j as t,k as o,a as n,c as e,w as c,i,l as u,e as r,ag as d}from"./index-BKmNCtIL.js";import{_ as m}from"./uv-icon.lsvsjwja.js";import"./uv-loading-icon.g3FCLMWS.js";const p=m(a({__name:"index",setup(a){const m=()=>{d({account:"lisi",id:"2",timestamp:"**********"})};return(a,d)=>{const p=t(o("u-button"),s),l=i;return n(),e(l,{class:"content"},{default:c((()=>[u(p,{type:"success",onClick:m},{default:c((()=>[r("登录")])),_:1})])),_:1})}}}),[["__scopeId","data-v-********"]]);export{p as default};

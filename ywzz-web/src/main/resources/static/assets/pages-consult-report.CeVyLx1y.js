var e,r;import{H as a,I as t,r as n,j as s,k as o,a as i,f as l,l as c,w as u,c as d,e as p,t as h,m,b as g,J as _,K as f,G as b,F as w,h as k,u as v,L as y,M as x,N as P,O as j,i as C,x as A,Q as z,R as S,S as I,T as L,p as M,q as E,U as T,V as B,W as R,X as F,Y as H,Z as q,s as O,v as U,_ as N,$ as V,d as D,a0 as $,o as G,n as W,B as K,A as Z,a1 as X,z as Y}from"./index-BKmNCtIL.js";import{C as Q,P as J,_ as ee,Q as re}from"./popup.CqrlupOr.js";import{_ as ae,R as te}from"./report.DGmwSo_4.js";import{_ as ne,a as se}from"./uv-icon.lsvsjwja.js";import{c as oe,g as ie,a as le,_ as ce,b as ue,d as de,e as pe,f as he,R as me}from"./responseLlm.CCC7rJk1.js";import{_ as ge}from"./uv-button.BP6GkdBe.js";import{R as _e}from"./responseRag.C2ij8iPb.js";import{_ as fe}from"./uv-loading-icon.g3FCLMWS.js";import{a as be,u as we,b as ke}from"./report.B1eHB6fc.js";var ve,ye={exports:{}};ve=ye,function(){function e(e){var r={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,describe:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,describe:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,describe:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,describe:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,describe:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",describe:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,describe:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,describe:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,describe:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,describe:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,describe:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},ellipsis:{defaultValue:!0,describe:"Replaces three dots with the ellipsis unicode character",type:"boolean"},completeHTMLDocument:{defaultValue:!1,describe:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,describe:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,describe:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(r));var a={};for(var t in r)r.hasOwnProperty(t)&&(a[t]=r[t].defaultValue);return a}var r={},a={},t={},n=e(!0),s="vanilla",o={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:e(!0),allOn:function(){var r=e(!0),a={};for(var t in r)r.hasOwnProperty(t)&&(a[t]=!0);return a}()};function i(e,a){var t=a?"Error in "+a+" extension->":"Error in unnamed extension",n={valid:!0,error:""};r.helper.isArray(e)||(e=[e]);for(var s=0;s<e.length;++s){var o=t+" sub-extension "+s+": ",i=e[s];if("object"!=typeof i)return n.valid=!1,n.error=o+"must be an object, but "+typeof i+" given",n;if(!r.helper.isString(i.type))return n.valid=!1,n.error=o+'property "type" must be a string, but '+typeof i.type+" given",n;var l=i.type=i.type.toLowerCase();if("language"===l&&(l=i.type="lang"),"html"===l&&(l=i.type="output"),"lang"!==l&&"output"!==l&&"listener"!==l)return n.valid=!1,n.error=o+"type "+l+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',n;if("listener"===l){if(r.helper.isUndefined(i.listeners))return n.valid=!1,n.error=o+'. Extensions of type "listener" must have a property called "listeners"',n}else if(r.helper.isUndefined(i.filter)&&r.helper.isUndefined(i.regex))return n.valid=!1,n.error=o+l+' extensions must define either a "regex" property or a "filter" method',n;if(i.listeners){if("object"!=typeof i.listeners)return n.valid=!1,n.error=o+'"listeners" property must be an object but '+typeof i.listeners+" given",n;for(var c in i.listeners)if(i.listeners.hasOwnProperty(c)&&"function"!=typeof i.listeners[c])return n.valid=!1,n.error=o+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+c+" must be a function but "+typeof i.listeners[c]+" given",n}if(i.filter){if("function"!=typeof i.filter)return n.valid=!1,n.error=o+'"filter" must be a function, but '+typeof i.filter+" given",n}else if(i.regex){if(r.helper.isString(i.regex)&&(i.regex=new RegExp(i.regex,"g")),!(i.regex instanceof RegExp))return n.valid=!1,n.error=o+'"regex" property must either be a string or a RegExp object, but '+typeof i.regex+" given",n;if(r.helper.isUndefined(i.replace))return n.valid=!1,n.error=o+'"regex" extensions must implement a replace string or function',n}}return n}function l(e,r){return"¨E"+r.charCodeAt(0)+"E"}r.helper={},r.extensions={},r.setOption=function(e,r){return n[e]=r,this},r.getOption=function(e){return n[e]},r.getOptions=function(){return n},r.resetOptions=function(){n=e(!0)},r.setFlavor=function(e){if(!o.hasOwnProperty(e))throw Error(e+" flavor was not found");r.resetOptions();var a=o[e];for(var t in s=e,a)a.hasOwnProperty(t)&&(n[t]=a[t])},r.getFlavor=function(){return s},r.getFlavorOptions=function(e){if(o.hasOwnProperty(e))return o[e]},r.getDefaultOptions=function(r){return e(r)},r.subParser=function(e,t){if(r.helper.isString(e)){if(void 0===t){if(a.hasOwnProperty(e))return a[e];throw Error("SubParser named "+e+" not registered!")}a[e]=t}},r.extension=function(e,a){if(!r.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=r.helper.stdExtName(e),r.helper.isUndefined(a)){if(!t.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return t[e]}"function"==typeof a&&(a=a()),r.helper.isArray(a)||(a=[a]);var n=i(a,e);if(!n.valid)throw Error(n.error);t[e]=a},r.getAllExtensions=function(){return t},r.removeExtension=function(e){delete t[e]},r.resetExtensions=function(){t={}},r.validateExtension=function(e){return!!i(e,null).valid},r.hasOwnProperty("helper")||(r.helper={}),r.helper.isString=function(e){return"string"==typeof e||e instanceof String},r.helper.isFunction=function(e){return e&&"[object Function]"==={}.toString.call(e)},r.helper.isArray=function(e){return Array.isArray(e)},r.helper.isUndefined=function(e){return void 0===e},r.helper.forEach=function(e,a){if(r.helper.isUndefined(e))throw new Error("obj param is required");if(r.helper.isUndefined(a))throw new Error("callback param is required");if(!r.helper.isFunction(a))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(a);else if(r.helper.isArray(e))for(var t=0;t<e.length;t++)a(e[t],t,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var n in e)e.hasOwnProperty(n)&&a(e[n],n,e)}},r.helper.stdExtName=function(e){return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},r.helper.escapeCharactersCallback=l,r.helper.escapeCharacters=function(e,r,a){var t="(["+r.replace(/([\[\]\\])/g,"\\$1")+"])";a&&(t="\\\\"+t);var n=new RegExp(t,"g");return e=e.replace(n,l)},r.helper.unescapeHTMLEntities=function(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var c=function(e,r,a,t){var n,s,o,i,l,c=t||"",u=c.indexOf("g")>-1,d=new RegExp(r+"|"+a,"g"+c.replace(/g/g,"")),p=new RegExp(r,c.replace(/g/g,"")),h=[];do{for(n=0;o=d.exec(e);)if(p.test(o[0]))n++||(i=(s=d.lastIndex)-o[0].length);else if(n&&! --n){l=o.index+o[0].length;var m={left:{start:i,end:s},match:{start:s,end:o.index},right:{start:o.index,end:l},wholeMatch:{start:i,end:l}};if(h.push(m),!u)return h}}while(n&&(d.lastIndex=s));return h};r.helper.matchRecursiveRegExp=function(e,r,a,t){for(var n=c(e,r,a,t),s=[],o=0;o<n.length;++o)s.push([e.slice(n[o].wholeMatch.start,n[o].wholeMatch.end),e.slice(n[o].match.start,n[o].match.end),e.slice(n[o].left.start,n[o].left.end),e.slice(n[o].right.start,n[o].right.end)]);return s},r.helper.replaceRecursiveRegExp=function(e,a,t,n,s){if(!r.helper.isFunction(a)){var o=a;a=function(){return o}}var i=c(e,t,n,s),l=e,u=i.length;if(u>0){var d=[];0!==i[0].wholeMatch.start&&d.push(e.slice(0,i[0].wholeMatch.start));for(var p=0;p<u;++p)d.push(a(e.slice(i[p].wholeMatch.start,i[p].wholeMatch.end),e.slice(i[p].match.start,i[p].match.end),e.slice(i[p].left.start,i[p].left.end),e.slice(i[p].right.start,i[p].right.end))),p<u-1&&d.push(e.slice(i[p].wholeMatch.end,i[p+1].wholeMatch.start));i[u-1].wholeMatch.end<e.length&&d.push(e.slice(i[u-1].wholeMatch.end)),l=d.join("")}return l},r.helper.regexIndexOf=function(e,a,t){if(!r.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(a instanceof RegExp==0)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var n=e.substring(t||0).search(a);return n>=0?n+(t||0):n},r.helper.splitAtIndex=function(e,a){if(!r.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,a),e.substring(a)]},r.helper.encodeEmailAddress=function(e){var r=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,(function(e){if("@"===e)e=r[Math.floor(2*Math.random())](e);else{var a=Math.random();e=a>.9?r[2](e):a>.45?r[1](e):r[0](e)}return e}))},r.helper.padEnd=function(e,r,a){return r|=0,a=String(a||" "),e.length>r?String(e):((r-=e.length)>a.length&&(a+=a.repeat(r/a.length)),String(e)+a.slice(0,r))},"undefined"==typeof console&&(console={warn:function(e){alert(e)},log:function(e){alert(e)},error:function(e){throw e}}),r.helper.regexes={asteriskDashAndColon:/([*_:~])/g},r.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},r.Converter=function(e){var a={},l=[],c=[],u={},d=s,p={parsed:{},raw:"",format:""};function h(e,a){if(a=a||null,r.helper.isString(e)){if(a=e=r.helper.stdExtName(e),r.extensions[e])return void function(e,a){"function"==typeof e&&(e=e(new r.Converter)),r.helper.isArray(e)||(e=[e]);var t=i(e,a);if(!t.valid)throw Error(t.error);for(var n=0;n<e.length;++n)switch(e[n].type){case"lang":l.push(e[n]);break;case"output":c.push(e[n]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(r.extensions[e],e);if(r.helper.isUndefined(t[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=t[e]}"function"==typeof e&&(e=e()),r.helper.isArray(e)||(e=[e]);var n=i(e,a);if(!n.valid)throw Error(n.error);for(var s=0;s<e.length;++s){switch(e[s].type){case"lang":l.push(e[s]);break;case"output":c.push(e[s])}if(e[s].hasOwnProperty("listeners"))for(var o in e[s].listeners)e[s].listeners.hasOwnProperty(o)&&m(o,e[s].listeners[o])}}function m(e,a){if(!r.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof a)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof a+" given");u.hasOwnProperty(e)||(u[e]=[]),u[e].push(a)}!function(){for(var t in e=e||{},n)n.hasOwnProperty(t)&&(a[t]=n[t]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var s in e)e.hasOwnProperty(s)&&(a[s]=e[s]);a.extensions&&r.helper.forEach(a.extensions,h)}(),this._dispatch=function(e,r,a,t){if(u.hasOwnProperty(e))for(var n=0;n<u[e].length;++n){var s=u[e][n](e,r,this,a,t);s&&void 0!==s&&(r=s)}return r},this.listen=function(e,r){return m(e,r),this},this.makeHtml=function(e){if(!e)return e;var t={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:l,outputModifiers:c,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),a.smartIndentationFix&&(e=function(e){var r=e.match(/^\s*/)[0].length,a=new RegExp("^\\s{0,"+r+"}","gm");return e.replace(a,"")}(e)),e="\n\n"+e+"\n\n",e=(e=r.subParser("detab")(e,a,t)).replace(/^[ \t]+$/gm,""),r.helper.forEach(l,(function(n){e=r.subParser("runExtension")(n,e,a,t)})),e=r.subParser("metadata")(e,a,t),e=r.subParser("hashPreCodeTags")(e,a,t),e=r.subParser("githubCodeBlocks")(e,a,t),e=r.subParser("hashHTMLBlocks")(e,a,t),e=r.subParser("hashCodeTags")(e,a,t),e=r.subParser("stripLinkDefinitions")(e,a,t),e=r.subParser("blockGamut")(e,a,t),e=r.subParser("unhashHTMLSpans")(e,a,t),e=(e=(e=r.subParser("unescapeSpecialChars")(e,a,t)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=r.subParser("completeHTMLDocument")(e,a,t),r.helper.forEach(c,(function(n){e=r.subParser("runExtension")(n,e,a,t)})),p=t.metadata,e},this.makeMarkdown=this.makeMd=function(e,a){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!a){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");a=window.document}var t=a.createElement("div");t.innerHTML=e;var n={preList:function(e){for(var a=e.querySelectorAll("pre"),t=[],n=0;n<a.length;++n)if(1===a[n].childElementCount&&"code"===a[n].firstChild.tagName.toLowerCase()){var s=a[n].firstChild.innerHTML.trim(),o=a[n].firstChild.getAttribute("data-language")||"";if(""===o)for(var i=a[n].firstChild.className.split(" "),l=0;l<i.length;++l){var c=i[l].match(/^language-(.+)$/);if(null!==c){o=c[1];break}}s=r.helper.unescapeHTMLEntities(s),t.push(s),a[n].outerHTML='<precode language="'+o+'" precodenum="'+n.toString()+'"></precode>'}else t.push(a[n].innerHTML),a[n].innerHTML="",a[n].setAttribute("prenum",n.toString());return t}(t)};!function e(r){for(var a=0;a<r.childNodes.length;++a){var t=r.childNodes[a];3===t.nodeType?/\S/.test(t.nodeValue)||/^[ ]+$/.test(t.nodeValue)?(t.nodeValue=t.nodeValue.split("\n").join(" "),t.nodeValue=t.nodeValue.replace(/(\s)+/g,"$1")):(r.removeChild(t),--a):1===t.nodeType&&e(t)}}(t);for(var s=t.childNodes,o="",i=0;i<s.length;i++)o+=r.subParser("makeMarkdown.node")(s[i],n);return o},this.setOption=function(e,r){a[e]=r},this.getOption=function(e){return a[e]},this.getOptions=function(){return a},this.addExtension=function(e,r){h(e,r=r||null)},this.useExtension=function(e){h(e)},this.setFlavor=function(e){if(!o.hasOwnProperty(e))throw Error(e+" flavor was not found");var r=o[e];for(var t in d=e,r)r.hasOwnProperty(t)&&(a[t]=r[t])},this.getFlavor=function(){return d},this.removeExtension=function(e){r.helper.isArray(e)||(e=[e]);for(var a=0;a<e.length;++a){for(var t=e[a],n=0;n<l.length;++n)l[n]===t&&l.splice(n,1);for(var s=0;s<c.length;++s)c[s]===t&&c.splice(s,1)}},this.getAllExtensions=function(){return{language:l,output:c}},this.getMetadata=function(e){return e?p.raw:p.parsed},this.getMetadataFormat=function(){return p.format},this._setMetadataPair=function(e,r){p.parsed[e]=r},this._setMetadataFormat=function(e){p.format=e},this._setMetadataRaw=function(e){p.raw=e}},r.subParser("anchors",(function(e,a,t){var n=function(e,n,s,o,i,l,c){if(r.helper.isUndefined(c)&&(c=""),s=s.toLowerCase(),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)o="";else if(!o){if(s||(s=n.toLowerCase().replace(/ ?\n/g," ")),o="#"+s,r.helper.isUndefined(t.gUrls[s]))return e;o=t.gUrls[s],r.helper.isUndefined(t.gTitles[s])||(c=t.gTitles[s])}var u='<a href="'+(o=o.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback))+'"';return""!==c&&null!==c&&(u+=' title="'+(c=(c=c.replace(/"/g,"&quot;")).replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback))+'"'),a.openLinksInNewWindow&&!/^#/.test(o)&&(u+=' rel="noopener noreferrer" target="¨E95Eblank"'),u+=">"+n+"</a>"};return e=(e=(e=(e=(e=t.converter._dispatch("anchors.before",e,a,t)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[([^\[\]]+)]()()()()()/g,n),a.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,(function(e,t,n,s,o){if("\\"===n)return t+s;if(!r.helper.isString(a.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var i=a.ghMentionsLink.replace(/\{u}/g,o),l="";return a.openLinksInNewWindow&&(l=' rel="noopener noreferrer" target="¨E95Eblank"'),t+'<a href="'+i+'"'+l+">"+s+"</a>"}))),e=t.converter._dispatch("anchors.after",e,a,t)}));var u=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,d=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,p=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,h=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,m=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,g=function(e){return function(a,t,n,s,o,i,l){var c=n=n.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback),u="",d="",p=t||"",h=l||"";return/^www\./i.test(n)&&(n=n.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&i&&(u=i),e.openLinksInNewWindow&&(d=' rel="noopener noreferrer" target="¨E95Eblank"'),p+'<a href="'+n+'"'+d+">"+c+"</a>"+u+h}},_=function(e,a){return function(t,n,s){var o="mailto:";return n=n||"",s=r.subParser("unescapeSpecialChars")(s,e,a),e.encodeEmails?(o=r.helper.encodeEmailAddress(o+s),s=r.helper.encodeEmailAddress(s)):o+=s,n+'<a href="'+o+'">'+s+"</a>"}};r.subParser("autoLinks",(function(e,r,a){return e=(e=(e=a.converter._dispatch("autoLinks.before",e,r,a)).replace(p,g(r))).replace(m,_(r,a)),e=a.converter._dispatch("autoLinks.after",e,r,a)})),r.subParser("simplifiedAutoLinks",(function(e,r,a){return r.simplifiedAutoLink?(e=a.converter._dispatch("simplifiedAutoLinks.before",e,r,a),e=(e=r.excludeTrailingPunctuationFromURLs?e.replace(d,g(r)):e.replace(u,g(r))).replace(h,_(r,a)),e=a.converter._dispatch("simplifiedAutoLinks.after",e,r,a)):e})),r.subParser("blockGamut",(function(e,a,t){return e=t.converter._dispatch("blockGamut.before",e,a,t),e=r.subParser("blockQuotes")(e,a,t),e=r.subParser("headers")(e,a,t),e=r.subParser("horizontalRule")(e,a,t),e=r.subParser("lists")(e,a,t),e=r.subParser("codeBlocks")(e,a,t),e=r.subParser("tables")(e,a,t),e=r.subParser("hashHTMLBlocks")(e,a,t),e=r.subParser("paragraphs")(e,a,t),e=t.converter._dispatch("blockGamut.after",e,a,t)})),r.subParser("blockQuotes",(function(e,a,t){e=t.converter._dispatch("blockQuotes.before",e,a,t),e+="\n\n";var n=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return a.splitAdjacentBlockquotes&&(n=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(n,(function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=r.subParser("githubCodeBlocks")(e,a,t),e=(e=(e=r.subParser("blockGamut")(e,a,t)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,r){var a=r;return a=(a=a.replace(/^  /gm,"¨0")).replace(/¨0/g,"")})),r.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",a,t)})),e=t.converter._dispatch("blockQuotes.after",e,a,t)})),r.subParser("codeBlocks",(function(e,a,t){return e=t.converter._dispatch("codeBlocks.before",e,a,t),e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(e,n,s){var o=n,i=s,l="\n";return o=r.subParser("outdent")(o,a,t),o=r.subParser("encodeCode")(o,a,t),o=(o=(o=r.subParser("detab")(o,a,t)).replace(/^\n+/g,"")).replace(/\n+$/g,""),a.omitExtraWLInCodeBlocks&&(l=""),o="<pre><code>"+o+l+"</code></pre>",r.subParser("hashBlock")(o,a,t)+i}))).replace(/¨0/,""),e=t.converter._dispatch("codeBlocks.after",e,a,t)})),r.subParser("codeSpans",(function(e,a,t){return void 0===(e=t.converter._dispatch("codeSpans.before",e,a,t))&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,n,s,o){var i=o;return i=(i=i.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),i=n+"<code>"+(i=r.subParser("encodeCode")(i,a,t))+"</code>",i=r.subParser("hashHTMLSpans")(i,a,t)})),e=t.converter._dispatch("codeSpans.after",e,a,t)})),r.subParser("completeHTMLDocument",(function(e,r,a){if(!r.completeHTMLDocument)return e;e=a.converter._dispatch("completeHTMLDocument.before",e,r,a);var t="html",n="<!DOCTYPE HTML>\n",s="",o='<meta charset="utf-8">\n',i="",l="";for(var c in void 0!==a.metadata.parsed.doctype&&(n="<!DOCTYPE "+a.metadata.parsed.doctype+">\n","html"!==(t=a.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==t||(o='<meta charset="utf-8">')),a.metadata.parsed)if(a.metadata.parsed.hasOwnProperty(c))switch(c.toLowerCase()){case"doctype":break;case"title":s="<title>"+a.metadata.parsed.title+"</title>\n";break;case"charset":o="html"===t||"html5"===t?'<meta charset="'+a.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+a.metadata.parsed.charset+'">\n';break;case"language":case"lang":i=' lang="'+a.metadata.parsed[c]+'"',l+='<meta name="'+c+'" content="'+a.metadata.parsed[c]+'">\n';break;default:l+='<meta name="'+c+'" content="'+a.metadata.parsed[c]+'">\n'}return e=n+"<html"+i+">\n<head>\n"+s+o+l+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=a.converter._dispatch("completeHTMLDocument.after",e,r,a)})),r.subParser("detab",(function(e,r,a){return e=(e=(e=(e=(e=(e=a.converter._dispatch("detab.before",e,r,a)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(e,r){for(var a=r,t=4-a.length%4,n=0;n<t;n++)a+=" ";return a}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),e=a.converter._dispatch("detab.after",e,r,a)})),r.subParser("ellipsis",(function(e,r,a){return r.ellipsis?(e=(e=a.converter._dispatch("ellipsis.before",e,r,a)).replace(/\.\.\./g,"…"),e=a.converter._dispatch("ellipsis.after",e,r,a)):e})),r.subParser("emoji",(function(e,a,t){return a.emoji?(e=(e=t.converter._dispatch("emoji.before",e,a,t)).replace(/:([\S]+?):/g,(function(e,a){return r.helper.emojis.hasOwnProperty(a)?r.helper.emojis[a]:e})),e=t.converter._dispatch("emoji.after",e,a,t)):e})),r.subParser("encodeAmpsAndAngles",(function(e,r,a){return e=(e=(e=(e=(e=a.converter._dispatch("encodeAmpsAndAngles.before",e,r,a)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=a.converter._dispatch("encodeAmpsAndAngles.after",e,r,a)})),r.subParser("encodeBackslashEscapes",(function(e,a,t){return e=(e=(e=t.converter._dispatch("encodeBackslashEscapes.before",e,a,t)).replace(/\\(\\)/g,r.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,r.helper.escapeCharactersCallback),e=t.converter._dispatch("encodeBackslashEscapes.after",e,a,t)})),r.subParser("encodeCode",(function(e,a,t){return e=(e=t.converter._dispatch("encodeCode.before",e,a,t)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,r.helper.escapeCharactersCallback),e=t.converter._dispatch("encodeCode.after",e,a,t)})),r.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,a,t){return e=(e=(e=t.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,a,t)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,r.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(e){return e.replace(/([\\`*_~=|])/g,r.helper.escapeCharactersCallback)})),e=t.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,a,t)})),r.subParser("githubCodeBlocks",(function(e,a,t){return a.ghCodeBlocks?(e=t.converter._dispatch("githubCodeBlocks.before",e,a,t),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(e,n,s,o){var i=a.omitExtraWLInCodeBlocks?"":"\n";return o=r.subParser("encodeCode")(o,a,t),o="<pre><code"+(s?' class="'+s+" language-"+s+'"':"")+">"+(o=(o=(o=r.subParser("detab")(o,a,t)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+i+"</code></pre>",o=r.subParser("hashBlock")(o,a,t),"\n\n¨G"+(t.ghCodeBlocks.push({text:e,codeblock:o})-1)+"G\n\n"}))).replace(/¨0/,""),t.converter._dispatch("githubCodeBlocks.after",e,a,t)):e})),r.subParser("hashBlock",(function(e,r,a){return e=(e=a.converter._dispatch("hashBlock.before",e,r,a)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(a.gHtmlBlocks.push(e)-1)+"K\n\n",e=a.converter._dispatch("hashBlock.after",e,r,a)})),r.subParser("hashCodeTags",(function(e,a,t){return e=t.converter._dispatch("hashCodeTags.before",e,a,t),e=r.helper.replaceRecursiveRegExp(e,(function(e,n,s,o){var i=s+r.subParser("encodeCode")(n,a,t)+o;return"¨C"+(t.gHtmlSpans.push(i)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),e=t.converter._dispatch("hashCodeTags.after",e,a,t)})),r.subParser("hashElement",(function(e,r,a){return function(e,r){var t=r;return t=(t=(t=t.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),t="\n\n¨K"+(a.gHtmlBlocks.push(t)-1)+"K\n\n"}})),r.subParser("hashHTMLBlocks",(function(e,a,t){e=t.converter._dispatch("hashHTMLBlocks.before",e,a,t);var n=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],s=function(e,r,a,n){var s=e;return-1!==a.search(/\bmarkdown\b/)&&(s=a+t.converter.makeHtml(r)+n),"\n\n¨K"+(t.gHtmlBlocks.push(s)-1)+"K\n\n"};a.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,(function(e,r){return"&lt;"+r+"&gt;"})));for(var o=0;o<n.length;++o)for(var i,l=new RegExp("^ {0,3}(<"+n[o]+"\\b[^>]*>)","im"),c="<"+n[o]+"\\b[^>]*>",u="</"+n[o]+">";-1!==(i=r.helper.regexIndexOf(e,l));){var d=r.helper.splitAtIndex(e,i),p=r.helper.replaceRecursiveRegExp(d[1],s,c,u,"im");if(p===d[1])break;e=d[0].concat(p)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,r.subParser("hashElement")(e,a,t)),e=(e=r.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(t.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,r.subParser("hashElement")(e,a,t)),e=t.converter._dispatch("hashHTMLBlocks.after",e,a,t)})),r.subParser("hashHTMLSpans",(function(e,r,a){function t(e){return"¨C"+(a.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=a.converter._dispatch("hashHTMLSpans.before",e,r,a)).replace(/<[^>]+?\/>/gi,(function(e){return t(e)}))).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,(function(e){return t(e)}))).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,(function(e){return t(e)}))).replace(/<[^>]+?>/gi,(function(e){return t(e)})),e=a.converter._dispatch("hashHTMLSpans.after",e,r,a)})),r.subParser("unhashHTMLSpans",(function(e,r,a){e=a.converter._dispatch("unhashHTMLSpans.before",e,r,a);for(var t=0;t<a.gHtmlSpans.length;++t){for(var n=a.gHtmlSpans[t],s=0;/¨C(\d+)C/.test(n);){var o=RegExp.$1;if(n=n.replace("¨C"+o+"C",a.gHtmlSpans[o]),10===s)break;++s}e=e.replace("¨C"+t+"C",n)}return e=a.converter._dispatch("unhashHTMLSpans.after",e,r,a)})),r.subParser("hashPreCodeTags",(function(e,a,t){return e=t.converter._dispatch("hashPreCodeTags.before",e,a,t),e=r.helper.replaceRecursiveRegExp(e,(function(e,n,s,o){var i=s+r.subParser("encodeCode")(n,a,t)+o;return"\n\n¨G"+(t.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=t.converter._dispatch("hashPreCodeTags.after",e,a,t)})),r.subParser("headers",(function(e,a,t){e=t.converter._dispatch("headers.before",e,a,t);var n=isNaN(parseInt(a.headerLevelStart))?1:parseInt(a.headerLevelStart),s=a.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,o=a.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(s,(function(e,s){var o=r.subParser("spanGamut")(s,a,t),i=a.noHeaderId?"":' id="'+l(s)+'"',c="<h"+n+i+">"+o+"</h"+n+">";return r.subParser("hashBlock")(c,a,t)}))).replace(o,(function(e,s){var o=r.subParser("spanGamut")(s,a,t),i=a.noHeaderId?"":' id="'+l(s)+'"',c=n+1,u="<h"+c+i+">"+o+"</h"+c+">";return r.subParser("hashBlock")(u,a,t)}));var i=a.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function l(e){var n,s;if(a.customizedHeaderId){var o=e.match(/\{([^{]+?)}\s*$/);o&&o[1]&&(e=o[1])}return n=e,s=r.helper.isString(a.prefixHeaderId)?a.prefixHeaderId:!0===a.prefixHeaderId?"section-":"",a.rawPrefixHeaderId||(n=s+n),n=a.ghCompatibleHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():a.rawHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():n.replace(/[^\w]/g,"").toLowerCase(),a.rawPrefixHeaderId&&(n=s+n),t.hashLinkCounts[n]?n=n+"-"+t.hashLinkCounts[n]++:t.hashLinkCounts[n]=1,n}return e=e.replace(i,(function(e,s,o){var i=o;a.customizedHeaderId&&(i=o.replace(/\s?\{([^{]+?)}\s*$/,""));var c=r.subParser("spanGamut")(i,a,t),u=a.noHeaderId?"":' id="'+l(o)+'"',d=n-1+s.length,p="<h"+d+u+">"+c+"</h"+d+">";return r.subParser("hashBlock")(p,a,t)})),e=t.converter._dispatch("headers.after",e,a,t)})),r.subParser("horizontalRule",(function(e,a,t){e=t.converter._dispatch("horizontalRule.before",e,a,t);var n=r.subParser("hashBlock")("<hr />",a,t);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,n),e=t.converter._dispatch("horizontalRule.after",e,a,t)})),r.subParser("images",(function(e,a,t){function n(e,a,n,s,o,i,l,c){var u=t.gUrls,d=t.gTitles,p=t.gDimensions;if(n=n.toLowerCase(),c||(c=""),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)s="";else if(""===s||null===s){if(""!==n&&null!==n||(n=a.toLowerCase().replace(/ ?\n/g," ")),s="#"+n,r.helper.isUndefined(u[n]))return e;s=u[n],r.helper.isUndefined(d[n])||(c=d[n]),r.helper.isUndefined(p[n])||(o=p[n].width,i=p[n].height)}a=a.replace(/"/g,"&quot;").replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback);var h='<img src="'+(s=s.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback))+'" alt="'+a+'"';return c&&r.helper.isString(c)&&(h+=' title="'+(c=c.replace(/"/g,"&quot;").replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback))+'"'),o&&i&&(h+=' width="'+(o="*"===o?"auto":o)+'"',h+=' height="'+(i="*"===i?"auto":i)+'"'),h+=" />"}return e=(e=(e=(e=(e=(e=t.converter._dispatch("images.before",e,a,t)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(e,r,a,t,s,o,i,l){return n(e,r,a,t=t.replace(/\s/g,""),s,o,0,l)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,n)).replace(/!\[([^\[\]]+)]()()()()()/g,n),e=t.converter._dispatch("images.after",e,a,t)})),r.subParser("italicsAndBold",(function(e,r,a){function t(e,r,a){return r+e+a}return e=a.converter._dispatch("italicsAndBold.before",e,r,a),e=r.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,r){return t(r,"<strong><em>","</em></strong>")}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,r){return t(r,"<strong>","</strong>")}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,r){return t(r,"<em>","</em>")})):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,r){return/\S$/.test(r)?t(r,"<strong><em>","</em></strong>"):e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,r){return/\S$/.test(r)?t(r,"<strong>","</strong>"):e}))).replace(/_([^\s_][\s\S]*?)_/g,(function(e,r){return/\S$/.test(r)?t(r,"<em>","</em>"):e})),e=r.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,(function(e,r,a){return t(a,r+"<strong><em>","</em></strong>")}))).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,(function(e,r,a){return t(a,r+"<strong>","</strong>")}))).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,(function(e,r,a){return t(a,r+"<em>","</em>")})):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,r){return/\S$/.test(r)?t(r,"<strong><em>","</em></strong>"):e}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,r){return/\S$/.test(r)?t(r,"<strong>","</strong>"):e}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,r){return/\S$/.test(r)?t(r,"<em>","</em>"):e})),e=a.converter._dispatch("italicsAndBold.after",e,r,a)})),r.subParser("lists",(function(e,a,t){function n(e,n){t.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var s=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,o=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return a.disableForced4SpacesIndentedSublists&&(s=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(s,(function(e,n,s,i,l,c,u){u=u&&""!==u.trim();var d=r.subParser("outdent")(l,a,t),p="";return c&&a.tasklists&&(p=' class="task-list-item" style="list-style-type: none;"',d=d.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return u&&(e+=" checked"),e+=">"}))),d=d.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),n||d.search(/\n{2,}/)>-1?(d=r.subParser("githubCodeBlocks")(d,a,t),d=r.subParser("blockGamut")(d,a,t)):(d=(d=r.subParser("lists")(d,a,t)).replace(/\n$/,""),d=(d=r.subParser("hashHTMLBlocks")(d,a,t)).replace(/\n\n+/g,"\n\n"),d=o?r.subParser("paragraphs")(d,a,t):r.subParser("spanGamut")(d,a,t)),d="<li"+p+">"+(d=d.replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),t.gListLevel--,n&&(e=e.replace(/\s+$/,"")),e}function s(e,r){if("ol"===r){var a=e.match(/^ *(\d+)\./);if(a&&"1"!==a[1])return' start="'+a[1]+'"'}return""}function o(e,r,t){var o=a.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,i=a.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,l="ul"===r?o:i,c="";if(-1!==e.search(l))!function a(u){var d=u.search(l),p=s(e,r);-1!==d?(c+="\n\n<"+r+p+">\n"+n(u.slice(0,d),!!t)+"</"+r+">\n",l="ul"==(r="ul"===r?"ol":"ul")?o:i,a(u.slice(d))):c+="\n\n<"+r+p+">\n"+n(u,!!t)+"</"+r+">\n"}(e);else{var u=s(e,r);c="\n\n<"+r+u+">\n"+n(e,!!t)+"</"+r+">\n"}return c}return e=t.converter._dispatch("lists.before",e,a,t),e+="¨0",e=(e=t.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,r,a){return o(r,a.search(/[*+-]/g)>-1?"ul":"ol",!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,r,a,t){return o(a,t.search(/[*+-]/g)>-1?"ul":"ol",!1)}))).replace(/¨0/,""),e=t.converter._dispatch("lists.after",e,a,t)})),r.subParser("metadata",(function(e,r,a){if(!r.metadata)return e;function t(e){a.metadata.raw=e,(e=(e=e.replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(e,r,t){return a.metadata.parsed[r]=t,""}))}return e=(e=(e=(e=a.converter._dispatch("metadata.before",e,r,a)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(e,r,a){return t(a),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(e,r,n){return r&&(a.metadata.format=r),t(n),"¨M"}))).replace(/¨M/g,""),e=a.converter._dispatch("metadata.after",e,r,a)})),r.subParser("outdent",(function(e,r,a){return e=(e=(e=a.converter._dispatch("outdent.before",e,r,a)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),e=a.converter._dispatch("outdent.after",e,r,a)})),r.subParser("paragraphs",(function(e,a,t){for(var n=(e=(e=(e=t.converter._dispatch("paragraphs.before",e,a,t)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),s=[],o=n.length,i=0;i<o;i++){var l=n[i];l.search(/¨(K|G)(\d+)\1/g)>=0?s.push(l):l.search(/\S/)>=0&&(l=(l=r.subParser("spanGamut")(l,a,t)).replace(/^([ \t]*)/g,"<p>"),l+="</p>",s.push(l))}for(o=s.length,i=0;i<o;i++){for(var c="",u=s[i],d=!1;/¨(K|G)(\d+)\1/.test(u);){var p=RegExp.$1,h=RegExp.$2;c=(c="K"===p?t.gHtmlBlocks[h]:d?r.subParser("encodeCode")(t.ghCodeBlocks[h].text,a,t):t.ghCodeBlocks[h].codeblock).replace(/\$/g,"$$$$"),u=u.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,c),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(u)&&(d=!0)}s[i]=u}return e=(e=(e=s.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),t.converter._dispatch("paragraphs.after",e,a,t)})),r.subParser("runExtension",(function(e,r,a,t){if(e.filter)r=e.filter(r,t.converter,a);else if(e.regex){var n=e.regex;n instanceof RegExp||(n=new RegExp(n,"g")),r=r.replace(n,e.replace)}return r})),r.subParser("spanGamut",(function(e,a,t){return e=t.converter._dispatch("spanGamut.before",e,a,t),e=r.subParser("codeSpans")(e,a,t),e=r.subParser("escapeSpecialCharsWithinTagAttributes")(e,a,t),e=r.subParser("encodeBackslashEscapes")(e,a,t),e=r.subParser("images")(e,a,t),e=r.subParser("anchors")(e,a,t),e=r.subParser("autoLinks")(e,a,t),e=r.subParser("simplifiedAutoLinks")(e,a,t),e=r.subParser("emoji")(e,a,t),e=r.subParser("underline")(e,a,t),e=r.subParser("italicsAndBold")(e,a,t),e=r.subParser("strikethrough")(e,a,t),e=r.subParser("ellipsis")(e,a,t),e=r.subParser("hashHTMLSpans")(e,a,t),e=r.subParser("encodeAmpsAndAngles")(e,a,t),a.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=t.converter._dispatch("spanGamut.after",e,a,t)})),r.subParser("strikethrough",(function(e,a,t){return a.strikethrough&&(e=(e=t.converter._dispatch("strikethrough.before",e,a,t)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,n){return function(e){return a.simplifiedAutoLink&&(e=r.subParser("simplifiedAutoLinks")(e,a,t)),"<del>"+e+"</del>"}(n)})),e=t.converter._dispatch("strikethrough.after",e,a,t)),e})),r.subParser("stripLinkDefinitions",(function(e,a,t){var n=function(n,s,o,i,l,c,u){return s=s.toLowerCase(),e.toLowerCase().split(s).length-1<2?n:(o.match(/^data:.+?\/.+?;base64,/)?t.gUrls[s]=o.replace(/\s/g,""):t.gUrls[s]=r.subParser("encodeAmpsAndAngles")(o,a,t),c?c+u:(u&&(t.gTitles[s]=u.replace(/"|'/g,"&quot;")),a.parseImgDimensions&&i&&l&&(t.gDimensions[s]={width:i,height:l}),""))};return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,n)).replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,n)).replace(/¨0/,"")})),r.subParser("tables",(function(e,a,t){if(!a.tables)return e;function n(e,n){return"<td"+n+">"+r.subParser("spanGamut")(e,a,t)+"</td>\n"}function s(e){var s,o=e.split("\n");for(s=0;s<o.length;++s)/^ {0,3}\|/.test(o[s])&&(o[s]=o[s].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(o[s])&&(o[s]=o[s].replace(/\|[ \t]*$/,"")),o[s]=r.subParser("codeSpans")(o[s],a,t);var i,l,c,u,d=o[0].split("|").map((function(e){return e.trim()})),p=o[1].split("|").map((function(e){return e.trim()})),h=[],m=[],g=[],_=[];for(o.shift(),o.shift(),s=0;s<o.length;++s)""!==o[s].trim()&&h.push(o[s].split("|").map((function(e){return e.trim()})));if(d.length<p.length)return e;for(s=0;s<p.length;++s)g.push((i=p[s],/^:[ \t]*--*$/.test(i)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(i)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(i)?' style="text-align:center;"':""));for(s=0;s<d.length;++s)r.helper.isUndefined(g[s])&&(g[s]=""),m.push((l=d[s],c=g[s],u=void 0,u="",l=l.trim(),(a.tablesHeaderId||a.tableHeaderId)&&(u=' id="'+l.replace(/ /g,"_").toLowerCase()+'"'),"<th"+u+c+">"+(l=r.subParser("spanGamut")(l,a,t))+"</th>\n"));for(s=0;s<h.length;++s){for(var f=[],b=0;b<m.length;++b)r.helper.isUndefined(h[s][b]),f.push(n(h[s][b],g[b]));_.push(f)}return function(e,r){for(var a="<table>\n<thead>\n<tr>\n",t=e.length,n=0;n<t;++n)a+=e[n];for(a+="</tr>\n</thead>\n<tbody>\n",n=0;n<r.length;++n){a+="<tr>\n";for(var s=0;s<t;++s)a+=r[n][s];a+="</tr>\n"}return a+"</tbody>\n</table>\n"}(m,_)}return e=(e=(e=(e=t.converter._dispatch("tables.before",e,a,t)).replace(/\\(\|)/g,r.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,s)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,s),e=t.converter._dispatch("tables.after",e,a,t)})),r.subParser("underline",(function(e,a,t){return a.underline?(e=t.converter._dispatch("underline.before",e,a,t),e=(e=a.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,r){return"<u>"+r+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,r){return"<u>"+r+"</u>"})):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,r){return/\S$/.test(r)?"<u>"+r+"</u>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,r){return/\S$/.test(r)?"<u>"+r+"</u>":e}))).replace(/(_)/g,r.helper.escapeCharactersCallback),e=t.converter._dispatch("underline.after",e,a,t)):e})),r.subParser("unescapeSpecialChars",(function(e,r,a){return e=(e=a.converter._dispatch("unescapeSpecialChars.before",e,r,a)).replace(/¨E(\d+)E/g,(function(e,r){var a=parseInt(r);return String.fromCharCode(a)})),e=a.converter._dispatch("unescapeSpecialChars.after",e,r,a)})),r.subParser("makeMarkdown.blockquote",(function(e,a){var t="";if(e.hasChildNodes())for(var n=e.childNodes,s=n.length,o=0;o<s;++o){var i=r.subParser("makeMarkdown.node")(n[o],a);""!==i&&(t+=i)}return t="> "+(t=t.trim()).split("\n").join("\n> ")})),r.subParser("makeMarkdown.codeBlock",(function(e,r){var a=e.getAttribute("language"),t=e.getAttribute("precodenum");return"```"+a+"\n"+r.preList[t]+"\n```"})),r.subParser("makeMarkdown.codeSpan",(function(e){return"`"+e.innerHTML+"`"})),r.subParser("makeMarkdown.emphasis",(function(e,a){var t="";if(e.hasChildNodes()){t+="*";for(var n=e.childNodes,s=n.length,o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a);t+="*"}return t})),r.subParser("makeMarkdown.header",(function(e,a,t){var n=new Array(t+1).join("#"),s="";if(e.hasChildNodes()){s=n+" ";for(var o=e.childNodes,i=o.length,l=0;l<i;++l)s+=r.subParser("makeMarkdown.node")(o[l],a)}return s})),r.subParser("makeMarkdown.hr",(function(){return"---"})),r.subParser("makeMarkdown.image",(function(e){var r="";return e.hasAttribute("src")&&(r+="!["+e.getAttribute("alt")+"](",r+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(r+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"),r})),r.subParser("makeMarkdown.links",(function(e,a){var t="";if(e.hasChildNodes()&&e.hasAttribute("href")){var n=e.childNodes,s=n.length;t="[";for(var o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a);t+="](",t+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"}return t})),r.subParser("makeMarkdown.list",(function(e,a,t){var n="";if(!e.hasChildNodes())return"";for(var s=e.childNodes,o=s.length,i=e.getAttribute("start")||1,l=0;l<o;++l)void 0!==s[l].tagName&&"li"===s[l].tagName.toLowerCase()&&(n+=("ol"===t?i.toString()+". ":"- ")+r.subParser("makeMarkdown.listItem")(s[l],a),++i);return(n+="\n\x3c!-- --\x3e\n").trim()})),r.subParser("makeMarkdown.listItem",(function(e,a){for(var t="",n=e.childNodes,s=n.length,o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a);return/\n$/.test(t)?t=t.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):t+="\n",t})),r.subParser("makeMarkdown.node",(function(e,a,t){t=t||!1;var n="";if(3===e.nodeType)return r.subParser("makeMarkdown.txt")(e,a);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":t||(n=r.subParser("makeMarkdown.header")(e,a,1)+"\n\n");break;case"h2":t||(n=r.subParser("makeMarkdown.header")(e,a,2)+"\n\n");break;case"h3":t||(n=r.subParser("makeMarkdown.header")(e,a,3)+"\n\n");break;case"h4":t||(n=r.subParser("makeMarkdown.header")(e,a,4)+"\n\n");break;case"h5":t||(n=r.subParser("makeMarkdown.header")(e,a,5)+"\n\n");break;case"h6":t||(n=r.subParser("makeMarkdown.header")(e,a,6)+"\n\n");break;case"p":t||(n=r.subParser("makeMarkdown.paragraph")(e,a)+"\n\n");break;case"blockquote":t||(n=r.subParser("makeMarkdown.blockquote")(e,a)+"\n\n");break;case"hr":t||(n=r.subParser("makeMarkdown.hr")(e,a)+"\n\n");break;case"ol":t||(n=r.subParser("makeMarkdown.list")(e,a,"ol")+"\n\n");break;case"ul":t||(n=r.subParser("makeMarkdown.list")(e,a,"ul")+"\n\n");break;case"precode":t||(n=r.subParser("makeMarkdown.codeBlock")(e,a)+"\n\n");break;case"pre":t||(n=r.subParser("makeMarkdown.pre")(e,a)+"\n\n");break;case"table":t||(n=r.subParser("makeMarkdown.table")(e,a)+"\n\n");break;case"code":n=r.subParser("makeMarkdown.codeSpan")(e,a);break;case"em":case"i":n=r.subParser("makeMarkdown.emphasis")(e,a);break;case"strong":case"b":n=r.subParser("makeMarkdown.strong")(e,a);break;case"del":n=r.subParser("makeMarkdown.strikethrough")(e,a);break;case"a":n=r.subParser("makeMarkdown.links")(e,a);break;case"img":n=r.subParser("makeMarkdown.image")(e,a);break;default:n=e.outerHTML+"\n\n"}return n})),r.subParser("makeMarkdown.paragraph",(function(e,a){var t="";if(e.hasChildNodes())for(var n=e.childNodes,s=n.length,o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a);return t=t.trim()})),r.subParser("makeMarkdown.pre",(function(e,r){var a=e.getAttribute("prenum");return"<pre>"+r.preList[a]+"</pre>"})),r.subParser("makeMarkdown.strikethrough",(function(e,a){var t="";if(e.hasChildNodes()){t+="~~";for(var n=e.childNodes,s=n.length,o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a);t+="~~"}return t})),r.subParser("makeMarkdown.strong",(function(e,a){var t="";if(e.hasChildNodes()){t+="**";for(var n=e.childNodes,s=n.length,o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a);t+="**"}return t})),r.subParser("makeMarkdown.table",(function(e,a){var t,n,s="",o=[[],[]],i=e.querySelectorAll("thead>tr>th"),l=e.querySelectorAll("tbody>tr");for(t=0;t<i.length;++t){var c=r.subParser("makeMarkdown.tableCell")(i[t],a),u="---";if(i[t].hasAttribute("style"))switch(i[t].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":u=":---";break;case"text-align:right;":u="---:";break;case"text-align:center;":u=":---:"}o[0][t]=c.trim(),o[1][t]=u}for(t=0;t<l.length;++t){var d=o.push([])-1,p=l[t].getElementsByTagName("td");for(n=0;n<i.length;++n){var h=" ";void 0!==p[n]&&(h=r.subParser("makeMarkdown.tableCell")(p[n],a)),o[d].push(h)}}var m=3;for(t=0;t<o.length;++t)for(n=0;n<o[t].length;++n){var g=o[t][n].length;g>m&&(m=g)}for(t=0;t<o.length;++t){for(n=0;n<o[t].length;++n)1===t?":"===o[t][n].slice(-1)?o[t][n]=r.helper.padEnd(o[t][n].slice(-1),m-1,"-")+":":o[t][n]=r.helper.padEnd(o[t][n],m,"-"):o[t][n]=r.helper.padEnd(o[t][n],m);s+="| "+o[t].join(" | ")+" |\n"}return s.trim()})),r.subParser("makeMarkdown.tableCell",(function(e,a){var t="";if(!e.hasChildNodes())return"";for(var n=e.childNodes,s=n.length,o=0;o<s;++o)t+=r.subParser("makeMarkdown.node")(n[o],a,!0);return t.trim()})),r.subParser("makeMarkdown.txt",(function(e){var a=e.nodeValue;return a=(a=a.replace(/ +/g," ")).replace(/¨NBSP;/g," "),a=(a=(a=(a=(a=(a=(a=(a=(a=r.helper.unescapeHTMLEntities(a)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")})),ve.exports?ve.exports=r:this.showdown=r}.call(oe);const xe=ie(ye.exports),Pe=ne({__name:"responseReport",props:{item:{type:Object,default:()=>({content:"",thinkContent:"",message:"",thinkTime:""})},question:{type:Boolean,default:!1}},setup(e){var r;const z=a(),S=e,I=new xe.Converter,L=t((()=>(S.item.recordId&&(M.value=S.item.recordId),I.makeHtml(S.item.content)))),M=n(null==(r=S.item)?void 0:r.recordId),E=n(null);let T=n("");const B=n([]);n([]),B.value=z.feedbackOPS,B.value.map((e=>{e.isCheck=!1}));let R=n(!1),F=n(!1);function H(e){if(1==e){const r={recordId:M.value,optionType:1,recordScene:0,optionId:"",otherContent:""};F.value?x(M.value,r).then((r=>{R.value=e,F.value=!e})):P(r).then((r=>{R.value=e,F.value=!e}))}else j(M.value).then((r=>{R.value=e}))}function q(e){1==e?(B.value.map((e=>{e.isCheck=!1})),T.value="",E.value.open()):j(M.value).then((r=>{F.value=e}))}function O(){E.value.close()}function U(){const e=[];if(B.value.map((r=>{r.isCheck&&e.push(r.optionId)})),R.value){const r={optionType:0,optionId:e?e.join(","):"",otherContent:T.value};x(M.value,r).then((e=>{E.value.close(),F.value=!0,R.value=!1}))}else{const r={recordId:M.value,optionType:0,recordScene:0,optionId:e?e.join(","):"",otherContent:T.value};P(r).then((e=>{E.value.close(),F.value=!0,R.value=!1}))}}return(r,a)=>{const t=C,n=A,x=s(o("uv-icon"),se),P=s(o("uv-textarea"),le),j=s(o("uv-button"),ge),z=s(o("uv-popup"),ce);return i(),l(w,null,[c(t,{class:"response-content"},{default:u((()=>[e.item.thinkContent?(i(),d(t,{key:0,class:"think-title"},{default:u((()=>[p("已深度思考（用时"+h(e.item.thinkTime)+"秒）",1)])),_:1})):m("",!0),c(t,{class:"borderblock"},{default:u((()=>[c(t,{class:"left"}),e.item.thinkContent?(i(),d(t,{key:0,class:"think-content",innerHTML:e.item.thinkContent},null,8,["innerHTML"])):m("",!0)])),_:1}),c(t,null,{default:u((()=>[c(t,{style:{display:"flex"}},{default:u((()=>[c(t,{class:"pre"}),c(n,{class:"pretext"},{default:u((()=>[p("体检报告解读中...")])),_:1})])),_:1}),e.item.content?(i(),d(t,{key:0,innerHTML:L.value},null,8,["innerHTML"])):m("",!0)])),_:1})])),_:1}),e.question?m("",!0):(i(),l("div",{key:0,class:"message"},[p(" 以上回复由人工智能自动生成，仅供参考。 "),g("div",{class:"thumb_class"},[_(g("img",{class:"thumb-icon",src:ue,onClick:a[0]||(a[0]=e=>H(!0)),alt:""},null,512),[[f,!b(R)]]),_(g("img",{class:"thumb-icon-fill",src:de,onClick:a[1]||(a[1]=e=>H(!1)),alt:""},null,512),[[f,b(R)]]),g("div",{class:"line"}),_(g("img",{class:"thumb-icon",src:pe,onClick:a[2]||(a[2]=e=>q(!0)),alt:""},null,512),[[f,!b(F)]]),_(g("img",{class:"thumb-icon-fill",src:he,onClick:a[3]||(a[3]=e=>q(!1)),alt:""},null,512),[[f,b(F)]])])])),c(z,{ref_key:"popup",ref:E,mode:"bottom",onChange:r.change,closeOnClickOverlay:"false",bgColor:"none"},{default:u((()=>[c(t,{class:"feed-card"},{default:u((()=>[c(x,{name:"close",class:"close_icon",color:"#FFFFFF",size:"16",onClick:O}),c(t,{class:"text1"},{default:u((()=>[p("很抱歉回答未能让您满意")])),_:1}),c(t,{class:"text2"},{default:u((()=>[p("您的宝贵意见能帮我们优化产品，更好的为您服务。")])),_:1}),c(t,{class:"option-card"},{default:u((()=>[(i(!0),l(w,null,k(B.value,(e=>(i(),d(t,{class:v(e.isCheck?"sel_option":"option"),key:e.optionId,onClick:r=>{return a=e,void B.value.map((e=>{a.optionId==e.optionId&&(e.isCheck=!e.isCheck)}));var a}},{default:u((()=>[p(h(e.optionName),1)])),_:2},1032,["class","onClick"])))),128))])),_:1}),c(t,{class:"other-card"},{default:u((()=>[c(t,{class:"other_text"},{default:u((()=>[p("其他问题反馈")])),_:1}),c(P,{modelValue:b(T),"onUpdate:modelValue":a[4]||(a[4]=e=>y(T)?T.value=e:T=e),style:{margin:"15rpx"},count:"",maxlength:"500",placeholderStyle:{fontSize:"24rpx"},border:"none",customStyle:{background:"#EEF4F8",borderRadius:"12rpx",border:"2rpx solid #DEF1F1"},placeholder:" 没有想要的选项，您也可以在这里输入反馈内容。"},null,8,["modelValue"])])),_:1}),c(j,{text:"提交",onClick:U,style:{margin:"20rpx"},shape:"circle",color:"linear-gradient( 135deg, #557AF2 0%, #55CFFF 100%)"})])),_:1})])),_:1},8,["onChange"])],64)}}},[["__scopeId","data-v-31d0f577"]]);const je=ne({props:{src:{type:String,default:""},autoplay:{type:Boolean,default:!0}},data:()=>({videoSrc:"",show:!1}),computed:{getSec(){return this.src||this.videoSrc}},methods:{open(e){this.videoSrc=e,this.$refs.popup.open()},close(){this.$refs.popup.close()},change(e){this.show=e.show}}},[["render",function(e,r,a,t,n,l){const p=z,h=C,g=s(o("uv-popup"),ce);return i(),d(g,{ref:"popup",onChange:l.change},{default:u((()=>[n.show?(i(),d(h,{key:0,class:"video-view"},{default:u((()=>[c(p,{class:"video",src:l.getSec,autoplay:a.autoplay},null,8,["src","autoplay"])])),_:1})):m("",!0)])),_:1},8,["onChange"])}],["__scopeId","data-v-9cf65f78"]]);function Ce(e,r){return["[object Object]","[object File]"].includes(Object.prototype.toString.call(e))?Object.keys(e).reduce(((a,t)=>(r.includes(t)||(a[t]=e[t]),a)),{}):{}}function Ae(e){return e.tempFiles.map((e=>({...Ce(e,["path"]),url:e.path,size:e.size,name:e.name,type:e.type})))}function ze({accept:e,multiple:r,capture:a,compressed:t,maxDuration:n,sizeType:s,camera:o,maxCount:i}){return new Promise(((l,c)=>{switch(e){case"image":L({count:r?Math.min(i,9):1,sourceType:a,sizeType:s,success:e=>l(function(e){return e.tempFiles.map((e=>({...Ce(e,["path"]),type:"image",url:e.path,thumb:e.path,size:e.size,name:e.name})))}(e)),fail:c});break;case"video":I({sourceType:a,compressed:t,maxDuration:n,camera:o,success:e=>l(function(e){return[{...Ce(e,["tempFilePath","thumbTempFilePath","errMsg"]),type:"video",url:e.tempFilePath,thumb:e.thumbTempFilePath,size:e.size,name:e.name}]}(e)),fail:c});break;case"file":S({count:r?i:1,type:e,success:e=>l(Ae(e)),fail:c});break;default:S({count:r?i:1,type:"all",success:e=>l(Ae(e)),fail:c})}}))}const Se=ne({name:"uv-upload",emits:["error","beforeRead","oversize","afterRead","delete","clickPreview"],mixins:[M,E,{watch:{accept:{immediate:!0,handler(e){}}}},{props:{accept:{type:String,default:"image"},capture:{type:[String,Array],default:()=>["album","camera"]},compressed:{type:Boolean,default:!0},camera:{type:String,default:"back"},maxDuration:{type:Number,default:60},uploadIcon:{type:String,default:"camera-fill"},uploadIconColor:{type:String,default:"#D3D4D6"},useBeforeRead:{type:Boolean,default:!1},afterRead:{type:Function,default:null},beforeRead:{type:Function,default:null},previewFullImage:{type:Boolean,default:!0},previewFullVideo:{type:Boolean,default:!0},maxCount:{type:[String,Number],default:52},disabled:{type:Boolean,default:!1},imageMode:{type:String,default:"aspectFill"},name:{type:String,default:""},sizeType:{type:Array,default:()=>["original","compressed"]},multiple:{type:Boolean,default:!1},deletable:{type:Boolean,default:!0},maxSize:{type:[String,Number],default:Number.MAX_VALUE},fileList:{type:Array,default:()=>[]},uploadText:{type:String,default:""},width:{type:[String,Number],default:80},height:{type:[String,Number],default:80},previewImage:{type:Boolean,default:!0},...null==(r=null==(e=uni.$uv)?void 0:e.props)?void 0:r.upload}}],data:()=>({lists:[],isInCount:!0}),watch:{fileList:{deep:!0,immediate:!0,handler(){this.formatFileList()}},deletable(e){e||this.lists.map((e=>{e.deletable=this.deletable}))}},methods:{formatFileList(){const{fileList:e=[],maxCount:r}=this,a=e.map((e=>Object.assign(Object.assign({},e),{isImage:"image"===this.accept||T(e.url||e.thumb),isVideo:"video"===this.accept||B(e.url||e.thumb),deletable:"boolean"==typeof e.deletable?e.deletable:this.deletable})));this.lists=a,this.isInCount=a.length<r},chooseFile(){this.timer&&clearTimeout(this.timer),this.timer=setTimeout((()=>{const{maxCount:e,multiple:r,lists:a,disabled:t}=this;if(t)return;let n;try{n=R(this.capture)?this.capture:this.capture.split(",")}catch(s){n=[]}ze(Object.assign({accept:this.accept,multiple:this.multiple,capture:n,compressed:this.compressed,maxDuration:this.maxDuration,sizeType:this.sizeType,camera:this.camera},{maxCount:e-a.length})).then((e=>{this.onBeforeRead(r?e:e[0])})).catch((e=>{this.$emit("error",e)}))}),100)},onBeforeRead(e){const{beforeRead:r,useBeforeRead:a}=this;let t=!0;F(r)&&(t=r(e,this.getDetail())),a&&(t=new Promise(((r,a)=>{this.$emit("beforeRead",Object.assign(Object.assign({file:e},this.getDetail()),{callback:e=>{e?r():a()}}))}))),t&&(H(t)?t.then((r=>this.onAfterRead(r||e))):this.onAfterRead(e))},getDetail(e){return{name:this.name,index:null==e?this.fileList.length:e}},onAfterRead(e){const{maxSize:r,afterRead:a}=this;(Array.isArray(e)?e.some((e=>e.size>r)):e.size>r)?this.$emit("oversize",Object.assign({file:e},this.getDetail())):("function"==typeof a&&a(e,this.getDetail()),this.$emit("afterRead",Object.assign({file:e},this.getDetail())))},deleteItem(e){this.$emit("delete",Object.assign(Object.assign({},this.getDetail(e)),{file:this.fileList[e]}))},onPreviewImage(e,r){const a=this.$uv.deepClone(this.lists);a.map(((e,a)=>{a==r&&(e.current=!0)}));const t=a.filter((e=>e.isImage)).findIndex((e=>e.current));this.onClickPreview(e,r),e.isImage&&this.previewFullImage&&q({urls:this.lists.filter((e=>"image"===this.accept||T(e.url||e.thumb))).map((e=>e.url||e.thumb)),current:t,fail(){this.$uv.toast("预览图片失败")}})},onPreviewVideo(e,r){this.onClickPreview(e,r),this.previewFullVideo&&e.isVideo&&this.$refs.previewVideo.open(e.url)},onClickPreview(e,r){this.$emit("clickPreview",Object.assign(Object.assign({},e),this.getDetail(r)))}}},[["render",function(e,r,a,t,n,g){const _=V,f=s(o("uv-icon"),se),b=A,y=C,x=s(o("uv-loading-icon"),fe),P=s(o("uv-preview-video"),je);return i(),d(y,{class:"uv-upload",style:O([e.$uv.addStyle(e.customStyle)])},{default:u((()=>[c(y,{class:"uv-upload__wrap"},{default:u((()=>[e.previewImage?(i(!0),l(w,{key:0},k(n.lists,((r,a)=>(i(),d(y,{class:"uv-upload__wrap__preview",key:a},{default:u((()=>[r.isImage||r.type&&"image"===r.type?(i(),d(_,{key:0,src:r.thumb||r.url,mode:e.imageMode,class:"uv-upload__wrap__preview__image",onClick:e=>g.onPreviewImage(r,a),style:O([{width:e.$uv.addUnit(e.width),height:e.$uv.addUnit(e.height)}])},null,8,["src","mode","onClick","style"])):(i(),d(y,{key:1,class:"uv-upload__wrap__preview__other",onClick:e=>g.onPreviewVideo(r,a),style:O([{width:e.$uv.addUnit(e.width),height:e.$uv.addUnit(e.height)}])},{default:u((()=>[c(f,{color:"#80CBF9",size:"26",name:r.isVideo||r.type&&"video"===r.type?"movie":"folder"},null,8,["name"]),c(b,{class:"uv-upload__wrap__preview__other__text"},{default:u((()=>[p(h(r.isVideo||r.type&&"video"===r.type?"视频":"文件"),1)])),_:2},1024)])),_:2},1032,["onClick","style"])),"uploading"===r.status||"failed"===r.status?(i(),d(y,{key:2,class:"uv-upload__status"},{default:u((()=>[c(y,{class:"uv-upload__status__icon"},{default:u((()=>["failed"===r.status?(i(),d(f,{key:0,name:"close-circle",color:"#ffffff",size:"25"})):(i(),d(x,{key:1,size:"22",mode:"circle"}))])),_:2},1024),r.message?(i(),d(b,{key:0,class:"uv-upload__status__message"},{default:u((()=>[p(h(r.message),1)])),_:2},1024)):m("",!0)])),_:2},1024)):m("",!0),"uploading"!==r.status&&(e.deletable||r.deletable)?(i(),d(y,{key:3,class:"uv-upload__deletable",onClick:U((e=>g.deleteItem(a)),["stop"])},{default:u((()=>[c(y,{class:"uv-upload__deletable__icon"},{default:u((()=>[c(f,{name:"close",color:"#ffffff",size:"10"})])),_:1})])),_:2},1032,["onClick"])):m("",!0),"success"===r.status?(i(),d(y,{key:4,class:"uv-upload__success"},{default:u((()=>[c(y,{class:"uv-upload__success__icon"},{default:u((()=>[c(f,{name:"checkmark",color:"#ffffff",size:"12"})])),_:1})])),_:1})):m("",!0)])),_:2},1024)))),128)):m("",!0),n.isInCount?(i(),d(y,{key:1,onClick:g.chooseFile},{default:u((()=>[N(e.$slots,"default",{},(()=>[c(y,{class:v(["uv-upload__button",[e.disabled&&"uv-upload__button--disabled"]]),"hover-class":e.disabled?"":"uv-upload__button--hover","hover-stay-time":"150",onClick:U(g.chooseFile,["stop"]),style:O([{width:e.$uv.addUnit(e.width),height:e.$uv.addUnit(e.height)}])},{default:u((()=>[c(f,{name:e.uploadIcon,size:"26",color:e.uploadIconColor},null,8,["name","color"]),e.uploadText?(i(),d(b,{key:0,class:"uv-upload__button__text"},{default:u((()=>[p(h(e.uploadText),1)])),_:1})):m("",!0)])),_:1},8,["hover-class","onClick","class","style"])]),!0)])),_:3},8,["onClick"])):m("",!0)])),_:3}),c(P,{ref:"previewVideo"},null,512)])),_:3},8,["style"])}],["__scopeId","data-v-8b6cd2cb"]]),Ie=ne(D({__name:"reportSuggest",props:{reportId:{type:String,default:""}},emits:["check","chatRp"],setup(e,{emit:r}){const a=r,t=e,l=n({}),h=()=>{W({url:"/pages/report/index"})},m=n([]);let _=null;$((()=>{clearInterval(_)}));const f=e=>{m.value=[e.file];(new FormData).append("file",e.file);let r=e.file.url;we(r).then((r=>{200==r.code&&(a("check",e.file.name),a("chatRp",{content:""}),ke({parseId:r.data}).then((e=>{if(e.data&&e.data.parseResult){const r={recordId:e.data.recordId,content:e.data.parseResult};a("chatRp",r)}else setTimeout((()=>{_=setInterval((()=>{ke({parseId:r.data}).then((e=>{if(e.data&&e.data.parseResult){const r={recordId:e.data.recordId,content:e.data.parseResult};a("chatRp",r),clearInterval(_)}})).catch((()=>{clearInterval(_)}))}),5e3)}),3e4)})))}))};return G((async()=>{if(t.reportId){const e=await be({admId:t.reportId});l.value=e.data.summaryItemList}})),(e,r)=>{const a=V,t=s(o("uv-upload"),Se),n=C;return i(),d(n,{class:"suggest-content"},{default:u((()=>[g("div",{class:"suggest-content__list"},[g("div",{class:"header"},[g("img",{class:"consultation_icon",src:ae,alt:""}),g("div",{class:"title"},"报告解读 ")]),g("div",{class:"text"},[p(" 您可以选择已获取的体检报告，也可以选择上传新的体检报告进行解读。 "),g("div",{class:"item",onClick:h},[c(a,{class:"item-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAP7SURBVFiF1ZhNSFxXFMd/584btTDOjJgGF1mM4CIFoV24CDSlbqTNotCSmVR3FrpIdwYb4gfS14Uzph+YQkpTyMKAYOuMJdCNkEBcGEih0Cy6cKcLC9JMqnEM9WPeO104U8f65uk4I9j/Zt6955z7fnfePffc9+CUSYoX8aEX3QI3QNoBq7a30SVRc1/r/x5J22c3/DwtgMvDa1dF5bvaQpRKYirax9Yr7UCXr2enrdar27lnQFSVe6gzUXOcgGlHZRywxHW7pseiD8v5Ws1buTaEKLCRrW/8eM6WfK2BgLkrQ7k3Fe12jVwEygIZUaelcJ09IRgAFN08ip85KYDjyjObugdWY3ljeqsZ2BVmfxqNPKkJUB5ignxWDZBRBagNkEpgRWCiGiBxK4cpCzSTCi8AH1UDdFydukV96oA8H9nlwfXzRrhR6WCucrPwuA9IcR8LplfU/F4xkKjTggR6KwUSde4BnkCZZPRup704OWe3+m6QnkAWLOXRzysFsmDJz34YTFmgH8aalgC7UqBa6P+xqKtRYnAtjph+oAMlqzAZqn8+MmG3bvb0r59x6kkp2g1ioTpvHEZ+/GKvxEh8YLVTTOARsJROhlsBEkOrb6gGxo8CsFVP4mc7nIXdg55R43XQm3fyOz0BK/gAOL/fpBuO0FWse96lwyUqhs6jAAU2nRCQ7R5YjTlqyk3iYsAK/gacOWiSUECZ6rUXX5uwWze9094JLqhxDs8yJR9s2MkCOGL1gzb4eHvA/KtYbqepF7jjCZT+MrRCpVkm+q6P9clOnXMpuB1YBKJeDkbNO8CdWmZZm49t4b7dtAaslfXQXdDaAQnZquINm+CT9onh3ANc13vWIsvpZPitfX0qc6DxEsAVlFkAhceF3xmBZgVLkHjpmlPVX3yBUG1DJHbIvEqUH4XA+8UxFSYzyfD1Uo9MMvxp8ToxnDuHFjNZloNb3PYHcvUTjLR4mUQProV0sulpYjDXg+gUYInq1fjgegeAEWank+GbV4ZyU65qiwgWqhcKoRuuOB9MfR3N+gKlxyKzZWHLxaQaM/HBF+dEZBwkJLL7D2ih6Cp6QYRYSUge4dLMaPTXYkfNa1kmFbmlRzsp5FHpSY+G50s7T6S4ZpIRW9FvS7o6PhzZeB10b3N09Vo61Zj5b6xxjFVIV/XbSStWti7cB8wUmu2u4z4FCQGo6kh6LHLbK0567cWGl9vNz4CQCl+JcNfR4JFee730V7Dhj+IreeFDxkPg7T0P/SadjPSVixeAxOD6AELquBD7BhS5NT3aeK3YTth/hthueAR0ADPP6hq7/b4hGIB0KjymynWQ5WqBXNd9XtpO22c32DHvKXx/GMyp1D8pG3fh5zcNlgAAAABJRU5ErkJggg=="}),p(" 就诊人关联体检报告 "),g("div",{class:"btn"},"查看")]),g("div",{class:"item"},[c(a,{class:"item-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAOiSURBVFiF7ZfPi1tVFMc/570kje0YpoyVQSrNMoLCLGahUrCBLuqi0HYScATpFPsH6KLMJF00Ik3EtpSC+4m4mMVzpIMWFIWM2J0uZjmLWQQpONRK0jjYTPLyThfJZPJjkpcfr9aF301yzj0n55N377vnXqFF7y0VwjXD/Ao4iTfaBe4RKF+0Ui/vDJJgtBo1w7zmIQzAIeACleAP8dTDiUESfG2WEkYAyCv65TgkgpwAFhrm21SCP8av7Jy3bkxs989rUTxRyiGcQlm3MqHoOECxpcIpMcxch3uTqhHtB2X0GvBW8nvjSwS/k4tf2Zl+rkCKfgqsNqF8zq9ziVLkuQEJ2FY6FAO903AcNyB3EJSvK/sAxRPFmIq87hanot+tXp/8rcsPUxeuPjlRg9um2sdA30eYNpTcXKIUXc2ENgcGOpsqvUSFFRkgVtSYA97o8sNNU6s3uxOYNoTbwLt7Ltcp+5sXi4r+7BYH2OCs7RmO42zVff0lyvFW2/Vfr6fEBk4PANSmbz6fejB3tfiWoUavqV4EIqo8GgpoHDXWU9eaAognShcRRlzUyZKF6qxbnIp88XU6dGuQ3+wlV6D6JubEEHELReAy8GyBrBsT2/Fk6RLwTn8YsVXG638DAQFY6VAWyI5bbBD9S71scP0P5Kb2NSQEGp/hWPJxyosCglmw0hN3RgNSfaXxeocFueYFEDicWyqs3f3saH54oH3ZKPebljADTAJFlI0W/yzQOCvLBqrFlrEwEB6CHOi9hnasTCiKXZ7HLs8TKL+KyE8oG1YmFK3Vqh9Un9TOHwn8dUxhDUAd+2MOlc/WDP9CzfAviMrdYWH6AdXlC67gD/6h5RdmqTnNHdj0+X/xHzb/3NmdOu3f5XIzvhKMmVrNm1rNq+hH3gPVZZumU1aRzun1ifDhyq3QIyAPgLBFfQPNIvS9XfRS/53aNuarR6plAH/F/BbtPN9Iu10xtvBptm44MyA9D/MjAUlAF30V86SgEeqLd709Qu+dSxUmqVAv7HfOAMuN7GFZAJcpU0dnBGZB2m6dhui8o7xmpUNZX8XIAMGRqh+gXk/ocDxZWka7DlCReLK07CgYArFk6U2hHiNiLtLcArwHCgALXU9dmGb/etw+LJwZF6YbSOQhEEbYRvneiwKgj4vByQejASn/IIDDppUJXfIGaDj9x7v9njzs9n0UPsjZefzI7wV71+1dtF8T6Jgy06l9Ai1d/tnrfqNmU08Bk2gq+4/1T00AAAAASUVORK5CYII="}),p(" 上传PDF体检报告 "),c(t,{class:"upload-btn",fileList:[],name:"1",maxCount:1,onAfterRead:f,accept:"file"},{default:u((()=>[g("div",{class:"btn"},"上传")])),_:1})])])])])),_:1})}}}),[["__scopeId","data-v-65dfeaa7"]]),Le=ne(D({__name:"report",setup(e){const r=n("");K((e=>{r.value=null==e?void 0:e.reportId,r.value&&a.value.push({type:"report"}),"znwy"==(null==e?void 0:e.type)?a.value.push({type:"znwy"}):"bgjd"==(null==e?void 0:e.type)?a.value.push({type:"bgjd"}):(null==e?void 0:e.content)&&(null==e?void 0:e.type)&&b({type:null==e?void 0:e.type,content:null==e?void 0:e.content,templateId:null==e?void 0:e.templateId})}));const a=n([]),t=n(),p=(e,r=null)=>{if(t.value.handleInput(e,r),h.value)return _.value.show({type:"default",message:"正在问答中"})},h=n(!1),_=n(),f=async e=>{var r;const t=n({type:"responseReport",recordId:"",content:""});e.content?(a.value.pop(),t.value.recordId=e.recordId,a.value.push(t.value),y(t.value,null==(r=e.content)?void 0:r.split(""),(()=>{h.value=!1,Y((()=>{Z({selector:".page-bottom",duration:20})}))}))):a.value.push(t.value)},b=async e=>{var r,t;if("znwy"!=e.type){if(h.value)return _.value.show({type:"default",message:"正在问答中"});try{if(Z({selector:".page-bottom",duration:20}),h.value=!0,a.value.push(e),!e.content.includes(".pdf")){const s=await X(e);if("llm"===s.type){const e=n({type:"responseLlm",recordId:s.recordId,content:""});a.value.push(e.value),y(e.value,null==(t=null==(r=s.consultInfo)?void 0:r.recommendations)?void 0:t.split(""),(()=>{var r,a,t,n,o;(null==(a=null==(r=null==s?void 0:s.consultInfo)?void 0:r.departments)?void 0:a.length)||(null==(t=null==s?void 0:s.consultInfo)?void 0:t.deptFlag)?e.value.departments=(null==(o=null==s?void 0:s.consultInfo)?void 0:o.departments)||[]:e.value.message=(null==(n=null==s?void 0:s.consultInfo)?void 0:n.message)||"",h.value=!1,Y((()=>{Z({selector:".page-bottom",duration:20})}))}))}else if("rag"===s.type){const e=n({type:"responseRag",recordId:s.recordId,knowledgeBaseList:[]}),r=a=>{var t;if("btn"===a.type){e.value.knowledgeBaseList.push(a);const t=s.knowledgeBaseList.shift();t?r(t):h.value=!1,Y((()=>{Z({selector:".page-bottom",duration:20})}))}else{e.value.knowledgeBaseList.push({type:a.type,content:"",url:a.url,newLineFlag:a.newLineFlag});const n=e.value.knowledgeBaseList[e.value.knowledgeBaseList.length-1];y(n,null==(t=a.content)?void 0:t.split(""),(()=>{const e=s.knowledgeBaseList.shift();e?r(e):h.value=!1,Z({selector:".page-bottom",duration:20})}))}},t=s.knowledgeBaseList.shift();t&&r(t),a.value.push(e.value)}}}catch(s){h.value=!1}}else a.value.push({type:e.type})},v=function(e,r){let a=!0;return function(){if(!a)return!1;a=!1,e(),setTimeout((()=>{a=!0}),r)}}((()=>{Z({selector:".page-bottom",duration:20})}),800),y=(e,r,a)=>{r.length?(e.content+=r.shift(),v(),requestAnimationFrame((()=>{y(e,r,a)}))):a()};return(e,n)=>{const v=C,y=s(o("uv-toast"),ee);return i(),l(w,null,[c(v,{class:"consult-content"},{default:u((()=>[g("div",{class:"consult-content__chat-list"},[(i(!0),l(w,null,k(a.value,((e,t)=>(i(),l(w,{key:t},["report"===e.type?(i(),d(te,{key:0,reportId:r.value,onCheck:p},null,8,["reportId"])):m("",!0),"question"===e.type?(i(),d(re,{key:1,content:e.content},null,8,["content"])):"responseLlm"===e.type?(i(),d(me,{key:2,item:e,question:h.value&&t===a.value.length-1},null,8,["item","question"])):"responseRag"===e.type?(i(),d(_e,{key:3,item:e,question:h.value&&t===a.value.length-1},null,8,["item","question"])):"bgjd"===e.type?(i(),d(Ie,{key:4,onCheck:p,onChatRp:f})):"responseReport"===e.type?(i(),d(Pe,{key:5,item:e,question:h.value&&t===a.value.length-1},null,8,["item","question"])):m("",!0)],64)))),128))])])),_:1}),c(Q,{ref_key:"chatInput",ref:t,onChat:b,tab:2,question:h.value,type:r.value?2:1},null,8,["question","type"]),c(y,{ref_key:"toast",ref:_},null,512),c(J),g("div",{class:"page-bottom"})],64)}}}),[["__scopeId","data-v-03f8e00f"]]);export{Le as default};

var e,t;import{_ as o}from"./uv-loading-icon.g3FCLMWS.js";import{p as a,q as i,ah as s,j as r,k as l,a as n,c as d,w as p,l as u,u as h,s as m,f as c,F as g,e as y,t as f,m as S,_ as b,x as v,ai as x,i as z}from"./index-BKmNCtIL.js";import{_ as T,a as C}from"./uv-icon.lsvsjwja.js";const _=T({name:"uv-button",mixins:[a,i,{props:{hairline:{type:Boolean,default:!0},type:{type:String,default:"info"},size:{type:String,default:"normal"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},loadingText:{type:[String,Number],default:""},loadingMode:{type:String,default:"spinner"},loadingSize:{type:[String,Number],default:14},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!0},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!0},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:0},hoverStartTime:{type:[String,Number],default:0},hoverStayTime:{type:[String,Number],default:200},text:{type:[String,Number],default:""},icon:{type:String,default:""},iconSize:{type:[String,Number],default:""},iconColor:{type:String,default:"#000000"},color:{type:String,default:""},customTextStyle:{type:[Object,String],default:""},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.button}}],emits:["click"],data:()=>({}),computed:{bemClass(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor(){return this.plain?this.color?this.color:"#3c9cff":"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor(){let e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle(){let e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize(){let e=14,{size:t}=this;return"large"===t&&(e=16),"normal"===t&&(e=14),"small"===t&&(e=12),"mini"===t&&(e=10),e},getIconSize(){const e=this.iconSize?this.iconSize:1.35*this.textSize;return this.$uv.addUnit(e)},btnWrapperStyle(){const e={},t=this.$uv.addStyle(this.customStyle);return t.width&&(e.width=t.width),e}},methods:{clickHandler(){this.disabled||this.loading||s((()=>{this.$emit("click")}),this.throttleTime)}}},[["render",function(e,t,a,i,s,T){const _=r(l("uv-loading-icon"),o),k=v,w=r(l("uv-icon"),C),N=x,M=z;return n(),d(M,{class:"uv-button-wrapper",style:m([T.btnWrapperStyle])},{default:p((()=>[u(N,{"hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.disabled||e.loading?"":"uv-button--active",class:h(["uv-button uv-reset-button",T.bemClass]),style:m([T.baseColor,e.$uv.addStyle(e.customStyle)]),onClick:T.clickHandler},{default:p((()=>[e.loading?(n(),c(g,{key:0},[u(_,{mode:e.loadingMode,size:1.15*e.loadingSize,color:T.loadingColor},null,8,["mode","size","color"]),u(k,{class:"uv-button__loading-text",style:m([{fontSize:T.textSize+"px"},e.$uv.addStyle(e.customTextStyle)])},{default:p((()=>[y(f(e.loadingText||e.text),1)])),_:1},8,["style"])],64)):(n(),c(g,{key:1},[e.icon?(n(),d(w,{key:0,name:e.icon,color:T.iconColorCom,size:T.getIconSize,customStyle:{marginRight:"2px"}},null,8,["name","color","size"])):S("",!0),b(e.$slots,"default",{},(()=>[u(k,{class:"uv-button__text",style:m([{fontSize:T.textSize+"px"},e.$uv.addStyle(e.customTextStyle)])},{default:p((()=>[y(f(e.text),1)])),_:1},8,["style"])]),!0),b(e.$slots,"suffix",{},void 0,!0)],64))])),_:3},8,["hover-start-time","hover-stay-time","form-type","open-type","app-parameter","hover-stop-propagation","send-message-title","send-message-path","lang","data-name","session-from","send-message-img","show-message-card","hover-class","style","onClick","class"])])),_:3},8,["style"])}],["__scopeId","data-v-74b376fb"]]);export{_};

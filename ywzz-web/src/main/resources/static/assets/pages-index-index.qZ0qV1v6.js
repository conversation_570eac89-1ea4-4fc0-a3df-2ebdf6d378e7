import{d as e,r as t,o as s,g as a,a as n,c as l,w as c,b as o,e as u,f as p,F as i,h as d,t as r,i as m,j as g,k as v,l as y,n as _,m as k}from"./index-BKmNCtIL.js";import{C as f,P as h,_ as I,Q as $}from"./popup.CqrlupOr.js";import{_ as x}from"./uv-icon.lsvsjwja.js";import"./uv-loading-icon.g3FCLMWS.js";const C=x(e({__name:"suggest",emits:["check"],setup(e,{emit:g}){const v=g,y=t([]);return s((async()=>{const e=await a({});y.value=e})),(e,t)=>{const s=m;return n(),l(s,{class:"suggest-content"},{default:c((()=>[o("div",{class:"suggest-content__prompt"},[o("div",null,[o("div",{class:"title"},[u("自助健康咨询专家"),o("div",{class:"underline"})]),o("div",{class:"desc"},"可以根据您的描述为您推荐就诊科室，帮您解读体检报告，回答您的问题，专注为您服务。")])]),o("div",{class:"suggest-content__list"},[o("div",{class:"header"},[o("img",{class:"ask",src:"/ywzz/assets/ask-new-Cepne0Zu.png",alt:""})]),o("div",{class:"scroll-wapper"},[o("div",{class:"scroll-list",style:{"margin-right":"120rpx"}},[(n(!0),p(i,null,d(y.value,(e=>(n(),p("span",{class:"item",key:e.templateId},[o("span",{onClick:t=>v("check",e.content,e.templateId,e.pageName)},r(e.content),9,["onClick"])])))),128))]),o("div",{class:"scroll-list"},[(n(!0),p(i,null,d(y.value,(e=>(n(),p("span",{class:"item",key:e.templateId},[o("span",{onClick:t=>v("check",e.content,e.templateId,e.pageName)},r(e.content),9,["onClick"])])))),128))])])]),o("div",{class:"suggest-content__message"}," 如您使用咨询功能，将代表您允许公众号服务使用您的相关体检信息。 ")])),_:1})}}}),[["__scopeId","data-v-9a4104b8"]]),w=x(e({__name:"index",setup(e){const s=t(""),a=t([{type:"suggest"}]),u=t(),r=(e,t=null,s)=>{if(x.value)return w.value.show({type:"default",message:"正在问答中"});u.value.handleInput(e,t,s)},x=t(!1),w=t(),b=async({type:e,content:t,templateId:s,pageName:a})=>{_("znwy"==a?{url:`/pages/consult/medication?content=${t}&type=${e}&templateId=${s||""}`}:"xxzx"==a?{url:`/pages/consult/info?content=${t}&type=${e}&templateId=${s||""}`}:{url:`/pages/consult/index?content=${t}&type=${e}&templateId=${s||""}`})};return(e,t)=>{const _=m,j=g(v("uv-toast"),I);return n(),p(i,null,[y(_,{class:"consult-content"},{default:c((()=>[o("div",{class:"consult-content__chat-list"},[(n(!0),p(i,null,d(a.value,((e,t)=>(n(),p(i,{key:t},["suggest"===e.type?(n(),l(C,{key:0,onCheck:r})):"question"===e.type?(n(),l($,{key:1,content:e.content},null,8,["content"])):k("",!0)],64)))),128))])])),_:1}),y(f,{ref_key:"chatInput",ref:u,onChat:b,question:x.value,type:s.value?2:1},null,8,["question","type"]),y(j,{ref_key:"toast",ref:w},null,512),y(h),o("div",{class:"page-bottom"})],64)}}}),[["__scopeId","data-v-e027e7eb"]]);export{w as default};

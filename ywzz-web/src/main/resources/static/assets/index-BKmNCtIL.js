function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["/ywzz/assets/pages-index-index.qZ0qV1v6.js","/ywzz/assets/popup.CqrlupOr.js","/ywzz/assets/uv-icon.lsvsjwja.js","/ywzz/assets/uv-icon-CHAcjNdy.css","/ywzz/assets/uv-loading-icon.g3FCLMWS.js","/ywzz/assets/uv-loading-icon-CSl6J7gL.css","/ywzz/assets/popup-C8GcOSw_.css","/ywzz/assets/index-DW2mcVer.css","/ywzz/assets/pages-consult-index.CCsjl5z9.js","/ywzz/assets/responseLlm.CCC7rJk1.js","/ywzz/assets/uv-button.BP6GkdBe.js","/ywzz/assets/uv-button-D3HRQhq5.css","/ywzz/assets/responseLlm-DToOu-RL.css","/ywzz/assets/report.DGmwSo_4.js","/ywzz/assets/report.B1eHB6fc.js","/ywzz/assets/report-CgvzMm9O.css","/ywzz/assets/responseRag.C2ij8iPb.js","/ywzz/assets/responseRag-Bvl5x1Ju.css","/ywzz/assets/index-3z7SA5lw.css","/ywzz/assets/infoSuggest-B1lTP8oP.css","/ywzz/assets/pages-consult-report.CeVyLx1y.js","/ywzz/assets/report-DDyXxOti.css","/ywzz/assets/pages-consult-medication.KiHFwP9W.js","/ywzz/assets/medication-CBZ-ycVD.css","/ywzz/assets/pages-consult-info.BC-IZLk8.js","/ywzz/assets/info-In_g-xIO.css","/ywzz/assets/pages-login-index.B_3eJiLv.js","/ywzz/assets/index-CI8g0wDy.css","/ywzz/assets/pages-report-index.D_UEOGH_.js","/ywzz/assets/title-icon.DwRpnmP7.js","/ywzz/assets/index-6KrXyMZR.css","/ywzz/assets/pages-report-info-index.BEIAciPT.js","/ywzz/assets/index-CvA7Jz57.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var e=Object.defineProperty,t=(t,n,o)=>(((t,n,o)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o})(t,"symbol"!=typeof n?n+"":n,o),o);!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},o=function(e,t,o){let r=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(t.map((t=>{if(t=function(e,t){return"."===e[0]?new URL(e,t).href:e}(t,o),t in n)return;n[t]=!0;const r=t.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let n=e.length-1;n>=0;n--){const o=e[n];if(o.href===t&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=t,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0})))}return r.then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function r(e,t){const n=new Set(e.split(","));return e=>n.has(e)}const i={},s=[],a=()=>{},l=()=>!1,c=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),u=e=>e.startsWith("onUpdate:"),f=Object.assign,d=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},h=Object.prototype.hasOwnProperty,p=(e,t)=>h.call(e,t),g=Array.isArray,m=e=>"[object Map]"===T(e),v=e=>"[object Set]"===T(e),y=e=>"function"==typeof e,b=e=>"string"==typeof e,_=e=>"symbol"==typeof e,w=e=>null!==e&&"object"==typeof e,x=e=>(w(e)||y(e))&&y(e.then)&&y(e.catch),S=Object.prototype.toString,T=e=>S.call(e),E=e=>T(e).slice(8,-1),C=e=>"[object Object]"===T(e),k=e=>b(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,O=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),A=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},$=/-(\w)/g,P=A((e=>e.replace($,((e,t)=>t?t.toUpperCase():"")))),M=/\B([A-Z])/g,B=A((e=>e.replace(M,"-$1").toLowerCase())),I=A((e=>e.charAt(0).toUpperCase()+e.slice(1))),L=A((e=>e?`on${I(e)}`:"")),j=(e,t)=>!Object.is(e,t),R=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},N=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},D=e=>{const t=parseFloat(e);return isNaN(t)?e:t},F=e=>{const t=b(e)?Number(e):NaN;return isNaN(t)?e:t};let U;const q=()=>U||(U="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function V(e){if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?Y(o):V(o);if(r)for(const e in r)t[e]=r[e]}return t}if(b(e)||w(e))return e}const H=/;(?![^(]*\))/g,z=/:([^]+)/,W=/\/\*[^]*?\*\//g;function Y(e){const t={};return e.replace(W,"").split(H).forEach((e=>{if(e){const n=e.split(z);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function X(e){let t="";if(b(e))t=e;else if(g(e))for(let n=0;n<e.length;n++){const o=X(e[n]);o&&(t+=o+" ")}else if(w(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const K=r("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Z(e){return!!e||""===e}const G=e=>b(e)?e:null==e?"":g(e)||w(e)&&(e.toString===S||!y(e.toString))?JSON.stringify(e,J,2):String(e),J=(e,t)=>t&&t.__v_isRef?J(e,t.value):m(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[Q(t,o)+" =>"]=n,e)),{})}:v(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>Q(e)))}:_(t)?Q(t):!w(t)||g(t)||C(t)?t:String(t),Q=(e,t="")=>{var n;return _(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},ee=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e)),te=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),ne=["list-item"].map((e=>"uni-"+e));function oe(e){if(-1!==ne.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==ee.indexOf(t)||-1!==te.indexOf(t)}const re="\n",ie=50,se="UNI_LOCALE",ae=["%","%"],le=/^([a-z-]+:)?\/\//i,ce=/^data:.*,.*/,ue="WEB_INVOKE_APPSERVICE",fe="onShow",de="onHide",he="onLaunch",pe="onError",ge="onThemeChange",me="onPageNotFound",ve="onUnhandledRejection",ye="onLoad",be="onUnload",_e="onInit",we="onSaveExitState",xe="onResize",Se="onBackPress",Te="onPageScroll",Ee="onTabItemTap",Ce="onReachBottom",ke="onPullDownRefresh",Oe="onShareTimeline",Ae="onAddToFavorites",$e="onShareAppMessage",Pe="onNavigationBarButtonTap",Me="onNavigationBarChange",Be="onNavigationBarSearchInputClicked",Ie="onNavigationBarSearchInputChanged",Le="onNavigationBarSearchInputConfirmed",je="onNavigationBarSearchInputFocusChanged",Re="onAppEnterForeground",Ne="onAppEnterBackground",De="onWebInvokeAppService";function Fe(e){return e&&(e.appContext?e.proxy:e)}function Ue(e){if(!e)return;let t=e.type.name;for(;t&&oe(B(t));)t=(e=e.parent).type.name;return e.proxy}function qe(e){return 1===e.nodeType}function Ve(e){if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),V(t)}if(b(e))return Y(e);if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?Y(o):Ve(o);if(r)for(const e in r)t[e]=r[e]}return t}return V(e)}function He(e){let t="";if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(g(e))for(let n=0;n<e.length;n++){const o=He(e[n]);o&&(t+=o+" ")}else t=X(e);return t.trim()}function ze(e){return 0===e.indexOf("/")}function We(e){return ze(e)?e:"/"+e}const Ye=(e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n};function Xe(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function Ke(e){return P(e.substring(5))}const Ze=Xe((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[Ke(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[Ke(e)],n.call(this,e)}}));function Ge(e){return f({},e.dataset,e.__uniDataset)}const Je=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function Qe(e){return{passive:e}}function et(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:Ge(e),offsetTop:n,offsetLeft:o}}function tt(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function nt(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=tt(e[n])}catch(o){t[n]=e[n]}})),t}const ot=/\+/g;function rt(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(ot," ");let r=e.indexOf("="),i=tt(r<0?e:e.slice(0,r)),s=r<0?null:tt(e.slice(r+1));if(i in t){let e=t[i];g(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function it(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);r=o((()=>e.apply(this,arguments)),t)};return i.cancel=function(){n(r)},i}class st{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const at=[_e,ye,fe,de,be,Se,Te,Ee,Ce,ke,Oe,$e,Ae,we,Pe,Be,Ie,Le,je];const lt=[fe,de,he,pe,ge,me,ve,"onExit",_e,ye,"onReady",be,xe,Se,Te,Ee,Ce,ke,Oe,Ae,$e,we,Pe,Be,Ie,Le,je];const ct=[];const ut=Xe(((e,t)=>{if(y(e._component.onError))return t(e)})),ft=function(){};ft.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var dt=ft;const ht={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function pt(e,t,n){if(b(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in ht?ht[o]:o}return r}var o;return t}function gt(e,t={},n="light"){const o=t[n],r={};return void 0===o?e:(Object.keys(e).forEach((i=>{const s=e[i];r[i]=C(s)?gt(s,t,n):g(s)?s.map((e=>"object"==typeof e?gt(e,t,n):pt(o,e))):pt(o,s,i)})),r)}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let mt,vt;class yt{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=mt,!e&&mt&&(this.index=(mt.scopes||(mt.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=mt;try{return mt=this,e()}finally{mt=t}}}on(){mt=this}off(){mt=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function bt(e){return new yt(e)}function _t(){return mt}class wt{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=mt){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Ot();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),At()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=Et,t=vt;try{return Et=!0,vt=this,this._runnings++,xt(this),this.fn()}finally{St(this),this._runnings--,vt=t,Et=e}}stop(){var e;this.active&&(xt(this),St(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function xt(e){e._trackId++,e._depsLength=0}function St(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Tt(e.deps[t],e);e.deps.length=e._depsLength}}function Tt(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let Et=!0,Ct=0;const kt=[];function Ot(){kt.push(Et),Et=!1}function At(){const e=kt.pop();Et=void 0===e||e}function $t(){Ct++}function Pt(){for(Ct--;!Ct&&Bt.length;)Bt.shift()()}function Mt(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Tt(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Bt=[];function It(e,t,n){$t();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Bt.push(o.scheduler)))}Pt()}const Lt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},jt=new WeakMap,Rt=Symbol(""),Nt=Symbol("");function Dt(e,t,n){if(Et&&vt){let t=jt.get(e);t||jt.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Lt((()=>t.delete(n)))),Mt(vt,o)}}function Ft(e,t,n,o,r,i){const s=jt.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&g(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!_(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":g(e)?k(n)&&a.push(s.get("length")):(a.push(s.get(Rt)),m(e)&&a.push(s.get(Nt)));break;case"delete":g(e)||(a.push(s.get(Rt)),m(e)&&a.push(s.get(Nt)));break;case"set":m(e)&&a.push(s.get(Rt))}$t();for(const l of a)l&&It(l,4);Pt()}const Ut=r("__proto__,__v_isRef,__isVue"),qt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(_)),Vt=Ht();function Ht(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Mn(this);for(let t=0,r=this.length;t<r;t++)Dt(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Mn)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Ot(),$t();const n=Mn(this)[t].apply(this,e);return Pt(),At(),n}})),e}function zt(e){const t=Mn(this);return Dt(t,0,e),t.hasOwnProperty(e)}class Wt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Sn:xn:r?wn:_n).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=g(e);if(!o){if(i&&p(Vt,t))return Reflect.get(Vt,t,n);if("hasOwnProperty"===t)return zt}const s=Reflect.get(e,t,n);return(_(t)?qt.has(t):Ut(t))?s:(o||Dt(e,0,t),r?s:Dn(s)?i&&k(t)?s:s.value:w(s)?o?Cn(s):Tn(s):s)}}class Yt extends Wt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=An(r);if($n(n)||An(n)||(r=Mn(r),n=Mn(n)),!g(e)&&Dn(r)&&!Dn(n))return!t&&(r.value=n,!0)}const i=g(e)&&k(t)?Number(t)<e.length:p(e,t),s=Reflect.set(e,t,n,o);return e===Mn(o)&&(i?j(n,r)&&Ft(e,"set",t,n):Ft(e,"add",t,n)),s}deleteProperty(e,t){const n=p(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Ft(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return _(t)&&qt.has(t)||Dt(e,0,t),n}ownKeys(e){return Dt(e,0,g(e)?"length":Rt),Reflect.ownKeys(e)}}class Xt extends Wt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Kt=new Yt,Zt=new Xt,Gt=new Yt(!0),Jt=e=>e,Qt=e=>Reflect.getPrototypeOf(e);function en(e,t,n=!1,o=!1){const r=Mn(e=e.__v_raw),i=Mn(t);n||(j(t,i)&&Dt(r,0,t),Dt(r,0,i));const{has:s}=Qt(r),a=o?Jt:n?Ln:In;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function tn(e,t=!1){const n=this.__v_raw,o=Mn(n),r=Mn(e);return t||(j(e,r)&&Dt(o,0,e),Dt(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function nn(e,t=!1){return e=e.__v_raw,!t&&Dt(Mn(e),0,Rt),Reflect.get(e,"size",e)}function on(e){e=Mn(e);const t=Mn(this);return Qt(t).has.call(t,e)||(t.add(e),Ft(t,"add",e,e)),this}function rn(e,t){t=Mn(t);const n=Mn(this),{has:o,get:r}=Qt(n);let i=o.call(n,e);i||(e=Mn(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?j(t,s)&&Ft(n,"set",e,t):Ft(n,"add",e,t),this}function sn(e){const t=Mn(this),{has:n,get:o}=Qt(t);let r=n.call(t,e);r||(e=Mn(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Ft(t,"delete",e,void 0),i}function an(){const e=Mn(this),t=0!==e.size,n=e.clear();return t&&Ft(e,"clear",void 0,void 0),n}function ln(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Mn(i),a=t?Jt:e?Ln:In;return!e&&Dt(s,0,Rt),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function cn(e,t,n){return function(...o){const r=this.__v_raw,i=Mn(r),s=m(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?Jt:t?Ln:In;return!t&&Dt(i,0,l?Nt:Rt),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function un(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function fn(){const e={get(e){return en(this,e)},get size(){return nn(this)},has:tn,add:on,set:rn,delete:sn,clear:an,forEach:ln(!1,!1)},t={get(e){return en(this,e,!1,!0)},get size(){return nn(this)},has:tn,add:on,set:rn,delete:sn,clear:an,forEach:ln(!1,!0)},n={get(e){return en(this,e,!0)},get size(){return nn(this,!0)},has(e){return tn.call(this,e,!0)},add:un("add"),set:un("set"),delete:un("delete"),clear:un("clear"),forEach:ln(!0,!1)},o={get(e){return en(this,e,!0,!0)},get size(){return nn(this,!0)},has(e){return tn.call(this,e,!0)},add:un("add"),set:un("set"),delete:un("delete"),clear:un("clear"),forEach:ln(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=cn(r,!1,!1),n[r]=cn(r,!0,!1),t[r]=cn(r,!1,!0),o[r]=cn(r,!0,!0)})),[e,n,t,o]}const[dn,hn,pn,gn]=fn();function mn(e,t){const n=t?e?gn:pn:e?hn:dn;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(p(n,o)&&o in t?n:t,o,r)}const vn={get:mn(!1,!1)},yn={get:mn(!1,!0)},bn={get:mn(!0,!1)},_n=new WeakMap,wn=new WeakMap,xn=new WeakMap,Sn=new WeakMap;function Tn(e){return An(e)?e:kn(e,!1,Kt,vn,_n)}function En(e){return kn(e,!1,Gt,yn,wn)}function Cn(e){return kn(e,!0,Zt,bn,xn)}function kn(e,t,n,o,r){if(!w(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=(a=e).__v_skip||!Object.isExtensible(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(E(a));var a;if(0===s)return e;const l=new Proxy(e,2===s?o:n);return r.set(e,l),l}function On(e){return An(e)?On(e.__v_raw):!(!e||!e.__v_isReactive)}function An(e){return!(!e||!e.__v_isReadonly)}function $n(e){return!(!e||!e.__v_isShallow)}function Pn(e){return On(e)||An(e)}function Mn(e){const t=e&&e.__v_raw;return t?Mn(t):e}function Bn(e){return Object.isExtensible(e)&&N(e,"__v_skip",!0),e}const In=e=>w(e)?Tn(e):e,Ln=e=>w(e)?Cn(e):e;class jn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new wt((()=>e(this._value)),(()=>Nn(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Mn(this);return e._cacheable&&!e.effect.dirty||!j(e._value,e._value=e.effect.run())||Nn(e,4),Rn(e),e.effect._dirtyLevel>=2&&Nn(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function Rn(e){var t;Et&&vt&&(e=Mn(e),Mt(vt,null!=(t=e.dep)?t:e.dep=Lt((()=>e.dep=void 0),e instanceof jn?e:void 0)))}function Nn(e,t=4,n){const o=(e=Mn(e)).dep;o&&It(o,t)}function Dn(e){return!(!e||!0!==e.__v_isRef)}function Fn(e){return qn(e,!1)}function Un(e){return qn(e,!0)}function qn(e,t){return Dn(e)?e:new Vn(e,t)}class Vn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Mn(e),this._value=t?e:In(e)}get value(){return Rn(this),this._value}set value(e){const t=this.__v_isShallow||$n(e)||An(e);e=t?e:Mn(e),j(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:In(e),Nn(this,4))}}function Hn(e){return Dn(e)?e.value:e}const zn={get:(e,t,n)=>Hn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Dn(r)&&!Dn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Wn(e){return On(e)?e:new Proxy(e,zn)}class Yn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=Mn(this._object),t=this._key,null==(n=jt.get(e))?void 0:n.get(t);var e,t,n}}function Xn(e,t,n){const o=e[t];return Dn(o)?o:new Yn(e,t,n)}function Kn(e,t,n,o){try{return o?e(...o):e()}catch(r){Gn(r,t,n)}}function Zn(e,t,n,o){if(y(e)){const r=Kn(e,t,n,o);return r&&x(r)&&r.catch((e=>{Gn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(Zn(e[i],t,n,o));return r}function Gn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void Kn(s,null,10,[e,r,i])}}let Jn=!1,Qn=!1;const eo=[];let to=0;const no=[];let oo=null,ro=0;const io=Promise.resolve();let so=null;function ao(e){const t=so||io;return e?t.then(this?e.bind(this):e):t}function lo(e){eo.length&&eo.includes(e,Jn&&e.allowRecurse?to+1:to)||(null==e.id?eo.push(e):eo.splice(function(e){let t=to+1,n=eo.length;for(;t<n;){const o=t+n>>>1,r=eo[o],i=ho(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),co())}function co(){Jn||Qn||(Qn=!0,so=io.then(go))}function uo(e,t,n=(Jn?to+1:0)){for(;n<eo.length;n++){const t=eo[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;eo.splice(n,1),n--,t()}}}function fo(e){if(no.length){const e=[...new Set(no)].sort(((e,t)=>ho(e)-ho(t)));if(no.length=0,oo)return void oo.push(...e);for(oo=e,ro=0;ro<oo.length;ro++)oo[ro]();oo=null,ro=0}}const ho=e=>null==e.id?1/0:e.id,po=(e,t)=>{const n=ho(e)-ho(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function go(e){Qn=!1,Jn=!0,eo.sort(po);try{for(to=0;to<eo.length;to++){const e=eo[to];e&&!1!==e.active&&Kn(e,null,14)}}finally{to=0,eo.length=0,fo(),Jn=!1,so=null,(eo.length||no.length)&&go()}}function mo(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||i;let r=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=o[e]||i;s&&(r=n.map((e=>b(e)?e.trim():e))),t&&(r=n.map(D))}let l,c=o[l=L(t)]||o[l=L(P(t))];!c&&s&&(c=o[l=L(B(t))]),c&&Zn(c,e,6,vo(e,c,r));const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Zn(u,e,6,vo(e,u,r))}}function vo(e,t,n){if(1!==n.length)return n;if(y(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&p(o,"type")&&p(o,"timeStamp")&&p(o,"target")&&p(o,"currentTarget")&&p(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function yo(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!y(e)){const o=e=>{const n=yo(e,t,!0);n&&(a=!0,f(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(g(i)?i.forEach((e=>s[e]=null)):f(s,i),w(e)&&o.set(e,s),s):(w(e)&&o.set(e,null),null)}function bo(e,t){return!(!e||!c(t))&&(t=t.slice(2).replace(/Once$/,""),p(e,t[0].toLowerCase()+t.slice(1))||p(e,B(t))||p(e,t))}let _o=null,wo=null;function xo(e){const t=_o;return _o=e,wo=e&&e.type.__scopeId||null,t}function So(e,t=_o,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Bi(-1);const r=xo(t);let i;try{i=e(...n)}finally{xo(r),o._d&&Bi(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function To(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:f,renderCache:d,data:h,setupState:p,ctx:g,inheritAttrs:m}=e;let v,y;const b=xo(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=Yi(f.call(t,e,d,i,p,h,g)),y=l}else{const e=t;0,v=Yi(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),y=t.props?l:Eo(l)}}catch(w){Ai.length=0,Gn(w,e,1),v=Vi(ki)}let _=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(s&&e.some(u)&&(y=Co(y,s)),_=Hi(_,y))}return n.dirs&&(_=Hi(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),v=_,xo(b),v}const Eo=e=>{let t;for(const n in e)("class"===n||"style"===n||c(n))&&((t||(t={}))[n]=e[n]);return t},Co=(e,t)=>{const n={};for(const o in e)u(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function ko(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!bo(n,i))return!0}return!1}const Oo="components";function Ao(e,t){return Mo(Oo,e,!0,t)||e}const $o=Symbol.for("v-ndc");function Po(e){return b(e)?Mo(Oo,e,!1)||e:e||$o}function Mo(e,t,n=!0,o=!1){const r=_o||es;if(r){const n=r.type;{const e=ds(n,!1);if(e&&(e===t||e===P(t)||e===I(P(t))))return n}const i=Bo(r[e]||n[e],t)||Bo(r.appContext[e],t);return!i&&o?n:i}}function Bo(e,t){return e&&(e[t]||e[P(t)]||e[I(P(t))])}const Io=e=>e.__isSuspense;const Lo=Symbol.for("v-scx"),jo=()=>oi(Lo);function Ro(e,t){return Fo(e,null,t)}const No={};function Do(e,t,n){return Fo(e,t,n)}function Fo(e,t,{immediate:n,deep:o,flush:r,once:s,onTrack:l,onTrigger:c}=i){if(t&&s){const e=t;t=(...t)=>{e(...t),C()}}const u=es,f=e=>!0===o?e:Vo(e,!1===o?1:void 0);let h,p,m=!1,v=!1;if(Dn(e)?(h=()=>e.value,m=$n(e)):On(e)?(h=()=>f(e),m=!0):g(e)?(v=!0,m=e.some((e=>On(e)||$n(e))),h=()=>e.map((e=>Dn(e)?e.value:On(e)?f(e):y(e)?Kn(e,u,2):void 0))):h=y(e)?t?()=>Kn(e,u,2):()=>(p&&p(),Zn(e,u,3,[_])):a,t&&o){const e=h;h=()=>Vo(e())}let b,_=e=>{p=T.onStop=()=>{Kn(e,u,4),p=T.onStop=void 0}};if(ls){if(_=a,t?n&&Zn(t,u,3,[h(),v?[]:void 0,_]):h(),"sync"!==r)return a;{const e=jo();b=e.__watcherHandles||(e.__watcherHandles=[])}}let w=v?new Array(e.length).fill(No):No;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(o||m||(v?e.some(((e,t)=>j(e,w[t]))):j(e,w)))&&(p&&p(),Zn(t,u,3,[e,w===No?void 0:v&&w[0]===No?[]:w,_]),w=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===r?S=x:"post"===r?S=()=>bi(x,u&&u.suspense):(x.pre=!0,u&&(x.id=u.uid),S=()=>lo(x));const T=new wt(h,a,S),E=_t(),C=()=>{T.stop(),E&&d(E.effects,T)};return t?n?x():w=T.run():"post"===r?bi(T.run.bind(T),u&&u.suspense):T.run(),b&&b.push(C),C}function Uo(e,t,n){const o=this.proxy,r=b(e)?e.includes(".")?qo(o,e):()=>o[e]:e.bind(o,o);let i;y(t)?i=t:(i=t.handler,n=t);const s=rs(this),a=Fo(r,i.bind(o),n);return s(),a}function qo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Vo(e,t,n=0,o){if(!w(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),Dn(e))Vo(e.value,t,n,o);else if(g(e))for(let r=0;r<e.length;r++)Vo(e[r],t,n,o);else if(v(e)||m(e))e.forEach((e=>{Vo(e,t,n,o)}));else if(C(e))for(const r in e)Vo(e[r],t,n,o);return e}function Ho(e,t){if(null===_o)return e;const n=fs(_o)||_o.proxy,o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[e,s,a,l=i]=t[r];e&&(y(e)&&(e={mounted:e,updated:e}),e.deep&&Vo(s),o.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function zo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Ot(),Zn(l,n,8,[e.el,a,e,t]),At())}}const Wo=Symbol("_leaveCb"),Yo=Symbol("_enterCb");const Xo=[Function,Array],Ko={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Xo,onEnter:Xo,onAfterEnter:Xo,onEnterCancelled:Xo,onBeforeLeave:Xo,onLeave:Xo,onAfterLeave:Xo,onLeaveCancelled:Xo,onBeforeAppear:Xo,onAppear:Xo,onAfterAppear:Xo,onAppearCancelled:Xo},Zo={name:"BaseTransition",props:Ko,setup(e,{slots:t}){const n=ts(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Sr((()=>{e.isMounted=!0})),Cr((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&nr(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==ki){i=e;break}const s=Mn(e),{mode:a}=s;if(o.isLeaving)return Qo(i);const l=er(i);if(!l)return Qo(i);const c=Jo(l,s,o,n);tr(l,c);const u=n.subTree,f=u&&er(u);if(f&&f.type!==ki&&!Ni(l,f)){const e=Jo(f,s,o,n);if(tr(f,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},Qo(i);"in-out"===a&&l.type!==ki&&(e.delayLeave=(e,t,n)=>{Go(o,f)[String(f.key)]=f,e[Wo]=()=>{t(),e[Wo]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function Go(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function Jo(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:f,onLeave:d,onAfterLeave:h,onLeaveCancelled:p,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,_=String(e.key),w=Go(n,e),x=(e,t)=>{e&&Zn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),g(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=m||a}t[Wo]&&t[Wo](!0);const i=w[_];i&&Ni(e,i)&&i.el[Wo]&&i.el[Wo](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let s=!1;const a=e[Yo]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[Yo]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[Yo]&&t[Yo](!0),n.isUnmounting)return o();x(f,[t]);let i=!1;const s=t[Wo]=n=>{i||(i=!0,o(),x(n?p:h,[t]),t[Wo]=void 0,w[r]===e&&delete w[r])};w[r]=e,d?S(d,[t,s]):s()},clone:e=>Jo(e,t,n,o)};return T}function Qo(e){if(ar(e))return(e=Hi(e)).children=null,e}function er(e){return ar(e)?e.children?e.children[0]:void 0:e}function tr(e,t){6&e.shapeFlag&&e.component?tr(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function nr(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Ei?(128&s.patchFlag&&r++,o=o.concat(nr(s.children,t,a))):(t||s.type!==ki)&&o.push(null!=a?Hi(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function or(e,t){return y(e)?(()=>f({name:e.name},t,{setup:e}))():e}const rr=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function ir(e){y(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const f=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,f()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return or({name:"AsyncComponentWrapper",__asyncLoader:f,get __asyncResolved(){return l},setup(){const e=es;if(l)return()=>sr(l,e);const t=t=>{c=null,Gn(t,e,13,!o)};if(s&&e.suspense||ls)return f().then((t=>()=>sr(t,e))).catch((e=>(t(e),()=>o?Vi(o,{error:e}):null)));const a=Fn(!1),u=Fn(),d=Fn(!!r);return r&&setTimeout((()=>{d.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),f().then((()=>{a.value=!0,e.parent&&ar(e.parent.vnode)&&(e.parent.effect.dirty=!0,lo(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?sr(l,e):u.value&&o?Vi(o,{error:u.value}):n&&!d.value?Vi(n):void 0}})}function sr(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=Vi(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const ar=e=>e.type.__isKeepAlive;class lr{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const cr={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=ts(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new lr(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Ni(t,i)||"key"===e.matchBy&&t.key!==i.key?(mr(o=t),u(o,n,a,!0)):i&&mr(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=o,d=f("div");function h(t){r.forEach(((n,o)=>{const i=yr(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,R(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),bi((()=>{i.isDeactivated=!1,i.a&&R(i.a);const t=e.props&&e.props.onVnodeMounted;t&&Gi(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&br(t.bda),c(e,d,null,1,a),bi((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&R(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Gi(n,t.parent,e),t.isDeactivated=!0}),a)},Do((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&h((t=>fr(e,t))),t&&h((e=>!fr(t,e)))}),{flush:"post",deep:!0});let p=null;const g=()=>{null!=p&&r.set(p,vr(n.subTree))};return Sr(g),Er(g),Cr((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=vr(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&R(l.component.bda),mr(l);const e=l.component.da;e&&bi(e,a)}}))})),()=>{if(p=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Ri(o)||!(4&o.shapeFlag)&&!Io(o.type))return i=null,o;let s=vr(o);const a=s.type,l=yr(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!fr(c,l))||u&&l&&fr(u,l))return i=s,o;const f=null==s.key?a:s.key,d=r.get(f);return s.el&&(s=Hi(s),Io(o.type)&&(o.ssContent=s)),p=f,d&&(s.el=d.el,s.component=d.component,s.transition&&tr(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Io(o.type)?o:s}}},ur=cr;function fr(e,t){return g(e)?e.some((e=>fr(e,t))):b(e)?e.split(",").includes(t):"[object RegExp]"===T(e)&&e.test(t)}function dr(e,t){pr(e,"a",t)}function hr(e,t){pr(e,"da",t)}function pr(e,t,n=es){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,_r(t,o,n),n){let e=n.parent;for(;e&&e.parent;)ar(e.parent.vnode)&&gr(o,t,n,e),e=e.parent}}function gr(e,t,n,o){const r=_r(t,e,o,!0);kr((()=>{d(o[t],r)}),n)}function mr(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function vr(e){return Io(e.type)?e.ssContent:e}function yr(e,t){if("name"===t){const t=e.type;return ds(rr(e)?t.__asyncResolved||{}:t)}return String(e.key)}function br(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function _r(e,t,n=es,o=!1){if(n){if(r=e,at.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return[ye,fe].indexOf(e)>-1}(e))){const o=n.proxy;Zn(t.bind(o),n,e,ye===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Ot();const r=rs(n),i=Zn(t,n,e,o);return r(),At(),i});return o?i.unshift(s):i.push(s),s}var r}const wr=e=>(t,n=es)=>(!ls||"sp"===e)&&_r(e,((...e)=>t(...e)),n),xr=wr("bm"),Sr=wr("m"),Tr=wr("bu"),Er=wr("u"),Cr=wr("bum"),kr=wr("um"),Or=wr("sp"),Ar=wr("rtg"),$r=wr("rtc");function Pr(e,t=es){_r("ec",e,t)}function Mr(e,t,n,o){let r;const i=n;if(g(e)||b(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i)}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i)}else if(w(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i)));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i)}}else r=[];return r}function Br(e,t,n={},o,r){if(_o.isCE||_o.parent&&rr(_o.parent)&&_o.parent.isCE)return"default"!==t&&(n.name=t),Vi("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Pi();const s=i&&Ir(i(n)),a=ji(Ei,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Ir(e){return e.some((e=>!Ri(e)||e.type!==ki&&!(e.type===Ei&&!Ir(e.children))))?e:null}const Lr=e=>{if(!e)return null;if(ss(e)){return fs(e)||e.proxy}return Lr(e.parent)},jr=f(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Lr(e.parent),$root:e=>Lr(e.root),$emit:e=>e.emit,$options:e=>Hr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,lo(e.update)})(e)),$nextTick:e=>e.n||(e.n=ao.bind(e.proxy)),$watch:e=>Uo.bind(e)}),Rr=(e,t)=>e!==i&&!e.__isScriptSetup&&p(e,t),Nr={get({_:e},t){const{ctx:n,setupState:o,data:r,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(Rr(o,t))return a[t]=1,o[t];if(r!==i&&p(r,t))return a[t]=2,r[t];if((u=e.propsOptions[0])&&p(u,t))return a[t]=3,s[t];if(n!==i&&p(n,t))return a[t]=4,n[t];Fr&&(a[t]=0)}}const f=jr[t];let d,h;return f?("$attrs"===t&&Dt(e,0,t),f(e)):(d=l.__cssModules)&&(d=d[t])?d:n!==i&&p(n,t)?(a[t]=4,n[t]):(h=c.config.globalProperties,p(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:s}=e;return Rr(r,t)?(r[t]=n,!0):o!==i&&p(o,t)?(o[t]=n,!0):!p(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:s}},a){let l;return!!n[a]||e!==i&&p(e,a)||Rr(t,a)||(l=s[0])&&p(l,a)||p(o,a)||p(jr,a)||p(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:p(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Dr(e){return g(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Fr=!0;function Ur(e){const t=Hr(e),n=e.proxy,o=e.ctx;Fr=!1,t.beforeCreate&&qr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:l,provide:c,inject:u,created:f,beforeMount:d,mounted:h,beforeUpdate:p,updated:m,activated:v,deactivated:b,beforeDestroy:_,beforeUnmount:x,destroyed:S,unmounted:T,render:E,renderTracked:C,renderTriggered:k,errorCaptured:O,serverPrefetch:A,expose:$,inheritAttrs:P,components:M,directives:B,filters:I}=t;if(u&&function(e,t,n=a){g(e)&&(e=Xr(e));for(const o in e){const n=e[o];let r;r=w(n)?"default"in n?oi(n.from||o,n.default,!0):oi(n.from||o):oi(n),Dn(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),s)for(const a in s){const e=s[a];y(e)&&(o[a]=e.bind(n))}if(r){const t=r.call(n,n);w(t)&&(e.data=Tn(t))}if(Fr=!0,i)for(const g in i){const e=i[g],t=y(e)?e.bind(n,n):y(e.get)?e.get.bind(n,n):a,r=!y(e)&&y(e.set)?e.set.bind(n):a,s=hs({get:t,set:r});Object.defineProperty(o,g,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const a in l)Vr(l[a],o,n,a);if(c){const e=y(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{ni(t,e[t])}))}function L(e,t){g(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(f&&qr(f,e,"c"),L(xr,d),L(Sr,h),L(Tr,p),L(Er,m),L(dr,v),L(hr,b),L(Pr,O),L($r,C),L(Ar,k),L(Cr,x),L(kr,T),L(Or,A),g($))if($.length){const t=e.exposed||(e.exposed={});$.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});E&&e.render===a&&(e.render=E),null!=P&&(e.inheritAttrs=P),M&&(e.components=M),B&&(e.directives=B);const j=e.appContext.config.globalProperties.$applyOptions;j&&j(t,e,n)}function qr(e,t,n){Zn(g(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Vr(e,t,n,o){const r=o.includes(".")?qo(n,o):()=>n[o];if(b(e)){const n=t[e];y(n)&&Do(r,n)}else if(y(e))Do(r,e.bind(n));else if(w(e))if(g(e))e.forEach((e=>Vr(e,t,n,o)));else{const o=y(e.handler)?e.handler.bind(n):t[e.handler];y(o)&&Do(r,o,e)}}function Hr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>zr(l,e,s,!0))),zr(l,t,s)):l=t,w(t)&&i.set(t,l),l}function zr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&zr(e,i,n,!0),r&&r.forEach((t=>zr(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=Wr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const Wr={data:Yr,props:Gr,emits:Gr,methods:Zr,computed:Zr,beforeCreate:Kr,created:Kr,beforeMount:Kr,mounted:Kr,beforeUpdate:Kr,updated:Kr,beforeDestroy:Kr,beforeUnmount:Kr,destroyed:Kr,unmounted:Kr,activated:Kr,deactivated:Kr,errorCaptured:Kr,serverPrefetch:Kr,components:Zr,directives:Zr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=f(Object.create(null),e);for(const o in t)n[o]=Kr(e[o],t[o]);return n},provide:Yr,inject:function(e,t){return Zr(Xr(e),Xr(t))}};function Yr(e,t){return t?e?function(){return f(y(e)?e.call(this,this):e,y(t)?t.call(this,this):t)}:t:e}function Xr(e){if(g(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Kr(e,t){return e?[...new Set([].concat(e,t))]:t}function Zr(e,t){return e?f(Object.create(null),e,t):t}function Gr(e,t){return e?g(e)&&g(t)?[...new Set([...e,...t])]:f(Object.create(null),Dr(e),Dr(null!=t?t:{})):t}function Jr(){return{app:null,config:{isNativeTag:l,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Qr=0;function ei(e,t){return function(n,o=null){y(n)||(n=f({},n)),null==o||w(o)||(o=null);const r=Jr(),i=new WeakSet;let s=!1;const a=r.app={_uid:Qr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:gs,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&y(e.install)?(i.add(e),e.install(a,...t)):y(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=Vi(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,fs(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=ti;ti=a;try{return e()}finally{ti=t}}};return a}}let ti=null;function ni(e,t){if(es){let n=es.provides;const o=es.parent&&es.parent.provides;o===n&&(n=es.provides=Object.create(o)),n[e]=t,"app"===es.type.mpType&&es.appContext.app.provide(e,t)}else;}function oi(e,t,n=!1){const o=es||_o;if(o||ti){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:ti._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&y(t)?t.call(o&&o.proxy):t}}function ri(e,t,n,o){const[r,s]=e.propsOptions;let a,l=!1;if(t)for(let i in t){if(O(i))continue;const c=t[i];let u;r&&p(r,u=P(i))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:bo(e.emitsOptions,i)||i in o&&c===o[i]||(o[i]=c,l=!0)}if(s){const t=Mn(n),o=a||i;for(let i=0;i<s.length;i++){const a=s[i];n[a]=ii(r,t,a,o[a],e,!p(o,a))}}return l}function ii(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=p(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&y(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=rs(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==B(n)||(o=!0))}return o}function si(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const a=e.props,l={},c=[];let u=!1;if(!y(e)){const o=e=>{u=!0;const[n,o]=si(e,t,!0);f(l,n),o&&c.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!u)return w(e)&&o.set(e,s),s;if(g(a))for(let s=0;s<a.length;s++){const e=P(a[s]);ai(e)&&(l[e]=i)}else if(a)for(const i in a){const e=P(i);if(ai(e)){const t=a[i],n=l[e]=g(t)||y(t)?{type:t}:f({},t);if(n){const t=ui(Boolean,n.type),o=ui(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||p(n,"default"))&&c.push(e)}}}const d=[l,c];return w(e)&&o.set(e,d),d}function ai(e){return"$"!==e[0]&&!O(e)}function li(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function ci(e,t){return li(e)===li(t)}function ui(e,t){return g(t)?t.findIndex((t=>ci(t,e))):y(t)&&ci(t,e)?0:-1}const fi=e=>"_"===e[0]||"$stable"===e,di=e=>g(e)?e.map(Yi):[Yi(e)],hi=(e,t,n)=>{if(t._n)return t;const o=So(((...e)=>di(t(...e))),n);return o._c=!1,o},pi=(e,t,n)=>{const o=e._ctx;for(const r in e){if(fi(r))continue;const n=e[r];if(y(n))t[r]=hi(0,n,o);else if(null!=n){const e=di(n);t[r]=()=>e}}},gi=(e,t)=>{const n=di(t);e.slots.default=()=>n},mi=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Mn(t),N(t,"_",n)):pi(t,e.slots={})}else e.slots={},t&&gi(e,t);N(e.slots,Di,1)},vi=(e,t,n)=>{const{vnode:o,slots:r}=e;let s=!0,a=i;if(32&o.shapeFlag){const e=t._;e?n&&1===e?s=!1:(f(r,t),n||1!==e||delete r._):(s=!t.$stable,pi(t,r)),a=t}else t&&(gi(e,t),a={default:1});if(s)for(const i in r)fi(i)||null!=a[i]||delete r[i]};function yi(e,t,n,o,r=!1){if(g(e))return void e.forEach(((e,i)=>yi(e,t&&(g(t)?t[i]:t),n,o,r)));if(rr(o)&&!r)return;const s=4&o.shapeFlag?fs(o.component)||o.component.proxy:o.el,a=r?null:s,{i:l,r:c}=e,u=t&&t.r,f=l.refs===i?l.refs={}:l.refs,h=l.setupState;if(null!=u&&u!==c&&(b(u)?(f[u]=null,p(h,u)&&(h[u]=null)):Dn(u)&&(u.value=null)),y(c))Kn(c,l,12,[a,f]);else{const t=b(c),o=Dn(c);if(t||o){const i=()=>{if(e.f){const n=t?p(h,c)?h[c]:f[c]:c.value;r?g(n)&&d(n,s):g(n)?n.includes(s)||n.push(s):t?(f[c]=[s],p(h,c)&&(h[c]=f[c])):(c.value=[s],e.k&&(f[e.k]=c.value))}else t?(f[c]=a,p(h,c)&&(h[c]=a)):o&&(c.value=a,e.k&&(f[e.k]=a))};a?(i.id=-1,bi(i,n)):i()}}}const bi=function(e,t){var n;t&&t.pendingBranch?g(e)?t.effects.push(...e):t.effects.push(e):(g(n=e)?no.push(...n):oo&&oo.includes(n,n.allowRecurse?ro+1:ro)||no.push(n),co())};function _i(e){return function(e,t){q().__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:l,createElement:c,createText:u,createComment:f,setText:d,setElementText:h,parentNode:g,nextSibling:m,setScopeId:v=a,insertStaticContent:y}=e,b=(e,t,n,o=null,r=null,i=null,s=void 0,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Ni(e,t)&&(o=ee(e),K(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case Ci:_(e,t,n,o);break;case ki:w(e,t,n,o);break;case Oi:null==e&&S(t,n,o,s);break;case Ei:j(e,t,n,o,r,i,s,a,l);break;default:1&f?C(e,t,n,o,r,i,s,a,l):6&f?D(e,t,n,o,r,i,s,a,l):(64&f||128&f)&&c.process(e,t,n,o,r,i,s,a,l,oe)}null!=u&&r&&yi(u,e&&e.ref,i,t||e,!t)},_=(e,t,o,r)=>{if(null==e)n(t.el=u(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},w=(e,t,o,r)=>{null==e?n(t.el=f(t.children||""),o,r):t.el=e.el},S=(e,t,n,o)=>{[e.el,e.anchor]=y(e.children,t,n,o,e.el,e.anchor)},T=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=m(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=m(e),o(e),e=n;o(t)},C=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?k(t,n,o,r,i,s,a,l):M(e,t,r,i,s,a,l)},k=(e,t,o,i,s,a,l,u)=>{let f,d;const{props:p,shapeFlag:g,transition:m,dirs:v}=e;if(f=e.el=c(e.type,a,p&&p.is,p),8&g?h(f,e.children):16&g&&$(e.children,f,null,i,s,wi(e,a),l,u),v&&zo(e,null,i,"created"),A(f,e,e.scopeId,l,i),p){for(const t in p)"value"===t||O(t)||r(f,t,null,p[t],a,e.children,i,s,Q);"value"in p&&r(f,"value",null,p.value,a),(d=p.onVnodeBeforeMount)&&Gi(d,i,e)}Object.defineProperty(f,"__vueParentComponent",{value:i,enumerable:!1}),v&&zo(e,null,i,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,m);y&&m.beforeEnter(f),n(f,t,o),((d=p&&p.onVnodeMounted)||y||v)&&bi((()=>{d&&Gi(d,i,e),y&&m.enter(f),v&&zo(e,null,i,"mounted")}),s)},A=(e,t,n,o,r)=>{if(n&&v(e,n),o)for(let i=0;i<o.length;i++)v(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;A(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},$=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?Xi(e[c]):Yi(e[c]);b(null,l,t,n,o,r,i,s,a)}},M=(e,t,n,o,s,a,c)=>{const u=t.el=e.el;let{patchFlag:f,dynamicChildren:d,dirs:p}=t;f|=16&e.patchFlag;const g=e.props||i,m=t.props||i;let v;if(n&&xi(n,!1),(v=m.onVnodeBeforeUpdate)&&Gi(v,n,t,e),p&&zo(t,e,n,"beforeUpdate"),n&&xi(n,!0),d?I(e.dynamicChildren,d,u,n,o,wi(t,s),a):c||z(e,t,u,null,n,o,wi(t,s),a,!1),f>0){if(16&f)L(u,t,g,m,n,o,s);else if(2&f&&g.class!==m.class&&r(u,"class",null,m.class,s),4&f&&r(u,"style",g.style,m.style,s),8&f){const i=t.dynamicProps;for(let t=0;t<i.length;t++){const a=i[t],c=g[a],f=m[a];(f!==c||"value"===a||l&&l(u,a))&&r(u,a,c,f,s,e.children,n,o,Q)}}1&f&&e.children!==t.children&&h(u,t.children)}else c||null!=d||L(u,t,g,m,n,o,s);((v=m.onVnodeUpdated)||p)&&bi((()=>{v&&Gi(v,n,t,e),p&&zo(t,e,n,"updated")}),o)},I=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Ei||!Ni(l,c)||70&l.shapeFlag)?g(l.el):n;b(l,c,u,null,o,r,i,s,!0)}},L=(e,t,n,o,s,a,c)=>{if(n!==o){if(n!==i)for(const i in n)O(i)||i in o||r(e,i,n[i],null,c,t.children,s,a,Q);for(const i in o){if(O(i))continue;const u=o[i],f=n[i];(u!==f&&"value"!==i||l&&l(e,i))&&r(e,i,f,u,c,t.children,s,a,Q)}"value"in o&&r(e,"value",n.value,o.value,c)}},j=(e,t,o,r,i,s,a,l,c)=>{const f=t.el=e?e.el:u(""),d=t.anchor=e?e.anchor:u("");let{patchFlag:h,dynamicChildren:p,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(f,o,r),n(d,o,r),$(t.children||[],o,d,i,s,a,l,c)):h>0&&64&h&&p&&e.dynamicChildren?(I(e.dynamicChildren,p,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Si(e,t,!0)):z(e,t,o,d,i,s,a,l,c)},D=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):F(t,n,o,r,i,s,l):U(e,t,l)},F=(e,t,n,o,r,s,a)=>{const l=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||Ji,s={uid:Qi++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new yt(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:si(o,r),emitsOptions:yo(o,r),emit:null,emitted:null,propsDefaults:i,inheritAttrs:o.inheritAttrs,ctx:i,data:i,props:i,attrs:i,slots:i,refs:i,setupState:i,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=mo.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,o,r);if(ar(e)&&(l.ctx.renderer=oe),function(e,t=!1){t&&os(t);const{props:n,children:o}=e.vnode,r=ss(e);(function(e,t,n,o=!1){const r={},i={};N(i,Di,1),e.propsDefaults=Object.create(null),ri(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:En(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),mi(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Bn(new Proxy(e.ctx,Nr));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(Dt(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=rs(e);Ot();const i=Kn(o,e,0,[e.props,n]);if(At(),r(),x(i)){if(i.then(is,is),t)return i.then((n=>{cs(e,n,t)})).catch((t=>{Gn(t,e,0)}));e.asyncDep=i}else cs(e,i,t)}else us(e,t)}(e,t):void 0;t&&os(!1)}(l),l.asyncDep){if(r&&r.registerDep(l,V),!e.el){const e=l.subTree=Vi(ki);w(null,e,t,n)}}else V(l,e,t,n,r,s,a)},U=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||ko(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?ko(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!bo(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void H(o,t,n);o.next=t,function(e){const t=eo.indexOf(e);t>to&&eo.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},V=(e,t,n,o,r,i,s)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:a,vnode:c}=e;{const n=Ti(e);if(n)return t&&(t.el=c.el,H(e,t,s)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,f=t;xi(e,!1),t?(t.el=c.el,H(e,t,s)):t=c,n&&R(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Gi(u,a,t,c),xi(e,!0);const d=To(e),h=e.subTree;e.subTree=d,b(h,d,g(h.el),ee(h),e,r,i),t.el=d.el,null===f&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,d.el),o&&bi(o,r),(u=t.props&&t.props.onVnodeUpdated)&&bi((()=>Gi(u,a,t,c)),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:f}=e,d=rr(t);if(xi(e,!1),c&&R(c),!d&&(s=l&&l.onVnodeBeforeMount)&&Gi(s,f,t),xi(e,!0),a&&ie){const n=()=>{e.subTree=To(e),ie(a,e.subTree,e,r,null)};d?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const s=e.subTree=To(e);b(null,s,n,o,e,r,i),t.el=s.el}if(u&&bi(u,r),!d&&(s=l&&l.onVnodeMounted)){const e=t;bi((()=>Gi(s,f,e)),r)}(256&t.shapeFlag||f&&rr(f.vnode)&&256&f.vnode.shapeFlag)&&(e.ba&&br(e.ba),e.a&&bi(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new wt(l,a,(()=>lo(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,xi(e,!0),u()},H=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Mn(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;ri(e,t,r,i)&&(c=!0);for(const i in a)t&&(p(t,i)||(o=B(i))!==i&&p(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=ii(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&p(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(bo(e.emitsOptions,s))continue;const u=t[s];if(l)if(p(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=P(s);r[t]=ii(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&Ft(e,"set","$attrs")}(e,t.props,o,n),vi(e,t.children,n),Ot(),uo(e),At()},z=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,f=t.children,{patchFlag:d,shapeFlag:p}=t;if(d>0){if(128&d)return void Y(c,f,n,o,r,i,s,a,l);if(256&d)return void W(c,f,n,o,r,i,s,a,l)}8&p?(16&u&&Q(c,r,i),f!==c&&h(n,f)):16&u?16&p?Y(c,f,n,o,r,i,s,a,l):Q(c,r,i,!0):(8&u&&h(n,""),16&p&&$(f,n,o,r,i,s,a,l))},W=(e,t,n,o,r,i,a,l,c)=>{t=t||s;const u=(e=e||s).length,f=t.length,d=Math.min(u,f);let h;for(h=0;h<d;h++){const o=t[h]=c?Xi(t[h]):Yi(t[h]);b(e[h],o,n,null,r,i,a,l,c)}u>f?Q(e,r,i,!0,!1,d):$(t,n,o,r,i,a,l,c,d)},Y=(e,t,n,o,r,i,a,l,c)=>{let u=0;const f=t.length;let d=e.length-1,h=f-1;for(;u<=d&&u<=h;){const o=e[u],s=t[u]=c?Xi(t[u]):Yi(t[u]);if(!Ni(o,s))break;b(o,s,n,null,r,i,a,l,c),u++}for(;u<=d&&u<=h;){const o=e[d],s=t[h]=c?Xi(t[h]):Yi(t[h]);if(!Ni(o,s))break;b(o,s,n,null,r,i,a,l,c),d--,h--}if(u>d){if(u<=h){const e=h+1,s=e<f?t[e].el:o;for(;u<=h;)b(null,t[u]=c?Xi(t[u]):Yi(t[u]),n,s,r,i,a,l,c),u++}}else if(u>h)for(;u<=d;)K(e[u],r,i,!0),u++;else{const p=u,g=u,m=new Map;for(u=g;u<=h;u++){const e=t[u]=c?Xi(t[u]):Yi(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const _=h-g+1;let w=!1,x=0;const S=new Array(_);for(u=0;u<_;u++)S[u]=0;for(u=p;u<=d;u++){const o=e[u];if(y>=_){K(o,r,i,!0);continue}let s;if(null!=o.key)s=m.get(o.key);else for(v=g;v<=h;v++)if(0===S[v-g]&&Ni(o,t[v])){s=v;break}void 0===s?K(o,r,i,!0):(S[s-g]=u+1,s>=x?x=s:w=!0,b(o,t[s],n,null,r,i,a,l,c),y++)}const T=w?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):s;for(v=T.length-1,u=_-1;u>=0;u--){const e=g+u,s=t[e],d=e+1<f?t[e+1].el:o;0===S[u]?b(null,s,n,d,r,i,a,l,c):w&&(v<0||u!==T[v]?X(s,n,d,2):v--)}}},X=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void X(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,oe);if(a===Ei){n(s,t,o);for(let e=0;e<c.length;e++)X(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Oi)return void T(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),bi((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},K=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:d}=e;if(null!=a&&yi(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const h=1&u&&d,p=!rr(e);let g;if(p&&(g=s&&s.onVnodeBeforeUnmount)&&Gi(g,t,e),6&u)J(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);h&&zo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==Ei||f>0&&64&f)?Q(c,t,n,!1,!0):(i===Ei&&384&f||!r&&16&u)&&Q(l,t,n),o&&Z(e)}(p&&(g=s&&s.onVnodeUnmounted)||h)&&bi((()=>{g&&Gi(g,t,e),h&&zo(e,null,t,"unmounted")}),n)},Z=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===Ei)return void G(n,r);if(t===Oi)return void E(e);const s=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,s);o?o(e.el,s,r):r()}else s()},G=(e,t)=>{let n;for(;e!==t;)n=m(e),o(e),e=n;o(t)},J=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&R(o),r.stop(),i&&(i.active=!1,K(s,e,t,n)),a&&bi(a,t),bi((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Q=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)K(e[s],t,n,o,r)},ee=e=>6&e.shapeFlag?ee(e.component.subTree):128&e.shapeFlag?e.suspense.next():m(e.anchor||e.el);let te=!1;const ne=(e,t,n)=>{null==e?t._vnode&&K(t._vnode,null,null,!0):b(t._vnode||null,e,t,null,null,null,n),te||(te=!0,uo(),fo(),te=!1),t._vnode=e},oe={p:b,um:K,m:X,r:Z,mt:F,mc:$,pc:z,pbc:I,n:ee,o:e};let re,ie;return{render:ne,hydrate:re,createApp:ei(ne,re)}}(e)}function wi({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function xi({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Si(e,t,n=!1){const o=e.children,r=t.children;if(g(o)&&g(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=Xi(r[i]),t.el=e.el),n||Si(e,t)),t.type===Ci&&(t.el=e.el)}}function Ti(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ti(t)}const Ei=Symbol.for("v-fgt"),Ci=Symbol.for("v-txt"),ki=Symbol.for("v-cmt"),Oi=Symbol.for("v-stc"),Ai=[];let $i=null;function Pi(e=!1){Ai.push($i=e?null:[])}let Mi=1;function Bi(e){Mi+=e}function Ii(e){return e.dynamicChildren=Mi>0?$i||s:null,Ai.pop(),$i=Ai[Ai.length-1]||null,Mi>0&&$i&&$i.push(e),e}function Li(e,t,n,o,r,i){return Ii(qi(e,t,n,o,r,i,!0))}function ji(e,t,n,o,r){return Ii(Vi(e,t,n,o,r,!0))}function Ri(e){return!!e&&!0===e.__v_isVNode}function Ni(e,t){return e.type===t.type&&e.key===t.key}const Di="__vInternal",Fi=({key:e})=>null!=e?e:null,Ui=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?b(e)||Dn(e)||y(e)?{i:_o,r:e,k:t,f:!!n}:e:null);function qi(e,t=null,n=null,o=0,r=null,i=(e===Ei?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Fi(t),ref:t&&Ui(t),scopeId:wo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:_o};return a?(Ki(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=b(n)?8:16),Mi>0&&!s&&$i&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&$i.push(l),l}const Vi=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==$o||(e=ki);if(Ri(e)){const o=Hi(e,t,!0);return n&&Ki(o,n),Mi>0&&!i&&$i&&(6&o.shapeFlag?$i[$i.indexOf(e)]=o:$i.push(o)),o.patchFlag|=-2,o}s=e,y(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Pn(e)||Di in e?f({},e):e:null}(t);let{class:e,style:n}=t;e&&!b(e)&&(t.class=He(e)),w(n)&&(Pn(n)&&!g(n)&&(n=f({},n)),t.style=Ve(n))}const a=b(e)?1:Io(e)?128:(e=>e.__isTeleport)(e)?64:w(e)?4:y(e)?2:0;return qi(e,t,n,o,r,a,i,!0)};function Hi(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?Zi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Fi(a),ref:t&&t.ref?n&&r?g(r)?r.concat(Ui(t)):[r,Ui(t)]:Ui(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ei?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Hi(e.ssContent),ssFallback:e.ssFallback&&Hi(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function zi(e=" ",t=0){return Vi(Ci,null,e,t)}function Wi(e="",t=!1){return t?(Pi(),ji(ki,null,e)):Vi(ki,null,e)}function Yi(e){return null==e||"boolean"==typeof e?Vi(ki):g(e)?Vi(Ei,null,e.slice()):"object"==typeof e?Xi(e):Vi(Ci,null,String(e))}function Xi(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Hi(e)}function Ki(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(g(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),Ki(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Di in t?3===o&&_o&&(1===_o.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=_o}}else y(t)?(t={default:t,_ctx:_o},n=32):(t=String(t),64&o?(n=16,t=[zi(t)]):n=8);e.children=t,e.shapeFlag|=n}function Zi(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=He([t.class,o.class]));else if("style"===e)t.style=Ve([t.style,o.style]);else if(c(e)){const n=t[e],r=o[e];!r||n===r||g(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function Gi(e,t,n,o=null){Zn(e,t,7,[n,o])}const Ji=Jr();let Qi=0;let es=null;const ts=()=>es||_o;let ns,os;{const e=q(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};ns=t("__VUE_INSTANCE_SETTERS__",(e=>es=e)),os=t("__VUE_SSR_SETTERS__",(e=>ls=e))}const rs=e=>{const t=es;return ns(e),e.scope.on(),()=>{e.scope.off(),ns(t)}},is=()=>{es&&es.scope.off(),ns(null)};function ss(e){return 4&e.vnode.shapeFlag}let as,ls=!1;function cs(e,t,n){y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:w(t)&&(e.setupState=Wn(t)),us(e,n)}function us(e,t,n){const o=e.type;if(!e.render){if(!t&&as&&!o.render){const t=o.template||Hr(e).template;if(t){const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:i,compilerOptions:s}=o,a=f(f({isCustomElement:n,delimiters:i},r),s);o.render=as(t,a)}}e.render=o.render||a}{const t=rs(e);Ot();try{Ur(e)}finally{At(),t()}}}function fs(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Wn(Bn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in jr?jr[n](e):void 0,has:(e,t)=>t in e||t in jr}))}function ds(e,t=!0){return y(e)?e.displayName||e.name:e.name||t&&e.__name}const hs=(e,t)=>{const n=function(e,t,n=!1){let o,r;const i=y(e);return i?(o=e,r=a):(o=e.get,r=e.set),new jn(o,r,i||!r,n)}(e,0,ls);return n};function ps(e,t,n){const o=arguments.length;return 2===o?w(t)&&!g(t)?Ri(t)?Vi(e,null,[t]):Vi(e,t):Vi(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Ri(n)&&(n=[n]),Vi(e,t,n))}const gs="3.4.21",ms="undefined"!=typeof document?document:null,vs=ms&&ms.createElement("template"),ys={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?ms.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?ms.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?ms.createElement(e,{is:n}):ms.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>ms.createTextNode(e),createComment:e=>ms.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ms.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{vs.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=vs.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},bs="transition",_s="animation",ws=Symbol("_vtc"),xs=(e,{slots:t})=>ps(Zo,function(e){const t={};for(const f in e)f in Ss||(t[f]=e[f]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=s,appearToClass:u=a,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(w(e))return[Cs(e.enter),Cs(e.leave)];{const t=Cs(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:_,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:E=b,onAppearCancelled:C=_}=t,k=(e,t,n)=>{Os(e,t?u:a),Os(e,t?c:s),n&&n()},O=(e,t)=>{e._isLeaving=!1,Os(e,d),Os(e,p),Os(e,h),t&&t()},A=e=>(t,n)=>{const r=e?E:b,s=()=>k(t,e,n);Ts(r,[t,s]),As((()=>{Os(t,e?l:i),ks(t,e?u:a),Es(r)||Ps(t,o,m,s)}))};return f(t,{onBeforeEnter(e){Ts(y,[e]),ks(e,i),ks(e,s)},onBeforeAppear(e){Ts(T,[e]),ks(e,l),ks(e,c)},onEnter:A(!1),onAppear:A(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);ks(e,d),document.body.offsetHeight,ks(e,h),As((()=>{e._isLeaving&&(Os(e,d),ks(e,p),Es(x)||Ps(e,o,v,n))})),Ts(x,[e,n])},onEnterCancelled(e){k(e,!1),Ts(_,[e])},onAppearCancelled(e){k(e,!0),Ts(C,[e])},onLeaveCancelled(e){O(e),Ts(S,[e])}})}(e),t);xs.displayName="Transition";const Ss={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};xs.props=f({},Ko,Ss);const Ts=(e,t=[])=>{g(e)?e.forEach((e=>e(...t))):e&&e(...t)},Es=e=>!!e&&(g(e)?e.some((e=>e.length>1)):e.length>1);function Cs(e){return F(e)}function ks(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[ws]||(e[ws]=new Set)).add(t)}function Os(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[ws];n&&(n.delete(t),n.size||(e[ws]=void 0))}function As(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let $s=0;function Ps(e,t,n,o){const r=e._endId=++$s,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${bs}Delay`),i=o(`${bs}Duration`),s=Ms(r,i),a=o(`${_s}Delay`),l=o(`${_s}Duration`),c=Ms(a,l);let u=null,f=0,d=0;t===bs?s>0&&(u=bs,f=s,d=i.length):t===_s?c>0&&(u=_s,f=c,d=l.length):(f=Math.max(s,c),u=f>0?s>c?bs:_s:null,d=u?u===bs?i.length:l.length:0);const h=u===bs&&/\b(transform|all)(,|$)/.test(o(`${bs}Property`).toString());return{type:u,timeout:f,propCount:d,hasTransform:h}}(e,t);if(!s)return o();const c=s+"end";let u=0;const f=()=>{e.removeEventListener(c,d),i()},d=t=>{t.target===e&&++u>=l&&f()};setTimeout((()=>{u<l&&f()}),a+1),e.addEventListener(c,d)}function Ms(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Bs(t)+Bs(e[n]))))}function Bs(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Is=Symbol("_vod"),Ls=Symbol("_vsh"),js={beforeMount(e,{value:t},{transition:n}){e[Is]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Rs(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Rs(e,!0),o.enter(e)):o.leave(e,(()=>{Rs(e,!1)})):Rs(e,t))},beforeUnmount(e,{value:t}){Rs(e,t)}};function Rs(e,t){e.style.display=t?e[Is]:"none",e[Ls]=!t}const Ns=Symbol(""),Ds=/(^|;)\s*display\s*:/;const Fs=/\s*!important$/;function Us(e,t,n){if(g(n))n.forEach((n=>Us(e,t,n)));else if(null==n&&(n=""),n=Gs(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Vs[t];if(n)return n;let o=P(t);if("filter"!==o&&o in e)return Vs[t]=o;o=I(o);for(let r=0;r<qs.length;r++){const n=qs[r]+o;if(n in e)return Vs[t]=n}return t}(e,t);Fs.test(n)?e.setProperty(B(o),n.replace(Fs,""),"important"):e[o]=n}}const qs=["Webkit","Moz","ms"],Vs={};const{unit:Hs,unitRatio:zs,unitPrecision:Ws}={unit:"rem",unitRatio:10/320,unitPrecision:5},Ys=(Xs=Hs,Ks=zs,Zs=Ws,e=>e.replace(Je,((e,t)=>{if(!t)return e;if(1===Ks)return`${t}${Xs}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*Ks,Zs);return 0===n?"0":`${n}${Xs}`})));var Xs,Ks,Zs;const Gs=e=>b(e)?Ys(e):e,Js="http://www.w3.org/1999/xlink";const Qs=Symbol("_vei");function ea(e,t,n,o,r=null){const i=e[Qs]||(e[Qs]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(ta.test(e)){let n;for(t={};n=e.match(ta);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):B(e.slice(2));return[n,t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&g(i)){const n=ia(e,i);for(let o=0;o<n.length;o++){const i=n[o];Zn(i,t,5,i.__wwe?[e]:r(e))}}else Zn(ia(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=ra(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const ta=/(?:Once|Passive|Capture)$/;let na=0;const oa=Promise.resolve(),ra=()=>na||(oa.then((()=>na=0)),na=Date.now());function ia(e,t){if(g(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const sa=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const aa=["ctrl","shift","alt","meta"],la={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>aa.some((n=>e[`${n}Key`]&&!t.includes(n)))},ca=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=la[t[e]];if(o&&o(n,t))return}return e(n,...o)})},ua=f({patchProp:(e,t,n,o,r,i,s,a,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;ao((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const f="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[ws];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,f):"style"===t?function(e,t,n){const o=e.style,r=b(n);let i=!1;if(n&&!r){if(t)if(b(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Us(o,t,"")}else for(const e in t)null==n[e]&&Us(o,e,"");for(const e in n)"display"===e&&(i=!0),Us(o,e,n[e])}else if(r){if(t!==n){const e=o[Ns];e&&(n+=";"+e),o.cssText=n,i=Ds.test(n)}}else t&&e.removeAttribute("style");Is in e&&(e[Is]=i?o.display:"",e[Ls]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)Us(o,a,s[a])}(e,n,o):c(t)?u(t)||ea(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&sa(t)&&y(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(sa(t)&&b(n))return!1;return t in e}(e,t,o,f))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=Z(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,a,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Js,t.slice(6,t.length)):e.setAttributeNS(Js,t,n);else{const o=K(t);null==n||o&&!Z(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,f))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},ys);let fa;const da=(...e)=>{const t=(fa||(fa=_i(ua))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(b(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.4.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;y(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const ha="undefined"!=typeof document;const pa=Object.assign;function ga(e,t){const n={};for(const o in t){const r=t[o];n[o]=va(r)?r.map(e):e(r)}return n}const ma=()=>{},va=Array.isArray,ya=/#/g,ba=/&/g,_a=/\//g,wa=/=/g,xa=/\?/g,Sa=/\+/g,Ta=/%5B/g,Ea=/%5D/g,Ca=/%5E/g,ka=/%60/g,Oa=/%7B/g,Aa=/%7C/g,$a=/%7D/g,Pa=/%20/g;function Ma(e){return encodeURI(""+e).replace(Aa,"|").replace(Ta,"[").replace(Ea,"]")}function Ba(e){return Ma(e).replace(Sa,"%2B").replace(Pa,"+").replace(ya,"%23").replace(ba,"%26").replace(ka,"`").replace(Oa,"{").replace($a,"}").replace(Ca,"^")}function Ia(e){return null==e?"":function(e){return Ma(e).replace(ya,"%23").replace(xa,"%3F")}(e).replace(_a,"%2F")}function La(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const ja=/\/$/,Ra=e=>e.replace(ja,"");function Na(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:La(s)}}function Da(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Fa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ua(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!qa(e[n],t[n]))return!1;return!0}function qa(e,t){return va(e)?Va(e,t):va(t)?Va(t,e):e===t}function Va(e,t){return va(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}const Ha={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var za,Wa,Ya,Xa;(Wa=za||(za={})).pop="pop",Wa.push="push",(Xa=Ya||(Ya={})).back="back",Xa.forward="forward",Xa.unknown="";const Ka=/^[^#]+#/;function Za(e,t){return e.replace(Ka,"#")+t}const Ga=()=>({left:window.scrollX,top:window.scrollY});function Ja(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Qa(e,t){return(history.state?history.state.position-t:-1)+e}const el=new Map;let tl=()=>location.protocol+"//"+location.host;function nl(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Da(n,"")}return Da(n,e)+o+r}function ol(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?Ga():null}}function rl(e){const t=function(e){const{history:t,location:n}=window,o={value:nl(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:tl()+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=pa({},r.value,t.state,{forward:e,scroll:Ga()});i(s.current,s,!0),i(e,pa({},ol(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,pa({},t.state,ol(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}(e=function(e){if(!e)if(ha){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),Ra(e)}(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=nl(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:za.pop,direction:u?u>0?Ya.forward:Ya.back:Ya.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(pa({},e.state,{scroll:Ga()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=pa({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Za.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function il(e){return"string"==typeof e||"symbol"==typeof e}const sl=Symbol("");var al,ll;function cl(e,t){return pa(new Error,{type:e,[sl]:!0},t)}function ul(e,t){return e instanceof Error&&sl in e&&(null==t||!!(e.type&t))}(ll=al||(al={}))[ll.aborted=4]="aborted",ll[ll.cancelled=8]="cancelled",ll[ll.duplicated=16]="duplicated";const fl="[^/]+?",dl={sensitive:!1,strict:!1,start:!0,end:!0},hl=/[.+*?^${}()[\]/\\]/g;function pl(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function gl(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=pl(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(ml(o))return 1;if(ml(r))return-1}return r.length-o.length}function ml(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const vl={type:0,value:""},yl=/[a-zA-Z0-9_]/;function bl(e,t,n){const o=function(e,t){const n=pa({},dl,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(hl,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const f=u||fl;if(f!==fl){s+=10;try{new RegExp(`(${f})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+a.message)}}let d=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(d=c&&l.length<2?`(?:/${d})`:"/"+d),c&&(d+="?"),r+=d,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===f&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(va(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=va(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[vl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function f(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&f(),s()):":"===a?(f(),n=1):d();break;case 4:d(),n=o;break;case 1:"("===a?n=2:yl.test(a)?d():(f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),s(),r}(e.path),n),r=pa(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function _l(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:xl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=El(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(pa({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let f,d;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(f=bl(t,n,c),o?o.alias.push(f):(d=d||f,d!==f&&d.alias.push(f),a&&e.name&&!Sl(f)&&i(e.name)),Cl(f)&&s(f),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],f,o&&o.children[t])}o=o||f}return d?()=>{i(d)}:ma}function i(e){if(il(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){const t=function(e,t){let n=0,o=t.length;for(;n!==o;){const r=n+o>>1;gl(e,t[r])<0?o=r:n=r+1}const r=function(e){let t=e;for(;t=t.parent;)if(Cl(t)&&0===gl(e,t))return t;return}(e);r&&(o=t.lastIndexOf(r,o-1));return o}(e,n);n.splice(t,0,e),e.record.name&&!Sl(e)&&o.set(e.record.name,e)}return t=El({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw cl(1,{location:e});s=r.record.name,a=pa(wl(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&wl(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw cl(1,{location:e,currentLocation:t});s=r.record.name,a=pa({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Tl(l)}},removeRoute:i,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function wl(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function xl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Sl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Tl(e){return e.reduce(((e,t)=>pa(e,t.meta)),{})}function El(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Cl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function kl(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Sa," "),r=e.indexOf("="),i=La(r<0?e:e.slice(0,r)),s=r<0?null:La(e.slice(r+1));if(i in t){let e=t[i];va(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Ol(e){let t="";for(let n in e){const o=e[n];if(n=Ba(n).replace(wa,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(va(o)?o.map((e=>e&&Ba(e))):[o&&Ba(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Al(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=va(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const $l=Symbol(""),Pl=Symbol(""),Ml=Symbol(""),Bl=Symbol(""),Il=Symbol("");function Ll(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function jl(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(cl(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(cl(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch((e=>l(e)))}))}function Rl(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(jl(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&jl(c,n,o,a,e,r)()}))))}}var s;return i}function Nl(e){const t=oi(Ml),n=oi(Bl),o=hs((()=>{const n=Hn(e.to);return t.resolve(n)})),r=hs((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Fa.bind(null,r));if(s>-1)return s;const a=Fl(e[t-2]);return t>1&&Fl(r)===a&&i[i.length-1].path!==a?i.findIndex(Fa.bind(null,e[t-2])):s})),i=hs((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!va(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=hs((()=>r.value>-1&&r.value===n.matched.length-1&&Ua(n.params,o.value.params)));return{route:o,href:hs((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[Hn(e.replace)?"replace":"push"](Hn(e.to)).catch(ma):Promise.resolve()}}}const Dl=or({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Nl,setup(e,{slots:t}){const n=Tn(Nl(e)),{options:o}=oi(Ml),r=hs((()=>({[Ul(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Ul(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:ps("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Fl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ul=(e,t,n)=>null!=e?e:null!=t?t:n;function ql(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Vl=or({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=oi(Il),r=hs((()=>e.route||o.value)),i=oi(Pl,0),s=hs((()=>{let e=Hn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=hs((()=>r.value.matched[s.value]));ni(Pl,hs((()=>s.value+1))),ni($l,a),ni(Il,r);const l=Fn();return Do((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Fa(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return ql(n.default,{Component:c,route:o});const u=s.props[i],f=u?!0===u?o.params:"function"==typeof u?u(o):u:null,d=ps(c,pa({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return ql(n.default,{Component:d,route:o})||d}}});function Hl(e){const t=_l(e.routes,e),n=e.parseQuery||kl,o=e.stringifyQuery||Ol,r=e.history,i=Ll(),s=Ll(),a=Ll(),l=Un(Ha);let c=Ha;ha&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ga.bind(null,(e=>""+e)),f=ga.bind(null,Ia),d=ga.bind(null,La);function h(e,i){if(i=pa({},i||l.value),"string"==typeof e){const o=Na(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return pa(o,s,{params:d(s.params),hash:La(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=pa({},e,{path:Na(n,e.path,i.path).path});else{const t=pa({},e.params);for(const e in t)null==t[e]&&delete t[e];s=pa({},e,{params:f(t)}),i.params=f(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(d(a.params));const h=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,pa({},e,{hash:(p=c,Ma(p).replace(Oa,"{").replace($a,"}").replace(Ca,"^")),path:a.path}));var p;const g=r.createHref(h);return pa({fullPath:h,hash:c,query:o===Ol?Al(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function p(e){return"string"==typeof e?Na(n,e,l.value.path):pa({},e)}function g(e,t){if(c!==e)return cl(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=p(o):{path:o},o.params={}),pa({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=h(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(pa(p(u),{state:"object"==typeof u?pa({},i,u.state):i,force:s,replace:a}),t||n);const f=n;let d;return f.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Fa(t.matched[o],n.matched[r])&&Ua(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(d=cl(16,{to:f,from:r}),P(r,r,!0,!1)),(d?Promise.resolve(d):w(f,r)).catch((e=>ul(e)?ul(e,2)?e:$(e):A(e,f,r))).then((e=>{if(e){if(ul(e,2))return y(pa({replace:a},p(e.to),{state:"object"==typeof e.to?pa({},i,e.to.state):i,force:s}),t||f)}else e=S(f,r,!0,a,i);return x(f,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=I.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>Fa(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>Fa(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Rl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(jl(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),j(n).then((()=>{n=[];for(const o of i.list())n.push(jl(o,e,t));return n.push(l),j(n)})).then((()=>{n=Rl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(jl(o,e,t))}));return n.push(l),j(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(va(o.beforeEnter))for(const r of o.beforeEnter)n.push(jl(r,e,t));else n.push(jl(o.beforeEnter,e,t));return n.push(l),j(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Rl(a,"beforeRouteEnter",e,t,_),n.push(l),j(n)))).then((()=>{n=[];for(const o of s.list())n.push(jl(o,e,t));return n.push(l),j(n)})).catch((e=>ul(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>_((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===Ha,c=ha?history.state:{};n&&(o||a?r.replace(e.fullPath,pa({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,P(e,t,n,a),$()}let T;function E(){T||(T=r.listen(((e,t,n)=>{if(!L.listening)return;const o=h(e),i=v(o);if(i)return void y(pa(i,{replace:!0}),o).catch(ma);c=o;const s=l.value;var a,u;ha&&(a=Qa(s.fullPath,n.delta),u=Ga(),el.set(a,u)),w(o,s).catch((e=>ul(e,12)?e:ul(e,2)?(y(e.to,o).then((e=>{ul(e,20)&&!n.delta&&n.type===za.pop&&r.go(-1,!1)})).catch(ma),Promise.reject()):(n.delta&&r.go(-n.delta,!1),A(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!ul(e,8)?r.go(-n.delta,!1):n.type===za.pop&&ul(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(ma)})))}let C,k=Ll(),O=Ll();function A(e,t,n){$(e);const o=O.list();return o.length&&o.forEach((o=>o(e,t,n))),Promise.reject(e)}function $(e){return C||(C=!e,E(),k.list().forEach((([t,n])=>e?n(e):t())),k.reset()),e}function P(t,n,o,r){const{scrollBehavior:i}=e;if(!ha||!i)return Promise.resolve();const s=!o&&function(e){const t=el.get(e);return el.delete(e),t}(Qa(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return ao().then((()=>i(t,n,s))).then((e=>e&&Ja(e))).catch((e=>A(e,t,n)))}const M=e=>r.go(e);let B;const I=new Set,L={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return il(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:h,options:e,push:m,replace:function(e){return m(pa(p(e),{replace:!0}))},go:M,back:()=>M(-1),forward:()=>M(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:O.add,isReady:function(){return C&&l.value!==Ha?Promise.resolve():new Promise(((e,t)=>{k.add([e,t])}))},install(e){e.component("RouterLink",Dl),e.component("RouterView",Vl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Hn(l)}),ha&&!B&&l.value===Ha&&(B=!0,m(r.location).catch((e=>{})));const t={};for(const o in Ha)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Ml,this),e.provide(Bl,En(t)),e.provide(Il,l);const n=e.unmount;I.add(e),e.unmount=function(){I.delete(e),I.size<1&&(c=Ha,T&&T(),T=null,l.value=Ha,B=!1,C=!1),n()}}};function j(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return L}function zl(e){return oi(Bl)}const Wl=["{","}"];const Yl=/^(?:\d)+/,Xl=/^(?:\w)+/;const Kl="zh-Hans",Zl="zh-Hant",Gl="en",Jl="fr",Ql="es",ec=Object.prototype.hasOwnProperty,tc=(e,t)=>ec.call(e,t),nc=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Wl){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=Yl.test(t)?"list":a&&Xl.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function oc(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Kl;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Kl:e.indexOf("-hant")>-1?Zl:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?Zl:Kl);var n;let o=[Gl,Jl,Ql];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class rc{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale=Gl,this.fallbackLocale=Gl,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||nc,this.messages=n||{},this.setLocale(e||Gl),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=oc(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{tc(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=oc(t,this.messages))&&(o=this.messages[t]):n=t,tc(o,e)?this.formater.interpolate(o[e],n).join(""):e}}function ic(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Id?Id():"undefined"!=typeof global&&global.getLocale?global.getLocale():Gl),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Gl);const r=new rc({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Ug().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function sc(e,t){return e.indexOf(t[0])>-1}const ac=Xe((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let lc;function cc(e){return sc(e,ae)?dc().f(e,function(){const e=Id(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ae):e}function uc(e,t){if(1===t.length){if(e){const n=e=>b(e)&&sc(e,ae),o=t[0];let r=[];if(g(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return uc(e&&e[n],t)}function fc(e,t){const n=uc(e,t);if(!n)return!1;const o=t[t.length-1];if(g(n))n.forEach((e=>fc(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>cc(e),set(t){e=t}})}return!0}function dc(){if(!lc){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage[se]||__uniConfig.locale||navigator.language,lc=ic(e),ac()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>lc.add(e,__uniConfig.locales[e]))),lc.setLocale(e)}}return lc}function hc(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const pc=Xe((()=>{const e="uni.async.",t=["error"];dc().add(Gl,hc(e,t,["The connection timed out, click the screen to try again."]),!1),dc().add(Ql,hc(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),dc().add(Jl,hc(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),dc().add(Kl,hc(e,t,["连接服务器超时，点击屏幕重试"]),!1),dc().add(Zl,hc(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),gc=Xe((()=>{const e="uni.showToast.",t=["unpaired"];dc().add(Gl,hc(e,t,["Please note showToast must be paired with hideToast"]),!1),dc().add(Ql,hc(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),dc().add(Jl,hc(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),dc().add(Kl,hc(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),dc().add(Zl,hc(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),mc=Xe((()=>{const e="uni.showLoading.",t=["unpaired"];dc().add(Gl,hc(e,t,["Please note showLoading must be paired with hideLoading"]),!1),dc().add(Ql,hc(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),dc().add(Jl,hc(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),dc().add(Kl,hc(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),dc().add(Zl,hc(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),vc=Xe((()=>{const e="uni.showModal.",t=["cancel","confirm"];dc().add(Gl,hc(e,t,["Cancel","OK"]),!1),dc().add(Ql,hc(e,t,["Cancelar","OK"]),!1),dc().add(Jl,hc(e,t,["Annuler","OK"]),!1),dc().add(Kl,hc(e,t,["取消","确定"]),!1),dc().add(Zl,hc(e,t,["取消","確定"]),!1)})),yc=Xe((()=>{const e="uni.chooseFile.",t=["notUserActivation"];dc().add(Gl,hc(e,t,["File chooser dialog can only be shown with a user activation"]),!1),dc().add(Ql,hc(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),dc().add(Jl,hc(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),dc().add(Kl,hc(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),dc().add(Zl,hc(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),bc=Xe((()=>{const e="uni.video.",t=["danmu","volume"];dc().add(Gl,hc(e,t,["Danmu","Volume"]),!1),dc().add(Ql,hc(e,t,["Danmu","Volumen"]),!1),dc().add(Jl,hc(e,t,["Danmu","Le Volume"]),!1),dc().add(Kl,hc(e,t,["弹幕","音量"]),!1),dc().add(Zl,hc(e,t,["彈幕","音量"]),!1)}));function _c(e){const t=new dt;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}const wc="invokeViewApi",xc="invokeServiceApi";let Sc=1;const Tc=Object.create(null);function Ec(e,t){return e+"."+t}function Cc(e,t,n){t=Ec(e,t),Tc[t]||(Tc[t]=n)}function kc({id:e,name:t,args:n},o){t=Ec(o,t);const r=t=>{e&&qv.publishHandler(wc+"."+e,t)},i=Tc[t];i?i(n,r):r({})}const Oc=f(_c("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=qv,i=n?Sc++:0;n&&o(xc+"."+i,n,!0),r(xc,{id:i,name:e,args:t})}}),Ac=350,$c=10,Pc=Qe(!0);let Mc;function Bc(){Mc&&(clearTimeout(Mc),Mc=null)}let Ic=0,Lc=0;function jc(e){if(Bc(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Ic=t,Lc=n,Mc=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),Ac)}function Rc(e){if(!Mc)return;if(1!==e.touches.length)return Bc();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Ic)>$c||Math.abs(n-Lc)>$c?Bc():void 0}function Nc(e,t){const n=Number(e);return isNaN(n)?t:n}function Dc(){const e=__uniConfig.globalStyle||{},t=Nc(e.rpxCalcMaxDeviceWidth,960),n=Nc(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Fc(){Dc(),Ze(),window.addEventListener("touchstart",jc,Pc),window.addEventListener("touchmove",Rc,Pc),window.addEventListener("touchend",Bc,Pc),window.addEventListener("touchcancel",Bc,Pc)}function Uc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var qc,Vc,Hc=["top","left","right","bottom"],zc={};function Wc(){return Vc="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Yc(){if(Vc="string"==typeof Vc?Vc:Wc()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Hc.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),qc=!0}else Hc.forEach((function(e){zc[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Vc+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Kc.length||setTimeout((function(){var e={};Kc.forEach((function(t){e[t]=zc[t]})),Kc.length=0,Zc.forEach((function(t){t(e)}))}),0);Kc.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(zc,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Xc(e){return qc||Yc(),zc[e]}var Kc=[];var Zc=[];const Gc=Uc({get support(){return 0!=("string"==typeof Vc?Vc:Wc()).length},get top(){return Xc("top")},get left(){return Xc("left")},get right(){return Xc("right")},get bottom(){return Xc("bottom")},onChange:function(e){Wc()&&(qc||Yc(),"function"==typeof e&&Zc.push(e))},offChange:function(e){var t=Zc.indexOf(e);t>=0&&Zc.splice(t,1)}}),Jc=ca((()=>{}),["prevent"]),Qc=ca((e=>{}),["stop"]);function eu(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function tu(){const e=eu(document.documentElement.style,"--window-top");return e?e+Gc.top:0}function nu(){const e=document.documentElement.style,t=tu(),n=eu(e,"--window-bottom"),o=eu(e,"--window-left"),r=eu(e,"--window-right"),i=eu(e,"--top-window-height");return{top:t,bottom:n?n+Gc.bottom:0,left:o?o+Gc.left:0,right:r?r+Gc.right:0,topWindowHeight:i||0}}function ou(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function ru(e){return ou(e)}function iu(e){return Symbol(e)}function su(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function au(e,t=!1){if(t)return function(e){if(!su(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>fd(parseFloat(t))+"px"))}(e);if(b(e)){const t=parseInt(e)||0;return su(e)?fd(t):t}return e}const lu="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",cu="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function uu(e,t="#000",n=27){return Vi("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Vi("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function fu(){{const{$pageInstance:e}=ts();return e&&e.proxy.$page.id}}function du(){const e=mg(),t=e.length;if(t)return e[t-1]}function hu(){const e=du();if(e)return e.$page.meta}function pu(){const e=hu();return e?e.id:-1}function gu(){const e=du();if(e)return e.$vm}const mu=["navigationBar","pullToRefresh"];function vu(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=f({id:t},n,e);mu.forEach((t=>{o[t]=f({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function yu(e,t,n){if(b(e))n=t,t=e,e=gu();else if("number"==typeof e){const t=mg().find((t=>t.$page.id===e));e=t?t.$vm:gu()}if(!e)return;const o=e.$[t];return o&&Ye(o,n)}function bu(e){e.preventDefault()}let _u,wu=0;function xu({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-wu)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(wu=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(_u=setTimeout(s,300))),o=!1};return function(){clearTimeout(_u),o||requestAnimationFrame(s),o=!0}}function Su(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Su(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),We(i.concat(n).join("/"))}function Tu(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Eu{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(qe(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&qe(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Au(this.$el.querySelector(e));return t?Cu(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Au(n[o]);e&&t.push(Cu(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||b(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:B(n);(b(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(b(e)&&(e=Y(e)),C(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];y(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&qv.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Cu(e,t=!0){if(t&&e&&(e=Ue(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Eu(e)),e.$el.__wxsComponentDescriptor}function ku(e,t){return Cu(e,t)}function Ou(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>ku(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=Ue(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,ku(r,!1)]}}function Au(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function $u(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=et(t?r:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(r)),a=et(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){f(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Pu(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Mu(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Bu=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return Ou(e,t,n,!1)||[e];const i=$u(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=tu();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Pu(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=tu();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Pu(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=tu();i.touches=Mu(e.touches,t),i.changedTouches=Mu(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Ou(i,t,n)||[i]},createNativeEvent:$u},Symbol.toStringTag,{value:"Module"});function Iu(e){!function(e){const t=e.globalProperties;f(t,Bu),t.$gcd=ku}(e._context.config)}let Lu=1;function ju(e){return(e||pu())+"."+wc}const Ru=f(_c("view"),{invokeOnCallback:(e,t)=>Vv.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Vv,s=o?Lu++:0;o&&r(wc+"."+s,o,!0),i(ju(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Vv,a=Lu++,l=wc+"."+a;return r(l,n),s(ju(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Nu(e){yu(du(),xe,e),Vv.invokeOnCallback("onWindowResize",e)}function Du(e){const t=du();yu(Ug(),fe,e),yu(t,fe)}function Fu(){yu(Ug(),de),yu(du(),de)}const Uu=[Te,Ce];function qu(){Uu.forEach((e=>Vv.subscribe(e,function(e){return(t,n)=>{yu(parseInt(n),e,t)}}(e))))}function Vu(){!function(){const{on:e}=Vv;e(xe,Nu),e(Re,Du),e(Ne,Fu)}(),qu()}function Hu(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new st(this.$page.id)),e.eventChannel}}function zu(e){e._context.config.globalProperties.getOpenerEventChannel=Hu}function Wu(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function Yu(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${fd(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function Xu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(Yu)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?Yu(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const Ku={props:["animation"],watch:{animation:{deep:!0,handler(){Xu(this)}}},mounted(){Xu(this)}},Zu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Ku),Gu(e)},Gu=e=>(e.__reserved=!0,e.compatConfig={MODE:3},or(e));function Ju(e){return e.__wwe=!0,e}function Qu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=et(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const ef={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function tf(e){const t=Fn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:Ju((function(e){e.touches.length>1||s(e)})),onMousedown:Ju((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:Ju((function(){a()})),onMouseup:Ju((function(){r&&l()})),onTouchcancel:Ju((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function nf(e,t){return b(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const of=iu("uf"),rf=iu("ul");function sf(e,t,n){const o=fu();n&&!e||C(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&qv.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?qv.on(r,t[r]):e&&qv.on(`uni-${r}-${o}-${e}`,t[r])}))}function af(e,t,n){const o=fu();n&&!e||C(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&qv.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?qv.off(r,t[r]):e&&qv.off(`uni-${r}-${o}-${e}`,t[r])}))}const lf=Zu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Fn(null),o=oi(of,!1),{hovering:r,binding:i}=tf(e),s=Ju(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=oi(rf,!1);return a&&(a.addHandler(s),Cr((()=>{a.removeHandler(s)}))),function(e,t){sf(e.id,t),Do((()=>e.id),((e,n)=>{af(n,t,!0),sf(e,t,!0)})),kr((()=>{af(e.id,t)}))}(e,{"label-click":s}),()=>{const o=e.hoverClass,a=nf(e,"disabled"),l=nf(e,"loading"),c=nf(e,"plain"),u=o&&"none"!==o;return Vi("uni-button",Zi({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}});function cf(e){const{base:t}=__uniConfig.router;return 0===We(e).indexOf(t)?We(e):t+e}function uf(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return cf(e.slice(1));e="https:"+e}if(le.test(e)||ce.test(e)||0===e.indexOf("blob:"))return e;const o=mg();return o.length?cf(Su(o[o.length-1].$page.route,e).slice(1)):e}const ff=navigator.userAgent,df=/android/i.test(ff),hf=/iphone|ipad|ipod/i.test(ff),pf=ff.match(/Windows NT ([\d|\d.\d]*)/i),gf=/Macintosh|Mac/i.test(ff),mf=/Linux|X11/i.test(ff),vf=gf&&navigator.maxTouchPoints>0;function yf(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function bf(e){return e&&90===Math.abs(window.orientation)}function _f(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function wf(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function xf(e,t,n,o){Vv.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Sf(e,t){const n={},{top:o,topWindowHeight:r}=nu();if(t.node){const t=e.tagName.split("-")[1];t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=Ge(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(g(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(g(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Tf(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function Ef(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):Tf(i,n)?i:i.querySelector(n);return e?Sf(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(Sf(t,r))})),!l&&Tf(i,n)&&e.unshift(Sf(i,r)),e}}(e,t,n,r,i))})),n(o)}const Cf=["original","compressed"],kf=["album","camera"],Of=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Af(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function $f(e,t){return!g(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Pf(e){return function(){try{return e.apply(e,arguments)}catch(t){}}}let Mf=1;const Bf={};function If(e,t,n){if("number"==typeof e){const o=Bf[e];if(o)return o.keepAlive||delete Bf[e],o.callback(t,n)}return t}const Lf="success",jf="fail",Rf="complete";function Nf(e,t={},{beforeAll:n,beforeSuccess:o}={}){C(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];y(o)&&(t[n]=Pf(o),delete e[n])}return t}(t),a=y(r),l=y(i),c=y(s),u=Mf++;return function(e,t,n,o=!1){Bf[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),y(n)&&n(u),u.errMsg===e+":ok"?(y(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Df="success",Ff="fail",Uf="complete",qf={},Vf={};function Hf(e,t){return function(n){return e(n,t)||n}}function zf(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Hf(i,n));else{const e=i(t,n);if(x(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Wf(e,t={}){return[Df,Ff,Uf].forEach((n=>{const o=e[n];if(!g(o))return;const r=t[n];t[n]=function(e){zf(o,e,t).then((e=>y(r)&&r(e)||e))}})),t}function Yf(e,t){const n=[];g(qf.returnValue)&&n.push(...qf.returnValue);const o=Vf[e];return o&&g(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Xf(e){const t=Object.create(null);Object.keys(qf).forEach((e=>{"returnValue"!==e&&(t[e]=qf[e].slice())}));const n=Vf[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Kf(e,t,n,o){const r=Xf(e);if(r&&Object.keys(r).length){if(g(r.invoke)){return zf(r.invoke,n).then((n=>t(Wf(Xf(e),n),...o)))}return t(Wf(r,n),...o)}return t(n,...o)}function Zf(e,t){return(n={},...o)=>function(e){return!(!C(e)||![Lf,jf,Rf].find((t=>y(e[t]))))}(n)?Yf(e,Kf(e,t,n,o)):Yf(e,new Promise(((r,i)=>{Kf(e,t,f(n,{success:r,fail:i}),o)})))}function Gf(e,t,n,o={}){const r=t+":fail"+(n?" "+n:"");return delete o.errCode,If(e,f({errMsg:r},o))}function Jf(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(b(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!C(t.formatArgs)&&C(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(y(s)){const o=s(e[0][t],n);if(b(o))return o}else p(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Qf(e,t,n,o){return n=>{const r=Nf(e,n,o),i=Jf(0,[n],0,o);return i?Gf(r,e,i):t(n,{resolve:t=>function(e,t,n){return If(e,f(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Gf(r,e,function(e){return!e||b(e)?e:e.stack?e.message:e}(t),n)})}}function ed(e,t,n,o){return Zf(e,Qf(e,t,0,o))}function td(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Jf(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function nd(e,t,n,o){return Zf(e,function(e,t,n,o){return Qf(e,t,0,o)}(e,t,0,o))}let od=!1,rd=0,id=0,sd=960,ad=375,ld=750;function cd(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=yf(),t=wf(_f(e,bf(e)));return{platform:hf?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();rd=n,id=t,od="ios"===e}function ud(e,t){const n=Number(e);return isNaN(n)?t:n}const fd=td(0,((e,t)=>{if(0===rd&&(cd(),function(){const e=__uniConfig.globalStyle||{};sd=ud(e.rpxCalcMaxDeviceWidth,960),ad=ud(e.rpxCalcBaseDeviceWidth,375),ld=ud(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||rd;n=e===ld||n<=sd?n:ad;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==id&&od?.5:1),e<0?-o:o})),dd=new dt,hd=td(0,((e,...t)=>{dd.emit(e,...t)})),pd=[.5,.8,1,1.25,1.5,2];const gd=(e,t,n,o)=>{!function(e,t,n,o,r){Vv.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};const md={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function vd(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(p(md,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(md[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return[0,0,0,255]}class yd{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,vd(t)])}}class bd{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class _d{constructor(e){this.width=e}}let wd=0,xd={};function Sd(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(wd++);r.callbackId=e,xd[e]=o}Vv.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},b(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?y(e.success)&&e.success(t):y(e.fail)&&e.fail(t),y(e.complete)&&e.complete(t)}(xd[e],t),delete xd[e])}))}const Td={canvas:class{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){}setStrokeStyle(e){}setShadow(e,t,n,o){}addColorStop(e,t){}setLineWidth(e){}setLineCap(e){}setLineJoin(e){}setLineDash(e,t){}setMiterLimit(e){}fillRect(e,t,n,o){}strokeRect(e,t,n,o){}clearRect(e,t,n,o){}fill(){}stroke(){}scale(e,t){}rotate(e){}translate(e,t){}setFontSize(e){}fillText(e,t,n,o){}setTextAlign(e){}setTextBaseline(e){}drawImage(e,t,n,o,r,i,s,a,l){}setGlobalAlpha(e){}strokeText(e,t,n,o){}setTransform(e,t,n,o,r,i){}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],function(e,t,n,o,r){Vv.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new yd("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new yd("radial",[e,t,n])}createPattern(e,t){if(void 0===t);else if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new bd(e,t)}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new _d(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}},map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){gd(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){gd(this.id,this.pageId,"moveToLocation",e)}getScale(e){gd(this.id,this.pageId,"getScale",e)}getRegion(e){gd(this.id,this.pageId,"getRegion",e)}includePoints(e){gd(this.id,this.pageId,"includePoints",e)}translateMarker(e){gd(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){gd(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){gd(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){gd(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){gd(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){gd(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){gd(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){gd(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){gd(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){gd(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){gd(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){gd(this.id,this.pageId,"openMapApp",e)}on(e,t){gd(this.id,this.pageId,"on",{name:e,callback:t})}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){xf(this.id,this.pageId,"play")}pause(){xf(this.id,this.pageId,"pause")}stop(){xf(this.id,this.pageId,"stop")}seek(e){xf(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){xf(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~pd.indexOf(e)||(e=1),xf(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){xf(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){xf(this.id,this.pageId,"exitFullScreen")}showStatusBar(){xf(this.id,this.pageId,"showStatusBar")}hideStatusBar(){xf(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){Sd(this.id,this.pageId,e,t)}}};function Ed(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=Td[n];e.context=new r(t,o),delete e.contextInfo}}class Cd{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class kd{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return Ef(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{g(e)?e.forEach(Ed):Ed(e);const o=n[t];y(o)&&o.call(this,e)})),y(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=Fe(e),this}select(e){return this._nodesRef=new Cd(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Cd(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Cd(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const Od=td(0,(e=>((e=Fe(e))&&!function(e){const t=Fe(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}(e)&&(e=null),new kd(e||gu())))),Ad={formatArgs:{}},$d={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Pd{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=f({},$d,e)}_getOption(e){const t={transition:f({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const Md=Xe((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Pd.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Bd=td(0,(e=>(Md(),new Pd(e))),0,Ad),Id=td(0,(()=>{const e=Ug();return e&&e.$vm?e.$vm.$locale:dc().getLocale()})),Ld={[ve]:[],[me]:[],[pe]:[],[fe]:[],[de]:[]};const jd={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=$f(e,Cf)},sourceType(e,t){t.sourceType=$f(e,kf)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Rd={formatArgs:{sourceType(e,t){t.sourceType=$f(e,kf)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Nd=(Boolean,["all","image","video"]),Dd={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=$f(e,kf)},type(e,t){t.type=Af(e,Nd)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},Fd={formatArgs:{urls(e,t){t.urls=e.map((e=>b(e)&&e?uf(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:b(e)&&e&&(t.current=uf(e))}}},Ud="json",qd=["text","arraybuffer"],Vd=encodeURIComponent;ArrayBuffer,Boolean;const Hd={formatArgs:{method(e,t){t.method=Af((e||"").toUpperCase(),Of)},data(e,t){t.data=e||""},url(e,t){t.method===Of[0]&&C(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(p(t,a)){let e=t[a];null==e?e="":C(e)&&(e=JSON.stringify(e)),s[Vd(a)]=Vd(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Of[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Ud).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===qd.indexOf(t.responseType)&&(t.responseType="text")}}},zd={formatArgs:{header(e,t){t.header=e||{}}}},Wd={formatArgs:{filePath(e,t){e&&(t.filePath=uf(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const Yd={url:{type:String,required:!0}},Xd="navigateTo",Kd="redirectTo",Zd="reLaunch",Gd="switchTab",Jd="preloadPage",Qd="unPreloadPage",eh=(ih(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),ih(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),lh(Xd)),th=lh(Kd),nh=lh(Zd),oh=lh(Gd),rh={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(mg().length-1,e)}}};function ih(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let sh;function ah(){sh=""}function lh(e){return{formatArgs:{url:ch(e)},beforeAll:ah}}function ch(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=mg();return n.length&&(t=n[n.length-1].$page.route),Su(t,e)}(t)).split("?")[0],r=Tu(o,!0);if(!r)return"page `"+t+"` is not found";if(e===Xd||e===Kd){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if(e===Gd&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if(e!==Gd&&e!==Jd||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!b(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),e!==Qd)if(e!==Jd){if(sh===t&&"appLaunch"!==n.openType)return`${sh} locked`;__uniConfig.ready&&(sh=t)}else if(r.meta.isTabBar){const e=mg(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const uh={formatArgs:{duration:300}},fh=(Boolean,{formatArgs:{title:"",mask:!1}}),dh=(Boolean,{beforeInvoke(){vc()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!p(t,"cancelText")){const{t:e}=dc();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!p(t,"confirmText")){const{t:e}=dc();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),hh=["success","loading","none","error"],ph=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Af(e,hh)},image(e,t){t.image=e?uf(e):""},duration:1500,mask:!1}}),gh={};function mh(e,t){const n=gh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return vh(s,o)}(e)):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function vh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function yh(e){for(const n in gh)if(p(gh,n)){if(gh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return gh[t]=e,t}function bh(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete gh[e]}const _h=Wu(),wh=Wu();const xh=Zu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=Fn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Tn({width:-1,height:-1});return Do((()=>f({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){dr(o),Sr((()=>{t.initial&&ao(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Vi("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Vi("div",{onScroll:r},[Vi("div",null,null)],40,["onScroll"]),Vi("div",{onScroll:r},[Vi("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});let Sh;function Th(){}const Eh={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function Ch(e,t,n){function o(e){const t=hs((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(Sh),document.addEventListener("click",Th,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",Th,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Do((()=>t.value),(e=>e&&o(e)))}const kh={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Oh={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Ah={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},$h=Zu({name:"Image",props:kh,setup(e,{emit:t}){const n=Fn(null),o=function(e,t){const n=Fn(""),o=hs((()=>{let e="auto",o="";const r=Ah[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Tn({rootEl:e,src:hs((()=>t.src?uf(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Sr((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=Qu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Oh[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){Ph&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Do((()=>t.mode),((e,t)=>{Oh[t]&&r(),Oh[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:f}=i;a(u,f,l),o(),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:f})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Do((()=>e.src),(e=>l(e))),Do((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Sr((()=>l(e.src))),Cr((()=>c()))}(o,e,n,i,r),()=>Vi("uni-image",{ref:n},[Vi("div",{style:o.modeStyle},null,4),Oh[e.mode]?Vi(xh,{onResize:i},null,8,["onResize"]):Vi("span",null,null)],512)}});const Ph="Google Inc."===navigator.vendor;const Mh=Qe(!0),Bh=[];let Ih=0,Lh=!1;const jh=e=>Bh.forEach((t=>t.userAction=e));function Rh(e={userAction:!1}){if(!Lh){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Ih&&jh(!0),Ih++,setTimeout((()=>{! --Ih&&jh(!1)}),0)}),Mh)})),Lh=!0}Bh.push(e)}const Nh=()=>!!Ih;function Dh(){const e=Tn({userAction:!1});return Sr((()=>{Rh(e)})),Cr((()=>{!function(e){const t=Bh.indexOf(e);t>=0&&Bh.splice(t,1)}(e)})),{state:e}}function Fh(){const e=Tn({attrs:{}});return Sr((()=>{let t=ts();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function Uh(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function qh(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const Vh=["none","text","decimal","numeric","tel","search","email","url"],Hh=f({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Vh.indexOf(e)},cursorColor:{type:String,default:""}},Eh),zh=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function Wh(e,t,n,o){let r=null;r=it((n=>{t.value=qh(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),Do((()=>e.modelValue),r),Do((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return xr((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Yh(e,t){Dh();const n=hs((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Do((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Sr((()=>{n.value&&ao(o)}))}function Xh(e,t,n,o){Cc(pu(),"getSelectedTextRange",Uh);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=Fn(null),r=Qu(t,n),i=hs((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=hs((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=hs((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=hs((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=qh(e.modelValue,e.type)||qh(e.value,e.type);const u=Tn({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return Do((()=>u.focus),(e=>n("update:focus",e))),Do((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=Wh(e,i,n,s);Yh(e,r),Ch(0,r);const{state:l}=Fh();!function(e,t){const n=oi(of,!1);if(!n)return;const o=ts(),r={submit(){const n=o.proxy;return[n[e],b(t)?n[t]:t.value]},reset(){b(t)?o.proxy[t]="":t.value=""}};n.addField(r),Cr((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Do([()=>t.selectionStart,()=>t.selectionEnd],s),Do((()=>t.cursor),a),Do((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),y(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function f(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,f(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),f(e)})),c.addEventListener("compositionupdate",f)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}function Kh(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&navigator.userAgent.includes("iPhone OS 16")&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Zh=Zu({name:"Input",props:f({},Hh,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...zh],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=hs((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=hs((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(B(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=Fn(void 0!==t?t.toLocaleString():"");return Do((()=>e.modelValue),(e=>{n.value=void 0!==e?e.toLocaleString():""})),Do((()=>e.value),(e=>{n.value=void 0!==e?e.toLocaleString():""})),n}return Fn("")}(e,i),l={fn:null};const c=Fn(null),{fieldRef:u,state:f,scopedAttrsState:d,fixDisabledColor:h,trigger:p}=Xh(e,c,t,((e,t)=>{const n=e.target;if("number"===i.value){if(l.fn&&(n.removeEventListener("blur",l.fn),l.fn=null),n.validity&&!n.validity.valid){if((!a.value||!n.value)&&"-"===e.data||"-"===a.value[0]&&"deleteContentBackward"===e.inputType)return a.value="-",t.value="",l.fn=()=>{a.value=n.value=""},n.addEventListener("blur",l.fn),!1;const o=Kh(e,a,t,n,l);return"boolean"==typeof o?o:(a.value=t.value=n.value="-"===a.value?"":a.value,!1)}{const o=Kh(e,a,t,n,l);if("boolean"==typeof o)return o;a.value=n.value}const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));Do((()=>f.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const g=["number","digit"],m=hs((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),p("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),f.value=e.value}}),()=>{let t=e.disabled&&h?Vi("input",{key:"disabled-input",ref:u,value:f.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:f.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):Vi("input",{key:"input",ref:u,value:f.value,onInput:e=>{f.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:f.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return Vi("uni-input",{ref:c},[Vi("div",{class:"uni-input-wrapper"},[Ho(Vi("div",Zi(d.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[js,!(f.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?Vi("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Gh=["class","style"],Jh=/^on[A-Z]+/,Qh=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=ts(),r=Un({}),i=Un({}),s=Un({}),a=n.concat(Gh);return o.attrs=Tn(o.attrs),Ro((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:Jh.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:s}};function ep(e){const t=[];return g(e)&&e.forEach((e=>{Ri(e)?e.type===Ei?t.push(...ep(e.children)):t.push(e):g(e)&&t.push(...ep(e))})),t}const tp=Zu({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=Fn(null),o=Fn(!1);let{setContexts:r,events:i}=function(e,t){const n=Fn(0),o=Fn(0),r=Tn({x:null,y:null}),i=Fn(null);let s=null,a=[];function l(t){t&&1!==t&&(e.scaleArea?a.forEach((function(e){e._setScale(t)})):s&&s._setScale(t))}function c(e,n=a){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=Ju((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=np(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);s=e&&e===t?e:null}}})),f=Ju((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(np(n)/i.value)}r.x=n.x,r.y=n.y}})),d=Ju((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?a.forEach((function(e){e._endScale()})):s&&s._endScale())}));function h(){p(),a.forEach((function(e,t){e.setParent()}))}function p(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return ni("movableAreaWidth",n),ni("movableAreaHeight",o),{setContexts(e){a=e},events:{_onTouchstart:u,_onTouchmove:f,_onTouchend:d,_resize:h}}}(e,n);const{$listeners:s,$attrs:a,$excludeAttrs:l}=Qh(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),Sr((()=>{i._resize(),o.value=!0}));let u=[];const f=[];function d(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=f.find((e=>n===e.rootRef.value));o&&e.push(Bn(o))}r(e)}return ni("_isMounted",o),ni("movableAreaRootRef",n),ni("addMovableViewContext",(e=>{f.push(e),d()})),ni("removeMovableViewContext",(e=>{const t=f.indexOf(e);t>=0&&(f.splice(t,1),d())})),()=>{const e=t.default&&t.default();return u=ep(e),Vi("uni-movable-area",Zi({ref:n},a.value,l.value,c),[Vi(xh,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function np(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const op=function(e,t,n,o){e.addEventListener(t,(e=>{y(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let rp,ip;function sp(e,t,n){Cr((()=>{document.removeEventListener("mousemove",rp),document.removeEventListener("mouseup",ip)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;op(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),op(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),op(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const f=rp=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",f),op(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const d=ip=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",d),op(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function ap(e,t,n){return e>t-n&&e<t+n}function lp(e,t){return ap(e,0,t)}function cp(){}function up(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function fp(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function dp(e,t,n){this._springX=new fp(e,t,n),this._springY=new fp(e,t,n),this._springScale=new fp(e,t,n),this._startTime=0}cp.prototype.x=function(e){return Math.sqrt(e)},up.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},up.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},up.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},up.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},up.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},up.prototype.dt=function(){return-this._x_v/this._x_a},up.prototype.done=function(){const e=ap(this.s().x,this._endPositionX)||ap(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},up.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},up.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},fp.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}},fp.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},fp.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},fp.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!lp(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(lp(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),lp(t,.1)&&(t=0),lp(o,.1)&&(o=0),o+=this._endPosition),this._solution&&lp(o-e,.1)&&lp(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},fp.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},fp.prototype.done=function(e){return e||(e=(new Date).getTime()),ap(this.x(),this._endPosition,.1)&&lp(this.dx(),.1)},fp.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},fp.prototype.springConstant=function(){return this._k},fp.prototype.damping=function(){return this._c},fp.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},dp.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},dp.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},dp.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},dp.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function hp(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const pp=Zu({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=Fn(null),r=Qu(o,n),{setParent:i}=function(e,t,n){const o=oi("_isMounted",Fn(!1)),r=oi("addMovableViewContext",(()=>{})),i=oi("removeMovableViewContext",(()=>{}));let s,a,l=Fn(1),c=Fn(1),u=Fn(!1),f=Fn(0),d=Fn(0),h=null,p=null,g=!1,m=null,v=null;const y=new cp,b=new cp,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=hs((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new up(1,w.value);Do((()=>e.disabled),(()=>{z()}));const{_updateOldScale:S,_endScale:T,_setScale:E,scaleValueSync:C,_updateBoundary:k,_updateOffset:O,_updateWH:A,_scaleOffset:$,minX:P,minY:M,maxX:B,maxY:I,FAandSFACancel:L,_getLimitXY:j,_setTransform:R,_revise:N,dampingNumber:D,xMove:F,yMove:U,xSync:q,ySync:V,_STD:H}=function(e,t,n,o,r,i,s,a,l,c){const u=hs((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),f=hs((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),d=Fn(Number(e.scaleValue)||1);Do(d,(e=>{R(e)})),Do(u,(()=>{j()})),Do(f,(()=>{j()})),Do((()=>e.scaleValue),(e=>{d.value=Number(e)||0}));const{_updateBoundary:h,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=oi("movableAreaWidth",Fn(0)),r=oi("movableAreaHeight",Fn(0)),i=oi("movableAreaRootRef"),s={x:0,y:0},a={x:0,y:0},l=Fn(0),c=Fn(0),u=Fn(0),f=Fn(0),d=Fn(0),h=Fn(0);function p(){let e=0-s.x+a.x,t=o.value-l.value-s.x-a.x;u.value=Math.min(e,t),d.value=Math.max(e,t);let n=0-s.y+a.y,i=r.value-c.value-s.y-a.y;f.value=Math.min(n,i),h.value=Math.max(n,i)}function g(){s.x=vp(e.value,i.value),s.y=yp(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,s=l.value*o;a.x=(s-l.value)/2,a.y=(i-c.value)/2}return{_updateBoundary:p,_updateOffset:g,_updateWH:m,_scaleOffset:a,minX:u,minY:f,maxX:d,maxY:h}}(t,o,L),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:T,_revise:E,dampingNumber:C,xMove:k,yMove:O,xSync:A,ySync:$,_STD:P}=function(e,t,n,o,r,i,s,a,l,c,u,f,d,h){const p=hs((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=hs((()=>"all"===t.direction||"horizontal"===t.direction)),m=hs((()=>"all"===t.direction||"vertical"===t.direction)),v=Fn(_p(t.x)),y=Fn(_p(t.y));Do((()=>t.x),(e=>{v.value=_p(e)})),Do((()=>t.y),(e=>{y.value=_p(e)})),Do(v,(e=>{E(e)})),Do(y,(e=>{C(e)}));const b=new dp(1,9*Math.pow(p.value,2)/40,p.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<s.value&&(e=s.value,n=!0),t>i.value?(t=i.value,n=!0):t<a.value&&(t=a.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){f&&f.cancel(),u&&u.cancel()}function x(e,n,r,i,s,a){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let f=_(e,n);e=f.x,n=f.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=bp(b,(function(){let e=b.x();S(e.x,e.y,e.scale,i,s,a)}),(function(){u.cancel()}))):S(e,n,r,i,s,a)}function S(r,i,s,a="",u,f){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),s=Number(s.toFixed(1)),l.value===r&&c.value===i||u||h("change",{},{x:hp(r,n.x),y:hp(i,n.y),source:a}),t.scale||(s=o.value),s=+(s=d(s)).toFixed(3),f&&s!==o.value&&h("scale",{},{x:r,y:i,scale:s});let p="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+s+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=r,c.value=i,o.value=s)}function T(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function E(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function C(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:S,_revise:T,dampingNumber:p,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,_,v,y,s,a,l,c,L,n);function M(t,n){if(e.scale){t=L(t),g(t),h();const e=x(s.value,a.value),o=e.x,r=e.y;n?S(o,r,t,"",!0,!0):mp((function(){T(o,r,t,"",!0,!0)}))}}function B(){i.value=!0}function I(e){r.value=e}function L(e){return e=Math.max(.5,u.value,e),e=Math.min(10,f.value,e)}function j(){if(!e.scale)return!1;M(o.value,!0),I(o.value)}function R(t){return!!e.scale&&(M(t=L(t),!0),I(t),t)}function N(){i.value=!1,I(o.value)}function D(e){e&&(e=r.value*e,B(),M(e))}return{_updateOldScale:I,_endScale:N,_setScale:D,scaleValueSync:d,_updateBoundary:h,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:T,_revise:E,dampingNumber:C,xMove:k,yMove:O,xSync:A,ySync:$,_STD:P}}(e,n,t,l,c,u,f,d,h,p);function z(){u.value||e.disabled||(L(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],F.value&&(s=f.value),U.value&&(a=d.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function W(t){if(!u.value&&!e.disabled&&g){let n=f.value,o=d.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),F.value&&(n=t.detail.dx+s,_.historyX.shift(),_.historyX.push(n),U.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),U.value&&(o=t.detail.dy+a,_.historyY.shift(),_.historyY.push(o),F.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<P.value?e.outOfBounds?(r="touch-out-of-bounds",n=P.value-y.x(P.value-n)):n=P.value:n>B.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=B.value+y.x(n-B.value)):n=B.value),o<M.value?e.outOfBounds?(r="touch-out-of-bounds",o=M.value-b.x(M.value-o)):o=M.value:o>I.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=I.value+b.x(o-I.value)):o=I.value),mp((function(){R(n,o,l.value,r)}))}}}function Y(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!N("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=f.value,o=d.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let s=r+n,a=i+o;s<P.value?(s=P.value,a=o+(P.value-n)*i/r):s>B.value&&(s=B.value,a=o+(B.value-n)*i/r),a<M.value?(a=M.value,s=n+(M.value-o)*r/i):a>I.value&&(a=I.value,s=n+(I.value-o)*r/i),x.setEnd(s,a),p=bp(x,(function(){let e=x.s(),t=e.x,n=e.y;R(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||L()}function X(){if(!o.value)return;L();let t=e.scale?C.value:1;O(),A(t),k();let n=j(q.value+$.x,V.value+$.y),r=n.x,i=n.y;R(r,i,t,"",!0),S(t)}return Sr((()=>{sp(n.value,(e=>{switch(e.detail.state){case"start":z();break;case"move":W(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),H.reconfigure(1,9*Math.pow(D.value,2)/40,D.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:T,_setScale:E};r(e),kr((()=>{i(e)}))})),kr((()=>{L()})),{setParent:X}}(e,r,o);return()=>Vi("uni-movable-view",{ref:o},[Vi(xh,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let gp=!1;function mp(e){gp||(gp=!0,requestAnimationFrame((function(){e(),gp=!1})))}function vp(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=vp(e.offsetParent,t):0}function yp(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=yp(e.offsetParent,t):0}function bp(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function _p(e){return/\d+[ur]px$/i.test(e)?fd(parseFloat(e)):Number(e)||0}const wp=Zu({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return g(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=Fn(null),r=Fn(null),i=Qu(o,n),s=function(e){const t=Tn([...e.value]),n=Tn({value:t,height:34});return Do((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=Fn(null);Sr((()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)}));let l=Fn([]),c=Fn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==ki));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return ni("getPickerViewColumn",(function(e){return hs({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),ni("pickerViewProps",e),ni("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=ep(e);l.value=t,ao((()=>{c.value=t}))}return Vi("uni-picker-view",{ref:o},[Vi(xh,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),Vi("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class xp{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Sp(e,t,n){return e>t-n&&e<t+n}function Tp(e,t){return Sp(e,0,t)}class Ep{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Tp(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(Tp(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Tp(t,.4)&&(t=0),Tp(o,.4)&&(o=0),o+=this._endPosition),this._solution&&Tp(o-e,.4)&&Tp(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Sp(this.x(),this._endPosition,.4)&&Tp(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Cp{constructor(e,t,n){this._extent=e,this._friction=t||new xp(.01),this._spring=n||new Ep(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class kp{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Cp(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),y(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),y(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(y(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),y(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const Op=Zu({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=Fn(null),r=Fn(null),i=oi("getPickerViewColumn"),s=ts(),a=i?i(s):Fn(0),l=oi("pickerViewProps"),c=oi("pickerViewState"),u=Fn(34),f=Fn(null);Sr((()=>{const e=f.value;u.value=e.$el.offsetHeight}));const d=hs((()=>(c.height-u.value)/2)),{state:h}=Fh();let p;const g=Tn({current:a.value,length:0});let m;function v(){p&&!m&&(m=!0,ao((()=>{m=!1;let e=Math.min(g.current,g.length-1);e=Math.max(e,0),p.update(e*u.value,void 0,u.value)})))}Do((()=>a.value),(e=>{e!==g.current&&(g.current=e,v())})),Do((()=>g.current),(e=>a.value=e)),Do([()=>u.value,()=>g.length,()=>c.height],v);let y=0;function b(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(g.current+(t<0?-1:1),g.length-1);g.current=e=Math.max(e,0),p.scrollTo(e*u.value)}else y=t;e.preventDefault()}function _({clientY:e}){const t=o.value;if(!p.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(g.current+t,g.length-1);g.current=r=Math.max(r,0),p.scrollTo(r*u.value)}}}const w=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new kp(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new xp(1e-4),spring:new Ep(2,90,20),onSnap:e=>{isNaN(e)||e===g.current||(g.current=e)}});p=n,sp(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),v()};return Sr(w),()=>{const e=t.default&&t.default();g.length=ep(e).length;const n=`${d.value}px 0`;return Vi("uni-picker-view-column",{ref:o},[Vi("div",{onWheel:b,onClick:_,class:"uni-picker-view-group"},[Vi("div",Zi(h.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${d.value}px;${l.maskStyle}`}),null,16),Vi("div",Zi(h.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Vi(xh,{ref:f,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),Vi("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}});function Ap(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,f=0,d=1,h=null,p=!1,g=0,m="";const v=hs((()=>n.value.length>t.displayMultipleItems)),y=hs((()=>e.circular&&v.value));function b(r){Math.floor(2*f)===Math.floor(2*r)&&Math.ceil(2*f)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),f=Math.max(o-(l+1),l-s,0),d=Math.max(o-(c+1),c-s,0),h=Math.min(u,f,d),p=[n,l,c][[u,f,d].indexOf(h)];t.updatePosition(p,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*d+"%")+", "+(e.vertical?100*-r*d+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),f=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){h=null}function x(){if(!h)return void(p=!1);const e=h,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){b(o),h=null,p=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function S(e,o,r){w();const i=t.duration,s=n.value.length;let a=f;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);h={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},p||(p=!0,l=requestAnimationFrame(x))}function T(){s();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function E(e){e?T():s()}return Do([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),Do([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),h&&(b(h.toPos),h=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);d=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();d=e.width/t.width,d>0&&d<1||(d=1)}const a=f;f=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(a+l-g),g=l):(b(l),e.autoplay&&T())):(u=!0,b(-t.displayMultipleItems-1))})),Do((()=>t.interval),(()=>{c&&(s(),T())})),Do((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const s=n.value;if(!r){const t=s.length;S(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),Do((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),Do((()=>e.autoplay&&!t.userTracking),E),E(e.autoplay&&!t.userTracking),Sr((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(f+o);e?b(g):(m="touch",t.current=r,S(r,"touch",0!==o?o:0===r&&y.value&&f>=1?1:0))}sp(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),g=f,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&T())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const f=a-s||1,d=o.value;e.vertical?u(-r.dy/d.offsetHeight,-r.ddy/f):u(-r.dx/d.offsetWidth,-r.ddx/f)}(c.detail),!1}}}))})),kr((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const $p=Zu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=Fn(null),r=Qu(o,n),i=Fn(null),s=Fn(null),a=function(e){return Tn({interval:hs((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:hs((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:hs((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=hs((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:au(e.previousMargin,!0),bottom:au(e.nextMargin,!0)}:{top:0,bottom:0,left:au(e.previousMargin,!0),right:au(e.nextMargin,!0)}),t})),c=hs((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const f=[],d=Fn([]);function h(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=f.find((e=>n===e.rootRef.value));o&&e.push(Bn(o))}d.value=e}ni("addSwiperContext",(function(e){f.push(e),h()}));ni("removeSwiperContext",(function(e){const t=f.indexOf(e);t>=0&&(f.splice(t,1),h())}));const{onSwiperDotClick:p,circularEnabled:g,swiperEnabled:m}=Ap(e,a,d,s,n,r);let v=()=>null;return v=Pp(o,e,a,p,d,g,m),()=>{const n=t.default&&t.default();return u=ep(n),Vi("uni-swiper",{ref:o},[Vi("div",{ref:i,class:"uni-swiper-wrapper"},[Vi("div",{class:"uni-swiper-slides",style:l.value},[Vi("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Vi("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[d.value.map(((t,n,o)=>Vi("div",{onClick:()=>p(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),Pp=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,c=!1,u=Fn(!1);function d(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Ro((()=>{a="auto"===t.navigation,u.value=!0!==t.navigation||a,b()})),Ro((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,c=!0,a&&(u.value=!0))}));const h={onMouseover:e=>d(e,"over"),onMouseout:e=>d(e,"out")};function p(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const g=()=>uu("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:f}=e.value.getBoundingClientRect();let d=!1;if(d=t.vertical?!(r-a<f/3||l-r<f/3):!(o-i<c/3||s-o<c/3),d)return m=setTimeout((()=>{u.value=d}),300);u.value=d},y=()=>{u.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return Sr(b),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Vi(Ei,null,[Vi("div",Zi({class:["uni-swiper-navigation uni-swiper-navigation-prev",f({"uni-swiper-navigation-disabled":l},e)],onClick:e=>p(e,"prev",l)},h),[g()],16,["onClick"]),Vi("div",Zi({class:["uni-swiper-navigation uni-swiper-navigation-next",f({"uni-swiper-navigation-disabled":c},e)],onClick:e=>p(e,"next",c)},h),[g()],16,["onClick"])]):null}},Mp=Zu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=Fn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return Sr((()=>{const e=oi("addSwiperContext");e&&e(o)})),kr((()=>{const e=oi("removeSwiperContext");e&&e(o)})),()=>Vi("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),Bp={ensp:" ",emsp:" ",nbsp:" "};function Ip(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Bp[t]&&" "===i&&(i=Bp[t]),r?(o+="n"===i?re:"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Bp.nbsp).replace(/&ensp;/g,Bp.ensp).replace(/&emsp;/g,Bp.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split(re)}const Lp=Zu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Fn(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==ki){const n=Ip(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(zi(e)),t!==r&&o.push(Vi("br"))}))}else o.push(t)})),Vi("uni-text",{ref:n,selectable:!!e.selectable||null},[Vi("span",null,o)],8,["selectable"])}}}),jp=f({},Hh,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>Np.concat("return").includes(e)}});let Rp=!1;const Np=["done","go","next","search","send"];const Dp=Zu({name:"Textarea",props:jp,emits:["confirm","linechange",...zh],setup(e,{emit:t,expose:n}){const o=Fn(null),r=Fn(null),{fieldRef:i,state:s,scopedAttrsState:a,fixDisabledColor:l,trigger:c}=Xh(e,o,t),u=hs((()=>s.value.split(re))),f=hs((()=>Np.includes(e.confirmType))),d=Fn(0),h=Fn(null);function p({height:e}){d.value=e}function g(e){"Enter"===e.key&&f.value&&e.preventDefault()}function m(t){if("Enter"===t.key&&f.value){!function(e){c("confirm",e,{value:s.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Do((()=>d.value),(t=>{const n=o.value,i=h.value,s=r.value;let a=parseFloat(getComputedStyle(n).lineHeight);isNaN(a)&&(a=i.offsetHeight);var l=Math.round(t/a);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",s.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";Rp=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),s.value=e.value}}),()=>{let t=e.disabled&&l?Vi("textarea",{key:"disabled-textarea",ref:i,value:s.value,tabindex:"-1",readonly:!!e.disabled,maxlength:s.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Rp},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Vi("textarea",{key:"textarea",ref:i,value:s.value,disabled:!!e.disabled,maxlength:s.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Rp},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:g,onKeyup:m},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Vi("uni-textarea",{ref:o},[Vi("div",{ref:r,class:"uni-textarea-wrapper"},[Ho(Vi("div",Zi(a.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[js,!s.value.length]]),Vi("div",{ref:h,class:"uni-textarea-line"},[" "],512),Vi("div",{class:"uni-textarea-compute"},[u.value.map((e=>Vi("div",null,[e.trim()?e:"."]))),Vi(xh,{initial:!0,onResize:p},null,8,["initial","onResize"])]),"search"===e.confirmType?Vi("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),Fp=Zu({name:"View",props:f({},ef),setup(e,{slots:t}){const n=Fn(null),{hovering:o,binding:r}=tf(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?Vi("uni-view",Zi({class:o.value?i:"",ref:n},r),[t.default&&t.default()],16):Vi("uni-view",{ref:n},[t.default&&t.default()],512)}}});function Up(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function qp(e,t,n){e&&Cc(pu(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function Vp(e,t){e&&function(e,t){t=Ec(e,t),delete Tc[t]}(pu(),e)}let Hp=0;function zp(e,t,n,o){y(t)&&_r(e,t.bind(n),o)}function Wp(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!y(t))&&(lt.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];g(r)?r.forEach((e=>zp(o,e,n,t))):zp(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{const e=t.attrs.__pageQuery;0,yu(n,ye,e),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&yu(n,fe)}catch(i){}}}function Yp(e,t,n){Wp(e,t,n)}function Xp(e,t,n){return e[t]=n}function Kp(e,...t){const n=this[e];return n?n(...t):null}function Zp(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;yu(r.proxy,pe,t)}}function Gp(e,t){return e?[...new Set([].concat(e,t))]:t}function Jp(e){const t=e._context.config;var n;t.errorHandler=ut(e,Zp),n=t.optionMergeStrategies,lt.forEach((e=>{n[e]=Gp}));const o=t.globalProperties;o.$set=Xp,o.$applyOptions=Yp,o.$callMethod=Kp,function(e){ct.forEach((t=>t(e)))}(e)}const Qp=iu("upm");function eg(){return oi(Qp)}function tg(e){const t=function(e){return Tn(function(e){if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==mg().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(vu(zl().meta,e)))))}(e);return ni(Qp,t),t}function ng(){return zl()}function og(){return history.state&&history.state.__id__||1}let rg;function ig(){var e;return rg||(rg=__uniConfig.tabBar&&Tn((e=__uniConfig.tabBar,ac()&&e.list&&e.list.forEach((e=>{fc(e,["text"])})),e))),rg}const sg=window.CSS&&window.CSS.supports;function ag(e){return sg&&(sg(e)||sg.apply(window.CSS,e.split(":")))}const lg=ag("top:env(a)"),cg=ag("top:constant(a)"),ug=ag("backdrop-filter:blur(10px)"),fg=(()=>lg?"env":cg?"constant":"")();function dg(e){return fg?`calc(${e}px + ${fg}(safe-area-inset-bottom))`:`${e}px`}const hg="$$",pg=new Map;function gg(){return pg}function mg(){const e=[],t=pg.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function vg(e,t=!0){const n=pg.get(e);n.$.__isUnload=!0,yu(n,be),pg.delete(e),t&&function(e){const t=xg.get(e);t&&(xg.delete(e),Sg.pruneCacheEntry(t))}(e)}let yg=og();function bg(e){const t=eg();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=gt(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:We(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function _g(e){const t=bg(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),pg.set(wg(t.path,t.id),e)}function wg(e,t){return e+hg+t}const xg=new Map,Sg={get:e=>xg.get(e),set(e,t){!function(e){const t=parseInt(e.split(hg)[1]);if(!t)return;Sg.forEach(((e,n)=>{const o=parseInt(n.split(hg)[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;Sg.delete(n),Sg.pruneCacheEntry(e),ao((()=>{pg.forEach(((e,t)=>{e.$.isUnmounted&&pg.delete(t)}))}))}}))}(e),xg.set(e,t)},delete(e){xg.get(e)&&xg.delete(e)},forEach(e){xg.forEach(e)}};function Tg(e,t){!function(e){const t=Cg(e),{body:n}=document;kg&&n.removeAttribute(kg),t&&n.setAttribute(t,""),kg=t}(e),function(e){let t=0;if(e.isTabBar){const e=ig();e.shown&&(t=parseInt(e.height))}var n;ru({"--window-top":(n=0,fg?`calc(${n}px + ${fg}(safe-area-inset-top))`:`${n}px`),"--window-bottom":dg(t)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),function(e,t){document.removeEventListener("touchmove",bu),Og&&document.removeEventListener("scroll",Og);if(t.disableScroll)return document.addEventListener("touchmove",bu);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},s=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&qv.publishHandler(Te,{scrollTop:o},e),n&&qv.emit(e+"."+Te,{scrollTop:o})}}(s,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||ie,i.onReachBottom=()=>qv.publishHandler(Ce,{},s));Og=xu(i),requestAnimationFrame((()=>document.addEventListener("scroll",Og)))}(e,t)}function Eg(e){const t=Cg(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Cg(e){return e.type.__scopeId}let kg,Og;function Ag(e){const t=Hl({history:Mg(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:Pg});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&($g[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let $g=Object.create(null);const Pg=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,$g[o]);if(t)return t}return{left:0,top:0};var o};function Mg(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),rl(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=mg(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;vg(wg(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const Bg={install(e){Jp(e),Iu(e),zu(e),e.config.warnHandler||(e.config.warnHandler=Ig),Ag(e)}};function Ig(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n)}const Lg={class:"uni-async-loading"},jg=Vi("i",{class:"uni-loading"},null,-1),Rg=Gu({name:"AsyncLoading",render:()=>(Pi(),ji("div",Lg,[jg]))});function Ng(){window.location.reload()}const Dg=Gu({name:"AsyncError",setup(){pc();const{t:e}=dc();return()=>Vi("div",{class:"uni-async-error",onClick:Ng},[e("uni.async.error")],8,["onClick"])}});let Fg;function Ug(){return Fg}function qg(e){Fg=e,Object.defineProperty(Fg.$.ctx,"$children",{get:()=>mg().map((e=>e.$vm))});const t=Fg.$.appContext.app;t.component(Rg.name)||t.component(Rg.name,Rg),t.component(Dg.name)||t.component(Dg.name,Dg),function(e){e.$vm=e,e.$mpType="app";const t=Fn(dc().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Fg),function(e,t){const n=e.$options||{};n.globalData=f(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Fg),Vu(),Fc()}function Vg(e,{clone:t,init:n,setup:o,before:r}){t&&(e=f({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=ts();n(r.proxy);const s=o(r);if(i)return i(s||e,t)},e}function Hg(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Vg(e.default,t):Vg(e,t)}function zg(e){return Hg(e,{clone:!0,init:_g,setup(e){e.$pageInstance=e;const t=ng(),n=nt(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n,e.proxy.options=n;const o=eg();var r,i;return xr((()=>{Tg(e,o)})),Sr((()=>{Eg(e);const{onReady:n}=e;n&&R(n),Kg(t)})),pr((()=>{if(!e.__isVisible){Tg(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&R(n),ao((()=>{Kg(t)}))}}),"ba",r),function(e,t){pr(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&R(t)}})),i=o.id,qv.subscribe(Ec(i,wc),kc),Cr((()=>{!function(e){qv.unsubscribe(Ec(e,wc)),Object.keys(Tc).forEach((t=>{0===t.indexOf(e+".")&&delete Tc[t]}))}(o.id)})),n}})}function Wg(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=hm(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Vv.emit(xe,{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Yg(e){C(e.data)&&e.data.type===ue&&Vv.emit(De,e.data.data,e.data.pageId)}function Xg(){const{emit:e}=Vv;"visible"===document.visibilityState?e(Re,f({},wh)):e(Ne)}function Kg(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&yu("onTabItemTap",{index:n,text:t,pagePath:o})}function Zg(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function Gg(e,t,n){const o=Tn({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),r={x:0,y:0};return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0},onTouchmove:function(i){function s(){i.stopPropagation(),i.preventDefault()}n.fullscreen&&s();const a=o.gestureType;if("stop"===a)return;const l=i.targetTouches[0],c=l.pageX,u=l.pageY,f=r,d=t.value;if("progress"===a?function(e){const n=t.value,r=n.duration;let i=e/600*r+o.currentTimeOld;i<0?i=0:i>r&&(i=r);o.currentTimeNew=i}(c-f.x):"volume"===a&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),n.volume=i,o.volumeNew=i)}(u-f.y),"none"===a)if(Math.abs(c-f.x)>Math.abs(u-f.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=d.currentTime,n.fullscreen||s()}else{if(!e.pageGesture)return void(o.gestureType="stop");o.gestureType="volume",o.volumeOld=d.volume,n.fullscreen||s()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}function Jg(e,t,n,o,r,i,s,a){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:r,playbackRate:i,requestFullScreen:s,exitFullScreen:a};!function(e,t,n,o){const r=ts().proxy;Sr((()=>{qp(t||Up(r),e),Do((()=>r.id),((t,n)=>{qp(Up(r,t),e),Vp(n&&Up(r,n))}))})),Cr((()=>{Vp(t||Up(r))}))}(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in l&&l[e](n)}),function(e){const t=fu(),n=ts().proxy,o=n.$options.name.toLowerCase(),r=n.id||"context"+Hp++;return Sr((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}())}const Qg=Zu({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=Fn(null),i=Fn(null),s=Qu(r,t),{state:a}=Dh(),{$attrs:l}=Qh({excludeListeners:!0}),{t:c}=dc();bc();const{videoRef:u,state:f,play:d,pause:h,stop:p,seek:m,playbackRate:v,toggle:y,onDurationChange:b,onLoadedMetadata:_,onProgress:w,onWaiting:x,onVideoError:S,onPlay:T,onPause:E,onEnded:C,onTimeUpdate:k}=function(e,t,n){const o=Fn(null),r=hs((()=>uf(e.src))),i=hs((()=>"true"===e.muted||!0===e.muted)),s=Tn({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:i});function a(e){const t=e.target,n=t.buffered;n.length&&(s.buffered=n.end(n.length-1)/t.duration*100)}function l(){o.value.pause()}function c(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)}return Do((()=>r.value),(()=>{s.playing=!1,s.currentTime=0})),Do((()=>s.buffered),(e=>{n("progress",{},{buffered:e})})),Do((()=>i.value),(e=>{o.value.muted=e})),{videoRef:o,state:s,play:function(){const e=o.value;s.start=!0,e.play()},pause:l,stop:function(){c(0),l()},seek:c,playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;s.playing?e.pause():e.play()},onDurationChange:function({target:e}){s.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),a(t)},onProgress:a,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){s.playing=!1,n("error",e,{})},onPlay:function(e){s.start=!0,s.playing=!0,n("play",e,{})},onPause:function(e){s.playing=!1,n("pause",e,{})},onEnded:function(e){s.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target,o=s.currentTime=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,s),{state:O,danmuRef:A,updateDanmu:$,toggleDanmu:P,sendDanmu:M}=function(e,t){const n=Fn(null),o=Tn({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=g(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function s(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return i.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,a=r,l={time:n,index:a.index};if(n>a.time)for(let r=a.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&s(e)}else if(n<a.time)for(let t=a.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,f),{state:B,onFullscreenChange:I,emitFullscreenChange:L,toggleFullscreen:j,requestFullScreen:R,exitFullScreen:N}=function(e,t,n,o,r){const i=Tn({fullscreen:!1}),s=/^Apple/.test(navigator.vendor);function a(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||s&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&a(e)}function c(){l(!1)}return Cr(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||a(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:a,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(s,i,u,a,r),{state:D,onTouchstart:F,onTouchend:U,onTouchmove:q}=Gg(e,u,B),{state:V,progressRef:H,ballRef:z,clickProgress:W,toggleControls:Y}=function(e,t,n){const o=Fn(null),r=Fn(null),i=hs((()=>e.showCenterPlayBtn&&!t.start)),s=Fn(!0),a=hs((()=>!i.value&&e.controls&&s.value)),l=Tn({touching:!1,controlsTouching:!1,centerPlayBtnShow:i,controlsShow:a,controlsVisible:s});let c;function u(){c=setTimeout((()=>{l.controlsVisible=!1}),3e3)}function f(){c&&(clearTimeout(c),c=null)}return Cr((()=>{c&&clearTimeout(c)})),Do((()=>l.controlsShow&&t.playing&&!l.controlsTouching),(e=>{e?u():f()})),Do([()=>t.currentTime,()=>{e.duration}],(function(){l.touching||(t.progress=t.currentTime/t.duration*100)})),Sr((()=>{const e=Qe(!1);let i,s,a,c=!0;const u=r.value;function f(e){const n=e.targetTouches[0],r=n.pageX,l=n.pageY;if(c&&Math.abs(r-i)<Math.abs(l-s))return void d(e);c=!1;const u=o.value.offsetWidth;let f=a+(r-i)/u*100;f<0?f=0:f>100&&(f=100),t.progress=f,e.preventDefault(),e.stopPropagation()}function d(o){l.controlsTouching=!1,l.touching&&(u.removeEventListener("touchmove",f,e),c||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}u.addEventListener("touchstart",(n=>{l.controlsTouching=!0;const o=n.targetTouches[0];i=o.pageX,s=o.pageY,a=t.progress,c=!0,l.touching=!0,u.addEventListener("touchmove",f,e)})),u.addEventListener("touchend",d),u.addEventListener("touchcancel",d)})),{state:l,progressRef:o,ballRef:r,clickProgress:function(e){const r=o.value;let i=e.target,s=e.offsetX;for(;i&&i!==r;)s+=i.offsetLeft,i=i.parentNode;const a=r.offsetWidth;let l=0;s>=0&&s<=a&&(l=s/a,n(t.duration*l))},toggleControls:function(){l.controlsVisible=!l.controlsVisible},autoHideStart:u,autoHideEnd:f}}(e,f,m);return Jg(d,h,p,m,M,v,R,N),()=>Vi("uni-video",{ref:r,id:e.id,onClick:Y},[Vi("div",{ref:i,class:"uni-video-container",onTouchstart:F,onTouchend:U,onTouchmove:q,onFullscreenchange:ca(I,["stop"]),onWebkitfullscreenchange:ca((e=>I(e,!0)),["stop"])},[Vi("video",Zi({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:f.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:b,onLoadedmetadata:_,onProgress:w,onWaiting:x,onError:S,onPlay:T,onPause:E,onEnded:C,onTimeupdate:e=>{k(e),$(e)},onWebkitbeginfullscreen:()=>L(!0),onX5videoenterfullscreen:()=>L(!0),onWebkitendfullscreen:()=>L(!1),onX5videoexitfullscreen:()=>L(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),Ho(Vi("div",{class:"uni-video-bar uni-video-bar-full",onClick:ca((()=>{}),["stop"])},[Vi("div",{class:"uni-video-controls"},[Ho(Vi("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!f.playing,"uni-video-control-button-pause":f.playing},onClick:ca(y,["stop"])},null,10,["onClick"]),[[js,e.showPlayBtn]]),Ho(Vi("div",{class:"uni-video-current-time"},[Zg(f.currentTime)],512),[[js,e.showProgress]]),Ho(Vi("div",{ref:H,class:"uni-video-progress-container",onClick:ca(W,["stop"])},[Vi("div",{class:"uni-video-progress"},[Vi("div",{style:{width:f.buffered+"%"},class:"uni-video-progress-buffered"},null,4),Vi("div",{ref:z,style:{left:f.progress+"%"},class:"uni-video-ball"},[Vi("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[js,e.showProgress]]),Ho(Vi("div",{class:"uni-video-duration"},[Zg(Number(e.duration)||f.duration)],512),[[js,e.showProgress]])]),Ho(Vi("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":O.enable},onClick:ca(P,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[js,e.danmuBtn]]),Ho(Vi("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":B.fullscreen},onClick:ca((()=>j(!B.fullscreen)),["stop"])},null,10,["onClick"]),[[js,e.showFullscreenBtn]])],8,["onClick"]),[[js,V.controlsShow]]),Ho(Vi("div",{ref:A,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[js,f.start&&O.enable]]),V.centerPlayBtnShow&&Vi("div",{class:"uni-video-cover",onClick:ca((()=>{}),["stop"])},[Vi("div",{class:"uni-video-cover-play-button",onClick:ca(d,["stop"])},null,8,["onClick"]),Vi("p",{class:"uni-video-cover-duration"},[Zg(Number(e.duration)||f.duration)])],8,["onClick"]),Vi("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":"volume"===D.gestureType}},[Vi("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),Vi("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[Vi("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),Vi("div",{class:"uni-video-toast-value"},[Vi("div",{style:{width:100*D.volumeNew+"%"},class:"uni-video-toast-value-content"},[Vi("div",{class:"uni-video-toast-volume-grids"},[Mr(10,(()=>Vi("div",{class:"uni-video-toast-volume-grids-item"},null)))])],4)])],2),Vi("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":"progress"===D.gestureType}},[Vi("div",{class:"uni-video-toast-title"},[Zg(D.currentTimeNew)," / ",Zg(f.duration)])],2),Vi("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}}),em=nd("makePhoneCall",(({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t()))),tm="__DC_STAT_UUID",nm=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let om;function rm(){if(om=om||nm[tm],!om){om=Date.now()+""+Math.floor(1e7*Math.random());try{nm[tm]=om}catch(e){}}return om}function im(){if(!0!==__uniConfig.darkmode)return b(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function sm(){let e,t="0",n="",o="phone";const r=navigator.language;if(hf){e="iOS";const o=ff.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=ff.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(df){e="Android";const o=ff.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=ff.match(/\((.+?)\)/),i=r?r[1].split(";"):ff.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(vf)n="iPad",e="iOS",o="pad",t=y(window.BigInt)?"14.0":"13.0";else if(pf||gf||mf){n="PC",e="PC",o="pc",t="0";let r=ff.match(/\((.+?)\)/)[1];if(pf){switch(e="Windows",pf[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(gf){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(mf){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(ff)&&(a=t[n],l=ff.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:ff,osname:e,osversion:t,theme:im()}}const am=td(0,(()=>{const e=window.devicePixelRatio,t=yf(),n=bf(t),o=_f(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=wf(o);let s=window.innerHeight;const a=Gc.top,l={left:Gc.left,right:i-Gc.right,top:Gc.top,bottom:s-Gc.bottom,width:i-Gc.left-Gc.right,height:s-Gc.top-Gc.bottom},{top:c,bottom:u}=nu();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Gc.top,right:Gc.right,bottom:Gc.bottom,left:Gc.left},screenTop:r-s}}));let lm,cm=!0;function um(){cm&&(lm=sm())}const fm=td(0,(()=>{um();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:c}=lm;return f({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:rm(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i},{})})),dm=td(0,(()=>{um();const{theme:e,language:t,browserName:n,browserVersion:o}=lm;return f({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Id?Id():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""},{})})),hm=td(0,(()=>{cm=!0,um(),cm=!1;const e=am(),t=fm(),n=dm();cm=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=lm,l=f(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return C(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)}));const pm=td(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function gm(e){const t=localStorage&&localStorage.getItem(e);if(!b(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=b(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const mm=td(0,(e=>{try{return gm(e)}catch(t){return""}})),vm={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function ym({count:e,sourceType:t,type:n,extension:o}){Rh();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${vm[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let bm=null;const _m=nd("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{yc();const{t:s}=dc();bm&&(document.body.removeChild(bm),bm=null),bm=ym({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(bm),bm.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||yh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),bm.click(),Nh()}),0,Dd);let wm=null;const xm=nd("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{yc();const{t:i}=dc();wm&&(document.body.removeChild(wm),wm=null),wm=ym({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(wm),wm.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||yh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),wm.click(),Nh()}),0,jd),Sm={esc:["Esc","Escape"],enter:["Enter"]},Tm=Object.keys(Sm);const Em=Vi("div",{class:"uni-mask"},null,-1);function Cm(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),da(or({setup:()=>()=>(Pi(),ji(e,t,null,16))}))}function km(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function Om(e,{onEsc:t,onEnter:n}){const o=Fn(e.visible),{key:r,disable:i}=function(){const e=Fn(""),t=Fn(!1),n=n=>{if(t.value)return;const o=Tm.find((e=>-1!==Sm[e].indexOf(n.key)));o&&(e.value=o),ao((()=>e.value=""))};return Sr((()=>{document.addEventListener("keyup",n)})),Cr((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}();return Do((()=>e.visible),(e=>o.value=e)),Do((()=>o.value),(e=>i.value=!e)),Ro((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let Am=0,$m="";function Pm(e){let t=Am;Am+=e?1:-1,Am=Math.max(0,Am),Am>0?0===t&&($m=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=$m,$m="")}const Mm=Gu({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Tn({direction:"none"});let n=1,o=0,r=0,i=0,s=0;function a({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,s=t.height,f(e)}function u(e){const a=n*o>i,l=n*r>s;t.direction=a&&l?"all":a?"horizontal":l?"vertical":"none",f(e)}function f(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return Vi(tp,{style:n,onTouchstart:Ju(c),onTouchmove:Ju(f),onTouchend:Ju(u)},{default:()=>[Vi(pp,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:a},{default:()=>[Vi("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function Bm(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const Im=Gu({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){Sr((()=>Pm(!0))),kr((()=>Pm(!1)));const n=Fn(null),o=Fn(Bm(e));let r;function i(){r||ao((()=>{t("close")}))}function s(e){o.value=e.detail.current}Do((()=>e.current),(()=>o.value=Bm(e))),Sr((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const a={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return Vi("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[Vi($p,{navigation:"auto",current:o.value,onChange:s,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>Vi(Mp,null,{default:()=>[Vi(Mm,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Ri(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),Vi("div",{style:a},[uu("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],4)],8,["onClick"]);var r}}});let Lm,jm=null;const Rm=()=>{jm=null,ao((()=>{null==Lm||Lm.unmount(),Lm=null}))},Nm=nd("previewImage",((e,{resolve:t})=>{jm?f(jm,e):(jm=Tn(e),ao((()=>{Lm=Cm(Im,jm,Rm),Lm.mount(km("u-a-p"))}))),t()}),0,Fd);let Dm=null;const Fm=nd("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{yc();const{t:r}=dc();Dm&&(document.body.removeChild(Dm),Dm=null),Dm=ym({sourceType:e,extension:t,type:"video"}),document.body.appendChild(Dm),Dm.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||yh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=yh(t);i.onloadedmetadata=function(){bh(e),n(f(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,bh(e),n(r)}),300),i.src=e}else n(r)})),Dm.click(),Nh()}),0,Rd),Um=ed("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const f=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(b(t)||t instanceof ArrayBuffer)u=t;else if("json"===f)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===f){const e=[];for(const n in t)p(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const d=new XMLHttpRequest,h=new qm(d);d.open(o,e);for(const v in n)p(n,v)&&d.setRequestHeader(v,n[v]);const g=setTimeout((function(){d.onload=d.onabort=d.onerror=null,h.abort(),c("timeout",{errCode:5})}),a);return d.responseType=i,d.onload=function(){clearTimeout(g);const e=d.status;let t="text"===i?d.responseText:d.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:Vm(d.getAllResponseHeaders()),cookies:[]})},d.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},d.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},d.withCredentials=s,d.send(u),h}),0,Hd);class qm{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Vm(e){const t={};return e.split(re).forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class Hm{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const zm=ed("downloadFile",(({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,s=new XMLHttpRequest,a=new Hm(s);return s.open("GET",e,!0),Object.keys(t).forEach((e=>{s.setRequestHeader(e,t[e])})),s.responseType="blob",s.onload=function(){clearTimeout(i);const t=s.status,n=this.response;let r;const a=s.getResponseHeader("content-disposition");if(a){const e=a.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:yh(n)})},s.onabort=function(){clearTimeout(i),r("abort",{errCode:600003})},s.onerror=function(){clearTimeout(i),r("",{errCode:602001})},s.onprogress=function(e){a._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},s.send(),i=setTimeout((function(){s.onprogress=s.onload=s.onabort=s.onerror=null,a.abort(),r("timeout",{errCode:5})}),n),a}),0,zd);class Wm{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Ym=ed("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Wm;return g(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(vh(e)):mh(t)))).then((function(t){var n,o=new XMLHttpRequest,f=new FormData;Object.keys(s).forEach((e=>{f.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];f.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),a),o.send(f),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Wd),Xm=nd("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===yu(Se,{from:e.from||"navigateBack"})&&(o=!1),o?(Ug().$router.go(-e.delta),t()):n(Se)}),0,rh);function Km({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=Ug().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:rt(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++yg,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(ul(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new st(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}const Zm=nd(Xd,(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>Km({type:Xd,url:e,events:t,isAutomatedTesting:n}).then(o).catch(r)),0,eh);const Gm=nd(Kd,(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=du();if(!e)return;const t=e.$page;vg(wg(t.path,t.id))}(),Km({type:Kd,url:e,isAutomatedTesting:t}).then(n).catch(o))),0,th);const Jm=nd(Zd,(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=gg().keys();for(const t of e)vg(t)}(),Km({type:Zd,url:e,isAutomatedTesting:t}).then(n).catch(o))),0,nh);function Qm(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const ev=nd(Gd,(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>(function(){const e=gu();if(!e)return;const t=gg(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:vg(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,yu(e,de))}(),Km({type:Gd,url:e,tabBarText:t,isAutomatedTesting:n},function(e){const t=gg().values();for(const n of t){const t=n.$page;if(Qm(e,t))return n.$.__isActive=!0,t.id}}(e)).then(o).catch(r))),0,oh);function tv(e){__uniConfig.darkmode&&Vv.on(ge,e)}function nv(e){Vv.off(ge,e)}function ov(e){let t={};return __uniConfig.darkmode&&(t=gt(e,__uniConfig.themeConfig,im())),__uniConfig.darkmode?t:e}const rv={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},iv=or({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=Fn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=Om(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=Fn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=rv[e].cancelColor})(e,t)};return Ro((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===im()&&n({theme:"dark"}),tv(n))):nv(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:f,placeholderText:d}=e;return n.value=o,Vi(xs,{name:"uni-fade"},{default:()=>[Ho(Vi("uni-modal",{onTouchmove:Jc},[Em,Vi("div",{class:"uni-modal"},[t?Vi("div",{class:"uni-modal__hd"},[Vi("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,f?Vi("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:d,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Vi("div",{class:"uni-modal__bd",onTouchmovePassive:Qc,textContent:o},null,40,["onTouchmovePassive","textContent"]),Vi("div",{class:"uni-modal__ft"},[l&&Vi("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Vi("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[js,s.value]])]})}}});let sv;const av=Xe((()=>{Vv.on("onHidePopup",(()=>sv.visible=!1))}));let lv;function cv(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&sv.editable&&(o.content=t),lv&&lv(o)}const uv=nd("showModal",((e,{resolve:t})=>{av(),lv=t,sv?(f(sv,e),sv.visible=!0):(sv=Tn(e),ao((()=>(Cm(iv,sv,cv).mount(km("u-a-m")),ao((()=>sv.visible=!0))))))}),0,dh),fv={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==hh.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},dv="uni-toast__icon",hv={light:"#fff",dark:"rgba(255,255,255,0.9)"},pv=e=>hv[e],gv=or({name:"Toast",props:fv,setup(e){gc(),mc();const{Icon:t}=function(e){const t=Fn(pv(im())),n=({theme:e})=>t.value=pv(e);Ro((()=>{e.visible?tv(n):nv(n)}));const o=hs((()=>{switch(e.icon){case"success":return Vi(uu(lu,t.value,38),{class:dv});case"error":return Vi(uu(cu,t.value,38),{class:dv});case"loading":return Vi("i",{class:[dv,"uni-loading"]},null,2);default:return null}}));return{Icon:o}}(e),n=Om(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return Vi(xs,{name:"uni-fade"},{default:()=>[Ho(Vi("uni-toast",{"data-duration":r},[o?Vi("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Jc},null,40,["onTouchmove"]):"",s||t.value?Vi("div",{class:"uni-toast"},[s?Vi("img",{src:s,class:dv},null,10,["src"]):t.value,Vi("p",{class:"uni-toast__content"},[i])]):Vi("div",{class:"uni-sample-toast"},[Vi("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[js,n.value]])]})}}});let mv,vv,yv="";const bv=bt();function _v(e){mv?f(mv,e):(mv=Tn(f(e,{visible:!1})),ao((()=>{bv.run((()=>{Do([()=>mv.visible,()=>mv.duration],(([e,t])=>{if(e){if(vv&&clearTimeout(vv),"onShowLoading"===yv)return;vv=setTimeout((()=>{Ev("onHideToast")}),t)}else vv&&clearTimeout(vv)}))})),Vv.on("onHidePopup",(()=>Ev("onHidePopup"))),Cm(gv,mv,(()=>{})).mount(km("u-a-t"))}))),setTimeout((()=>{mv.visible=!0}),10)}const wv=nd("showToast",((e,{resolve:t,reject:n})=>{_v(e),yv="onShowToast",t()}),0,ph),xv={icon:"loading",duration:1e8,image:""},Sv=nd("showLoading",((e,{resolve:t,reject:n})=>{f(e,xv),_v(e),yv="onShowLoading",t()}),0,fh),Tv=nd("hideLoading",((e,{resolve:t,reject:n})=>{Ev("onHideLoading"),t()}));function Ev(e){const{t:t}=dc();if(!yv)return;let n="";"onHideToast"===e&&"onShowToast"!==yv?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==yv&&(n=t("uni.showLoading.unpaired")),n||(yv="",setTimeout((()=>{mv.visible=!1}),10))}const Cv=nd("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${uf(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${uf(t.substring(4,t.length-1))}')`:uf(t),n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function kv(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Vv.emit(Me,{titleText:t})}Ro(t),dr(t)}const Ov=nd("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(b(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const s=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),s(t-10)}))};s(t)}(t||e||0,n),o()}),0,uh),Av=Gu({name:"TabBar",setup(){const e=Fn([]),t=ig(),n=function(e,t){const n=On(e),o=n?Tn(ov(e)):ov(e);return __uniConfig.darkmode&&n&&Do(e,(e=>{const t=ov(e);for(const n in t)o[n]=t[n]})),t&&tv(t),o}(t,(()=>{const e=ov(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}Fn(f({type:"midButton"},e.midButton)),Ro(n)}(n,e),function(e){Do((()=>e.shown),(t=>{ru({"--window-bottom":dg(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Ro((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=We(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?ev({from:"tabBar",url:i,tabBarText:r}):yu("onTabItemTap",{index:n,text:r,pagePath:o})}}(zl(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=hs((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||ug&&n&&"none"!==n&&(t=Bv[n]),{backgroundColor:t||$v,backdropFilter:"none"!==n?"blur(10px)":n}})),n=hs((()=>{const{borderStyle:t,borderColor:n}=e;return n&&b(n)?{backgroundColor:n}:{backgroundColor:Iv[t]||t}})),o=hs((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Sr((()=>{n.iconfontSrc&&Cv({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return Vi("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[Lv(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)}))}(n,o,e);return Vi("uni-tabbar",{class:"uni-tabbar-"+n.position},[Vi("div",{class:"uni-tabbar",style:r.value},[Vi("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),Vi("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const $v="#f7f7fa",Pv="rgb(0, 0, 0, 0.8)",Mv="rgb(250, 250, 250, 0.8)",Bv={dark:Pv,light:Mv,extralight:Mv},Iv={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Lv(e,t,n,o,r,i){const{height:s}=i;return Vi("div",{class:"uni-tabbar__bd",style:{height:s}},[n?Rv(n,o||Pv,r,i):t&&jv(t,r,i),r.text&&Nv(e,r,i),r.redDot&&Dv(r.badge)],4)}function jv(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return Vi("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&Vi("img",{src:uf(e)},null,8,["src"])],6)}function Rv(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return Vi("div",{class:l,style:c},["midButton"!==i&&Vi("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Nv(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return Vi("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function Dv(e){return Vi("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Fv="0px",Uv=Gu({name:"Layout",setup(e,{emit:t}){const n=Fn(null);ou({"--status-bar-height":Fv,"--top-window-height":Fv,"--window-left":Fv,"--window-right":Fv,"--window-margin":Fv,"--tab-bar-height":Fv});const o=function(){const e=zl();return{routeKey:hs((()=>wg("/"+e.meta.route,og()))),isTabBar:hs((()=>e.meta.isTabBar)),routeCache:Sg}}(),{layoutState:r,windowState:i}=function(){ng();{const e=Tn({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Do((()=>e.marginWidth),(e=>ou({"--window-margin":e+"px"}))),Do((()=>e.leftWindowWidth+e.marginWidth),(e=>{ou({"--window-left":e+"px"})})),Do((()=>e.rightWindowWidth+e.marginWidth),(e=>{ou({"--window-right":e+"px"})})),{layoutState:e,windowState:hs((()=>({})))}}}();!function(e,t){const n=ng();function o(){const o=document.body.clientWidth,r=mg();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=Tu(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((p(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,ao((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,ao((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Do([()=>n.path],o),Sr((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=ng(),n=ig(),o=hs((()=>t.meta.isTabBar&&n.shown));return ou({"--tab-bar-height":n.height}),o}(),a=function(e){const t=Fn(!1);return hs((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(s);return()=>{const e=function(e,t,n,o,r,i){const s=function({routeKey:e,isTabBar:t,routeCache:n}){return Vi(Vl,null,{default:So((({Component:o})=>[(Pi(),ji(ur,{matchBy:"key",cache:n},[(Pi(),ji(Po(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e);return s}(o),t=function(e){return Ho(Vi(Av,null,null,512),[[js,e.value]])}(s);return Vi("uni-app",{ref:n,class:a.value},[e,t],2)}}});const qv=f(Oc,{publishHandler(e,t,n){Vv.subscribeHandler(e,t,n)}}),Vv=f(Ru,{publishHandler(e,t,n){qv.subscribeHandler(e,t,n)}}),Hv=Gu({name:"PageBody",setup(e,t){const n=!1,o=Fn(null);return Do((()=>n.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>Vi(Ei,null,[!1,Vi("uni-page-wrapper",o.value,[Vi("uni-page-body",null,[Br(t.slots,"default")])],16)])}}),zv=Gu({name:"Page",setup(e,t){const n=tg(og());n.navigationBar;const o={};return kv(n),()=>Vi("uni-page",{"data-page":n.route,style:o},[Wv(t)])}});function Wv(e){return Pi(),ji(Hv,{key:0},{default:So((()=>[Br(e.slots,"page")])),_:3})}const Yv={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=fd;const Xv=Object.assign({}),Kv=Object.assign;window.__uniConfig=Kv({easycom:{autoscan:!0,custom:{"^uv-(.*)":"@climblee/uv-ui/components/uv-$1/uv-$1.vue","^u-([^-].*)":"@climblee/uv-ui/components/uv-$1/uv-$1.vue","^u--(.*)":"@climblee/uv-ui/components/uv-$1/uv-$1.vue"}},globalStyle:{rpxCalcMaxDeviceWidth:8640,titleNView:!1,navigationBar:{backgroundColor:"transparent",type:"default",style:"custom",titleColor:"#000000"},isNVue:!1},tabBar:{position:"bottom",color:"#7A7E83",selectedColor:"#3cc51f",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",list:[{}],selectedIndex:0,shown:!0},compilerVersion:"4.23"},{appId:"__UNI__F907271",appName:"com.ctyk.mobile",appVersion:"1.0.0",appVersionCode:"100",async:Yv,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Xv).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Kv(e[n]||(e[n]={}),Xv[t].default),e}),{}),router:{mode:"hash",base:"/ywzz/",assets:"assets",routerBase:"/ywzz/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Zv={delay:Yv.delay,timeout:Yv.timeout,suspensible:Yv.suspensible};Yv.loading&&(Zv.loadingComponent={name:"SystemAsyncLoading",render:()=>Vi(Ao(Yv.loading))}),Yv.error&&(Zv.errorComponent={name:"SystemAsyncError",render:()=>Vi(Ao(Yv.error))});const Gv=()=>o((()=>import("./pages-index-index.qZ0qV1v6.js")),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url).then((e=>zg(e.default||e))),Jv=ir(Kv({loader:Gv},Zv)),Qv=()=>o((()=>import("./pages-consult-index.CCsjl5z9.js")),__vite__mapDeps([8,1,2,3,4,5,6,9,10,11,12,13,14,15,16,17,18,19]),import.meta.url).then((e=>zg(e.default||e))),ey=ir(Kv({loader:Qv},Zv)),ty=()=>o((()=>import("./pages-consult-report.CeVyLx1y.js")),__vite__mapDeps([20,1,2,3,4,5,6,13,14,15,9,10,11,12,16,17,21]),import.meta.url).then((e=>zg(e.default||e))),ny=ir(Kv({loader:ty},Zv)),oy=()=>o((()=>import("./pages-consult-medication.KiHFwP9W.js")),__vite__mapDeps([22,1,2,3,4,5,6,9,10,11,12,23]),import.meta.url).then((e=>zg(e.default||e))),ry=ir(Kv({loader:oy},Zv)),iy=()=>o((()=>import("./pages-consult-info.BC-IZLk8.js")),__vite__mapDeps([24,1,2,3,4,5,6,9,10,11,12,16,17,25,19]),import.meta.url).then((e=>zg(e.default||e))),sy=ir(Kv({loader:iy},Zv)),ay=()=>o((()=>import("./pages-login-index.B_3eJiLv.js")),__vite__mapDeps([26,10,4,2,3,5,11,27]),import.meta.url).then((e=>zg(e.default||e))),ly=ir(Kv({loader:ay},Zv)),cy=()=>o((()=>import("./pages-report-index.D_UEOGH_.js")),__vite__mapDeps([28,29,2,3,14,30]),import.meta.url).then((e=>zg(e.default||e))),uy=ir(Kv({loader:cy},Zv)),fy=()=>o((()=>import("./pages-report-info-index.BEIAciPT.js")),__vite__mapDeps([31,2,3,29,14,32]),import.meta.url).then((e=>zg(e.default||e))),dy=ir(Kv({loader:fy},Zv));function hy(e,t){return Pi(),ji(zv,null,{page:So((()=>[Vi(e,Kv({},t,{ref:"page"}),null,512)])),_:1})}function py(e,t){return vy({...e,method:"GET"},t)}function gy(e,t){return vy({...e,method:"POST"},t)}function my(e,t){return vy({...e,method:"UPLOAD"},t)}function vy(e,t){return new Promise(((n,o)=>{const{$uv:r}=uni;r.http.request({...e,custom:t}).then((e=>{n(e)})).catch((e=>{o(e)}))}))}function yy(e){return gy({url:"/gateway/shc/api/tests/message",data:e})}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(Jv,t)}},loader:Gv,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"首页",type:"default"},isNVue:!1}},{path:"/pages/consult/index",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(ey,t)}},loader:Qv,meta:{navigationBar:{titleText:"智慧分诊",type:"default"},isNVue:!1}},{path:"/pages/consult/report",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(ny,t)}},loader:ty,meta:{navigationBar:{titleText:"报告解读",type:"default"},isNVue:!1}},{path:"/pages/consult/medication",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(ry,t)}},loader:oy,meta:{navigationBar:{titleText:"智能问药",type:"default"},isNVue:!1}},{path:"/pages/consult/info",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(sy,t)}},loader:iy,meta:{navigationBar:{titleText:"信息咨询",type:"default"},isNVue:!1}},{path:"/pages/login/index",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(ly,t)}},loader:ay,meta:{navigationBar:{titleText:"登录",type:"default"},isNVue:!1}},{path:"/pages/report/index",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(uy,t)}},loader:cy,meta:{navigationBar:{titleText:"报告",type:"default"},isNVue:!1}},{path:"/pages/report-info/index",component:{setup(){const e=Ug(),t=e&&e.$route&&e.$route.query||{};return()=>hy(dy,t)}},loader:fy,meta:{navigationBar:{titleText:"报告解读",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const by="TOKEN__",_y="USER_ID__",wy="PRIVAYE_KEY",xy="PUBLICKEY_KEY",Sy="AGREEMENT_KEY",Ty="EXPIRETIME_KEY",Ey="USER__INFO__";function Cy(){return Ay(by)}function ky(){return Ay(_y)}function Oy(){return Ay(Sy)}function Ay(e){return mm(e)}function $y(e,t){return pm(e,t)}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Py=BigInt(0),My=BigInt(1),By=BigInt(2);function Iy(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function Ly(e){if(!Iy(e))throw new Error("Uint8Array expected")}const jy=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function Ry(e){Ly(e);let t="";for(let n=0;n<e.length;n++)t+=jy[e[n]];return t}function Ny(e){const t=e.toString(16);return 1&t.length?`0${t}`:t}function Dy(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);return BigInt(""===e?"0":`0x${e}`)}const Fy={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function Uy(e){return e>=Fy._0&&e<=Fy._9?e-Fy._0:e>=Fy._A&&e<=Fy._F?e-(Fy._A-10):e>=Fy._a&&e<=Fy._f?e-(Fy._a-10):void 0}function qy(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const o=new Uint8Array(n);for(let r=0,i=0;r<n;r++,i+=2){const t=Uy(e.charCodeAt(i)),n=Uy(e.charCodeAt(i+1));if(void 0===t||void 0===n){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}o[r]=16*t+n}return o}function Vy(e){return Dy(Ry(e))}function Hy(e){return Ly(e),Dy(Ry(Uint8Array.from(e).reverse()))}function zy(e,t){return qy(e.toString(16).padStart(2*t,"0"))}function Wy(e,t){return zy(e,t).reverse()}function Yy(e,t,n){let o;if("string"==typeof t)try{o=qy(t)}catch(i){throw new Error(`${e} must be valid hex string, got "${t}". Cause: ${i}`)}else{if(!Iy(t))throw new Error(`${e} must be hex string or Uint8Array`);o=Uint8Array.from(t)}const r=o.length;if("number"==typeof n&&r!==n)throw new Error(`${e} expected ${n} bytes, got ${r}`);return o}function Xy(...e){let t=0;for(let o=0;o<e.length;o++){const n=e[o];Ly(n),t+=n.length}const n=new Uint8Array(t);for(let o=0,r=0;o<e.length;o++){const t=e[o];n.set(t,r),r+=t.length}return n}const Ky=e=>(By<<BigInt(e-1))-My,Zy=e=>new Uint8Array(e),Gy=e=>Uint8Array.from(e);function Jy(e,t,n){if("number"!=typeof e||e<2)throw new Error("hashLen must be a number");if("number"!=typeof t||t<2)throw new Error("qByteLen must be a number");if("function"!=typeof n)throw new Error("hmacFn must be a function");let o=Zy(e),r=Zy(e),i=0;const s=()=>{o.fill(1),r.fill(0),i=0},a=(...e)=>n(r,o,...e),l=(e=Zy())=>{r=a(Gy([0]),e),o=a(),0!==e.length&&(r=a(Gy([1]),e),o=a())},c=()=>{if(i++>=1e3)throw new Error("drbg: tried 1000 values");let e=0;const n=[];for(;e<t;){o=a();const t=o.slice();n.push(t),e+=o.length}return Xy(...n)};return(e,t)=>{let n;for(s(),l(e);!(n=t(c()));)l();return s(),n}}const Qy={bigint:e=>"bigint"==typeof e,function:e=>"function"==typeof e,boolean:e=>"boolean"==typeof e,string:e=>"string"==typeof e,stringOrUint8Array:e=>"string"==typeof e||Iy(e),isSafeInteger:e=>Number.isSafeInteger(e),array:e=>Array.isArray(e),field:(e,t)=>t.Fp.isValid(e),hash:e=>"function"==typeof e&&Number.isSafeInteger(e.outputLen)};function eb(e,t,n={}){const o=(t,n,o)=>{const r=Qy[n];if("function"!=typeof r)throw new Error(`Invalid validator "${n}", expected function`);const i=e[t];if(!(o&&void 0===i||r(i,e)))throw new Error(`Invalid param ${String(t)}=${i} (${typeof i}), expected ${n}`)};for(const[r,i]of Object.entries(t))o(r,i,!1);for(const[r,i]of Object.entries(n))o(r,i,!0);return e}const tb=Object.freeze(Object.defineProperty({__proto__:null,abytes:Ly,bitGet:function(e,t){return e>>BigInt(t)&My},bitLen:function(e){let t;for(t=0;e>Py;e>>=My,t+=1);return t},bitMask:Ky,bitSet:function(e,t,n){return e|(n?My:Py)<<BigInt(t)},bytesToHex:Ry,bytesToNumberBE:Vy,bytesToNumberLE:Hy,concatBytes:Xy,createHmacDrbg:Jy,ensureBytes:Yy,equalBytes:function(e,t){if(e.length!==t.length)return!1;let n=0;for(let o=0;o<e.length;o++)n|=e[o]^t[o];return 0===n},hexToBytes:qy,hexToNumber:Dy,isBytes:Iy,numberToBytesBE:zy,numberToBytesLE:Wy,numberToHexUnpadded:Ny,numberToVarBytesBE:function(e){return qy(Ny(e))},utf8ToBytes:function(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))},validateObject:eb},Symbol.toStringTag,{value:"Module"})),nb=BigInt(0),ob=BigInt(1),rb=BigInt(2),ib=BigInt(3),sb=BigInt(4),ab=BigInt(5),lb=BigInt(8);
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function cb(e,t){const n=e%t;return n>=nb?n:t+n}function ub(e,t,n){if(n<=nb||t<nb)throw new Error("Expected power/modulo > 0");if(n===ob)return nb;let o=ob;for(;t>nb;)t&ob&&(o=o*e%n),e=e*e%n,t>>=ob;return o}function fb(e,t){if(e===nb||t<=nb)throw new Error(`invert: expected positive integers, got n=${e} mod=${t}`);let n=cb(e,t),o=t,r=nb,i=ob;for(;n!==nb;){const e=o%n,t=r-i*(o/n);o=n,n=e,r=i,i=t}if(o!==ob)throw new Error("invert: does not exist");return cb(r,t)}function db(e){if(e%sb===ib){const t=(e+ob)/sb;return function(e,n){const o=e.pow(n,t);if(!e.eql(e.sqr(o),n))throw new Error("Cannot find square root");return o}}if(e%lb===ab){const t=(e-ab)/lb;return function(e,n){const o=e.mul(n,rb),r=e.pow(o,t),i=e.mul(n,r),s=e.mul(e.mul(i,rb),r),a=e.mul(i,e.sub(s,e.ONE));if(!e.eql(e.sqr(a),n))throw new Error("Cannot find square root");return a}}return function(e){const t=(e-ob)/rb;let n,o,r;for(n=e-ob,o=0;n%rb===nb;n/=rb,o++);for(r=rb;r<e&&ub(r,t,e)!==e-ob;r++);if(1===o){const t=(e+ob)/sb;return function(e,n){const o=e.pow(n,t);if(!e.eql(e.sqr(o),n))throw new Error("Cannot find square root");return o}}const i=(n+ob)/rb;return function(e,s){if(e.pow(s,t)===e.neg(e.ONE))throw new Error("Cannot find square root");let a=o,l=e.pow(e.mul(e.ONE,r),n),c=e.pow(s,i),u=e.pow(s,n);for(;!e.eql(u,e.ONE);){if(e.eql(u,e.ZERO))return e.ZERO;let t=1;for(let o=e.sqr(u);t<a&&!e.eql(o,e.ONE);t++)o=e.sqr(o);const n=e.pow(l,ob<<BigInt(a-t-1));l=e.sqr(n),c=e.mul(c,n),u=e.mul(u,l),a=t}return c}}(e)}BigInt(9),BigInt(16);const hb=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function pb(e,t){const n=void 0!==t?t:e.toString(2).length;return{nBitLength:n,nByteLength:Math.ceil(n/8)}}function gb(e,t,n=!1,o={}){if(e<=nb)throw new Error(`Expected Field ORDER > 0, got ${e}`);const{nBitLength:r,nByteLength:i}=pb(e,t);if(i>2048)throw new Error("Field lengths over 2048 bytes are not supported");const s=db(e),a=Object.freeze({ORDER:e,BITS:r,BYTES:i,MASK:Ky(r),ZERO:nb,ONE:ob,create:t=>cb(t,e),isValid:t=>{if("bigint"!=typeof t)throw new Error("Invalid field element: expected bigint, got "+typeof t);return nb<=t&&t<e},is0:e=>e===nb,isOdd:e=>(e&ob)===ob,neg:t=>cb(-t,e),eql:(e,t)=>e===t,sqr:t=>cb(t*t,e),add:(t,n)=>cb(t+n,e),sub:(t,n)=>cb(t-n,e),mul:(t,n)=>cb(t*n,e),pow:(e,t)=>function(e,t,n){if(n<nb)throw new Error("Expected power > 0");if(n===nb)return e.ONE;if(n===ob)return t;let o=e.ONE,r=t;for(;n>nb;)n&ob&&(o=e.mul(o,r)),r=e.sqr(r),n>>=ob;return o}(a,e,t),div:(t,n)=>cb(t*fb(n,e),e),sqrN:e=>e*e,addN:(e,t)=>e+t,subN:(e,t)=>e-t,mulN:(e,t)=>e*t,inv:t=>fb(t,e),sqrt:o.sqrt||(e=>s(a,e)),invertBatch:e=>function(e,t){const n=new Array(t.length),o=t.reduce(((t,o,r)=>e.is0(o)?t:(n[r]=t,e.mul(t,o))),e.ONE),r=e.inv(o);return t.reduceRight(((t,o,r)=>e.is0(o)?t:(n[r]=e.mul(t,n[r]),e.mul(t,o))),r),n}(a,e),cmov:(e,t,n)=>n?t:e,toBytes:e=>n?Wy(e,i):zy(e,i),fromBytes:e=>{if(e.length!==i)throw new Error(`Fp.fromBytes: expected ${i}, got ${e.length}`);return n?Hy(e):Vy(e)}});return Object.freeze(a)}function mb(e){if("bigint"!=typeof e)throw new Error("field order must be bigint");const t=e.toString(2).length;return Math.ceil(t/8)}function vb(e){const t=mb(e);return t+Math.ceil(t/2)}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const yb=BigInt(0),bb=BigInt(1);function _b(e){return eb(e.Fp,hb.reduce(((e,t)=>(e[t]="function",e)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"})),eb(e,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...pb(e.n,e.nBitLength),...e,p:e.Fp.ORDER})}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const{bytesToNumberBE:wb,hexToBytes:xb}=tb,Sb={Err:class extends Error{constructor(e=""){super(e)}},_parseInt(e){const{Err:t}=Sb;if(e.length<2||2!==e[0])throw new t("Invalid signature integer tag");const n=e[1],o=e.subarray(2,n+2);if(!n||o.length!==n)throw new t("Invalid signature integer: wrong length");if(128&o[0])throw new t("Invalid signature integer: negative");if(0===o[0]&&!(128&o[1]))throw new t("Invalid signature integer: unnecessary leading zero");return{d:wb(o),l:e.subarray(n+2)}},toSig(e){const{Err:t}=Sb,n="string"==typeof e?xb(e):e;Ly(n);let o=n.length;if(o<2||48!=n[0])throw new t("Invalid signature tag");if(n[1]!==o-2)throw new t("Invalid signature: incorrect length");const{d:r,l:i}=Sb._parseInt(n.subarray(2)),{d:s,l:a}=Sb._parseInt(i);if(a.length)throw new t("Invalid signature: left bytes after parsing");return{r:r,s:s}},hexFromSig(e){const t=e=>8&Number.parseInt(e[0],16)?"00"+e:e,n=e=>{const t=e.toString(16);return 1&t.length?`0${t}`:t},o=t(n(e.s)),r=t(n(e.r)),i=o.length/2,s=r.length/2,a=n(i),l=n(s);return`30${n(s+i+4)}02${l}${r}02${a}${o}`}},Tb=BigInt(0),Eb=BigInt(1);BigInt(2);const Cb=BigInt(3);function kb(e){const t=function(e){const t=_b(e);eb(t,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:n,Fp:o,a:r}=t;if(n){if(!o.eql(r,o.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof n||"bigint"!=typeof n.beta||"function"!=typeof n.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...t})}(e),{Fp:n}=t,o=t.toBytes||((e,t,o)=>{const r=t.toAffine();return Xy(Uint8Array.from([4]),n.toBytes(r.x),n.toBytes(r.y))}),r=t.fromBytes||(e=>{const t=e.subarray(1);return{x:n.fromBytes(t.subarray(0,n.BYTES)),y:n.fromBytes(t.subarray(n.BYTES,2*n.BYTES))}});function i(e){const{a:o,b:r}=t,i=n.sqr(e),s=n.mul(i,e);return n.add(n.add(s,n.mul(e,o)),r)}if(!n.eql(n.sqr(t.Gy),i(t.Gx)))throw new Error("bad generator point: equation left != right");function s(e){return"bigint"==typeof e&&Tb<e&&e<t.n}function a(e){if(!s(e))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function l(e){const{allowedPrivateKeyLengths:n,nByteLength:o,wrapPrivateKey:r,n:i}=t;if(n&&"bigint"!=typeof e){if(Iy(e)&&(e=Ry(e)),"string"!=typeof e||!n.includes(e.length))throw new Error("Invalid key");e=e.padStart(2*o,"0")}let s;try{s="bigint"==typeof e?e:Vy(Yy("private key",e,o))}catch(l){throw new Error(`private key must be ${o} bytes, hex or bigint, not ${typeof e}`)}return r&&(s=cb(s,i)),a(s),s}const c=new Map;function u(e){if(!(e instanceof f))throw new Error("ProjectivePoint expected")}class f{constructor(e,t,o){if(this.px=e,this.py=t,this.pz=o,null==e||!n.isValid(e))throw new Error("x required");if(null==t||!n.isValid(t))throw new Error("y required");if(null==o||!n.isValid(o))throw new Error("z required")}static fromAffine(e){const{x:t,y:o}=e||{};if(!e||!n.isValid(t)||!n.isValid(o))throw new Error("invalid affine point");if(e instanceof f)throw new Error("projective point not allowed");const r=e=>n.eql(e,n.ZERO);return r(t)&&r(o)?f.ZERO:new f(t,o,n.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(e){const t=n.invertBatch(e.map((e=>e.pz)));return e.map(((e,n)=>e.toAffine(t[n]))).map(f.fromAffine)}static fromHex(e){const t=f.fromAffine(r(Yy("pointHex",e)));return t.assertValidity(),t}static fromPrivateKey(e){return f.BASE.multiply(l(e))}_setWindowSize(e){this._WINDOW_SIZE=e,c.delete(this)}assertValidity(){if(this.is0()){if(t.allowInfinityPoint&&!n.is0(this.py))return;throw new Error("bad point: ZERO")}const{x:e,y:o}=this.toAffine();if(!n.isValid(e)||!n.isValid(o))throw new Error("bad point: x or y not FE");const r=n.sqr(o),s=i(e);if(!n.eql(r,s))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:e}=this.toAffine();if(n.isOdd)return!n.isOdd(e);throw new Error("Field doesn't support isOdd")}equals(e){u(e);const{px:t,py:o,pz:r}=this,{px:i,py:s,pz:a}=e,l=n.eql(n.mul(t,a),n.mul(i,r)),c=n.eql(n.mul(o,a),n.mul(s,r));return l&&c}negate(){return new f(this.px,n.neg(this.py),this.pz)}double(){const{a:e,b:o}=t,r=n.mul(o,Cb),{px:i,py:s,pz:a}=this;let l=n.ZERO,c=n.ZERO,u=n.ZERO,d=n.mul(i,i),h=n.mul(s,s),p=n.mul(a,a),g=n.mul(i,s);return g=n.add(g,g),u=n.mul(i,a),u=n.add(u,u),l=n.mul(e,u),c=n.mul(r,p),c=n.add(l,c),l=n.sub(h,c),c=n.add(h,c),c=n.mul(l,c),l=n.mul(g,l),u=n.mul(r,u),p=n.mul(e,p),g=n.sub(d,p),g=n.mul(e,g),g=n.add(g,u),u=n.add(d,d),d=n.add(u,d),d=n.add(d,p),d=n.mul(d,g),c=n.add(c,d),p=n.mul(s,a),p=n.add(p,p),d=n.mul(p,g),l=n.sub(l,d),u=n.mul(p,h),u=n.add(u,u),u=n.add(u,u),new f(l,c,u)}add(e){u(e);const{px:o,py:r,pz:i}=this,{px:s,py:a,pz:l}=e;let c=n.ZERO,d=n.ZERO,h=n.ZERO;const p=t.a,g=n.mul(t.b,Cb);let m=n.mul(o,s),v=n.mul(r,a),y=n.mul(i,l),b=n.add(o,r),_=n.add(s,a);b=n.mul(b,_),_=n.add(m,v),b=n.sub(b,_),_=n.add(o,i);let w=n.add(s,l);return _=n.mul(_,w),w=n.add(m,y),_=n.sub(_,w),w=n.add(r,i),c=n.add(a,l),w=n.mul(w,c),c=n.add(v,y),w=n.sub(w,c),h=n.mul(p,_),c=n.mul(g,y),h=n.add(c,h),c=n.sub(v,h),h=n.add(v,h),d=n.mul(c,h),v=n.add(m,m),v=n.add(v,m),y=n.mul(p,y),_=n.mul(g,_),v=n.add(v,y),y=n.sub(m,y),y=n.mul(p,y),_=n.add(_,y),m=n.mul(v,_),d=n.add(d,m),m=n.mul(w,_),c=n.mul(b,c),c=n.sub(c,m),m=n.mul(b,v),h=n.mul(w,h),h=n.add(h,m),new f(c,d,h)}subtract(e){return this.add(e.negate())}is0(){return this.equals(f.ZERO)}wNAF(e){return h.wNAFCached(this,c,e,(e=>{const t=n.invertBatch(e.map((e=>e.pz)));return e.map(((e,n)=>e.toAffine(t[n]))).map(f.fromAffine)}))}multiplyUnsafe(e){const o=f.ZERO;if(e===Tb)return o;if(a(e),e===Eb)return this;const{endo:r}=t;if(!r)return h.unsafeLadder(this,e);let{k1neg:i,k1:s,k2neg:l,k2:c}=r.splitScalar(e),u=o,d=o,p=this;for(;s>Tb||c>Tb;)s&Eb&&(u=u.add(p)),c&Eb&&(d=d.add(p)),p=p.double(),s>>=Eb,c>>=Eb;return i&&(u=u.negate()),l&&(d=d.negate()),d=new f(n.mul(d.px,r.beta),d.py,d.pz),u.add(d)}multiply(e){a(e);let o,r,i=e;const{endo:s}=t;if(s){const{k1neg:e,k1:t,k2neg:a,k2:l}=s.splitScalar(i);let{p:c,f:u}=this.wNAF(t),{p:d,f:p}=this.wNAF(l);c=h.constTimeNegate(e,c),d=h.constTimeNegate(a,d),d=new f(n.mul(d.px,s.beta),d.py,d.pz),o=c.add(d),r=u.add(p)}else{const{p:e,f:t}=this.wNAF(i);o=e,r=t}return f.normalizeZ([o,r])[0]}multiplyAndAddUnsafe(e,t,n){const o=f.BASE,r=(e,t)=>t!==Tb&&t!==Eb&&e.equals(o)?e.multiply(t):e.multiplyUnsafe(t),i=r(this,t).add(r(e,n));return i.is0()?void 0:i}toAffine(e){const{px:t,py:o,pz:r}=this,i=this.is0();null==e&&(e=i?n.ONE:n.inv(r));const s=n.mul(t,e),a=n.mul(o,e),l=n.mul(r,e);if(i)return{x:n.ZERO,y:n.ZERO};if(!n.eql(l,n.ONE))throw new Error("invZ was invalid");return{x:s,y:a}}isTorsionFree(){const{h:e,isTorsionFree:n}=t;if(e===Eb)return!0;if(n)return n(f,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:e,clearCofactor:n}=t;return e===Eb?this:n?n(f,this):this.multiplyUnsafe(t.h)}toRawBytes(e=!0){return this.assertValidity(),o(f,this,e)}toHex(e=!0){return Ry(this.toRawBytes(e))}}f.BASE=new f(t.Gx,t.Gy,n.ONE),f.ZERO=new f(n.ZERO,n.ONE,n.ZERO);const d=t.nBitLength,h=function(e,t){const n=(e,t)=>{const n=t.negate();return e?n:t},o=e=>({windows:Math.ceil(t/e)+1,windowSize:2**(e-1)});return{constTimeNegate:n,unsafeLadder(t,n){let o=e.ZERO,r=t;for(;n>yb;)n&bb&&(o=o.add(r)),r=r.double(),n>>=bb;return o},precomputeWindow(e,t){const{windows:n,windowSize:r}=o(t),i=[];let s=e,a=s;for(let o=0;o<n;o++){a=s,i.push(a);for(let e=1;e<r;e++)a=a.add(s),i.push(a);s=a.double()}return i},wNAF(t,r,i){const{windows:s,windowSize:a}=o(t);let l=e.ZERO,c=e.BASE;const u=BigInt(2**t-1),f=2**t,d=BigInt(t);for(let e=0;e<s;e++){const t=e*a;let o=Number(i&u);i>>=d,o>a&&(o-=f,i+=bb);const s=t,h=t+Math.abs(o)-1,p=e%2!=0,g=o<0;0===o?c=c.add(n(p,r[s])):l=l.add(n(g,r[h]))}return{p:l,f:c}},wNAFCached(e,t,n,o){const r=e._WINDOW_SIZE||1;let i=t.get(e);return i||(i=this.precomputeWindow(e,r),1!==r&&t.set(e,o(i))),this.wNAF(r,i,n)}}}(f,t.endo?Math.ceil(d/2):d);return{CURVE:t,ProjectivePoint:f,normPrivateKeyToScalar:l,weierstrassEquation:i,isWithinCurveOrder:s}}BigInt(4);var Ob=Object.defineProperty,Ab=(e,t)=>{for(var n in t)Ob(e,n,{get:t[n],enumerable:!0})},$b={};Ab($b,{EmptyArray:()=>k_,arrayToHex:()=>y_,arrayToUtf8:()=>b_,calculateSharedKey:()=>E_,comparePublicKeyHex:()=>x_,compressPublicKeyHex:()=>g_,doDecrypt:()=>$_,doEncrypt:()=>O_,doSignature:()=>P_,doVerifySignature:()=>M_,generateKeyPairHex:()=>p_,getHash:()=>I_,getPoint:()=>R_,getPublicKeyFromPrivateKey:()=>j_,getZ:()=>B_,hexToArray:()=>__,initRNGPool:()=>zb,leftPad:()=>v_,precomputePublicKey:()=>L_,utf8ToHex:()=>m_,verifyPublicKey:()=>w_});var Pb=BigInt(0),Mb=BigInt(1),Bb=BigInt(2);BigInt(3);var Ib=class{constructor(e=null,t="00",n="00",o=""){this.tlv=e,this.t=t,this.l=n,this.v=o}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const e=this.v.length/2;let t=e.toString(16);if(t.length%2==1&&(t="0"+t),e<128)return t;return(128+t.length/2).toString(16)+t}getValue(){return""}},Lb=class extends Ib{constructor(e){super(),this.t="02",e&&(this.v=function(e){let t=e.toString(16);if("-"!==t[0])t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{t=t.substring(1);let n=t.length;n%2==1?n+=1:t.match(/^[0-7]/)||(n+=2);let o="";for(let e=0;e<n;e++)o+="f";t=((Dy(o)^e)+Mb).toString(16).replace(/^-/,"")}return t}(e))}getValue(){return this.v}},jb=class extends Ib{constructor(e){super(),t(this,"hV",""),this.s=e,this.t="04",e&&(this.v=e.toLowerCase())}getValue(){return this.v}},Rb=class extends Ib{constructor(e){super(),t(this,"t","30"),this.asn1Array=e}getValue(){return this.v=this.asn1Array.map((e=>e.getEncodedHex())).join(""),this.v}};function Nb(e,t){if(+e[t+2]<8)return 1;const n=e.slice(t+2,t+6).slice(0,2);return 2*(parseInt(n,16)-128)}function Db(e,t){const n=Nb(e,t),o=e.substring(t+2,t+2+2*n);if(!o)return-1;return+(+o[0]<8?Dy(o):Dy(o.substring(2))).toString()}function Fb(e,t){return t+2*(Nb(e,t)+1)}function Ub(e,t,n,o){const r=new Lb(e),i=new Lb(t),s=new jb(n),a=new jb(o);return new Rb([r,i,s,a]).getEncodedHex()}var qb,Vb=16384,Hb=new Uint8Array(0);async function zb(){if("crypto"in globalThis)qb=globalThis.crypto;else if(!(Hb.length>Vb/2))if("wx"in globalThis&&"getRandomValues"in globalThis.wx)Hb=await new Promise((e=>{wx.getRandomValues({length:Vb,success(t){e(new Uint8Array(t.randomValues))}})}));else try{if(globalThis.crypto)qb=globalThis.crypto;else{const e=await o((()=>import("..-__vite-browser-external.FmFgRqLi.js")),[],import.meta.url);qb=e.webcrypto}const e=new Uint8Array(Vb);qb.getRandomValues(e),Hb=e}catch(e){throw new Error("no available csprng, abort.")}}zb();var Wb=e=>e instanceof Uint8Array,Yb=e=>new DataView(e.buffer,e.byteOffset,e.byteLength);if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");var Xb=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function Kb(e){if(!Wb(e))throw new Error("Uint8Array expected");let t="";for(let n=0;n<e.length;n++)t+=Xb[e[n]];return t}function Zb(e){if("string"==typeof e&&(e=function(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}(e)),!Wb(e))throw new Error("expected Uint8Array, got "+typeof e);return e}var Gb=class{clone(){return this._cloneInto()}};var Jb=(e,t,n)=>e&t|e&n|t&n,Qb=(e,t,n)=>e^t^n,e_=(e,t,n)=>e&t|~e&n;function t_(e,t){const n=31&t;return e<<n|e>>>32-n}function n_(e){return e^t_(e,9)^t_(e,17)}var o_=class extends Gb{constructor(e,n,o,r){super(),t(this,"buffer"),t(this,"view"),t(this,"finished",!1),t(this,"length",0),t(this,"pos",0),t(this,"destroyed",!1),this.blockLen=e,this.outputLen=n,this.padOffset=o,this.isLE=r,this.buffer=new Uint8Array(e),this.view=Yb(this.buffer)}update(e){const{view:t,buffer:n,blockLen:o}=this,r=(e=Zb(e)).length;for(let i=0;i<r;){const s=Math.min(o-this.pos,r-i);if(s!==o)n.set(e.subarray(i,i+s),this.pos),this.pos+=s,i+=s,this.pos===o&&(this.process(t,0),this.pos=0);else{const t=Yb(e);for(;o<=r-i;i+=o)this.process(t,i)}}return this.length+=e.length,this.roundClean(),this}digestInto(e){this.finished=!0;const{buffer:t,view:n,blockLen:o,isLE:r}=this;let{pos:i}=this;t[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>o-i&&(this.process(n,0),i=0);for(let u=i;u<o;u++)t[u]=0;!function(e,t,n,o){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,o);const r=BigInt(32),i=BigInt(4294967295),s=Number(n>>r&i),a=Number(n&i),l=o?4:0,c=o?0:4;e.setUint32(t+l,s,o),e.setUint32(t+c,a,o)}(n,o-8,BigInt(8*this.length),r),this.process(n,0);const s=Yb(e),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const l=a/4,c=this.get();if(l>c.length)throw new Error("_sha2: outputLen bigger than state");for(let u=0;u<l;u++)s.setUint32(4*u,c[u],r)}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:n,length:o,finished:r,destroyed:i,pos:s}=this;return e.length=o,e.pos=s,e.finished=r,e.destroyed=i,o%t&&e.buffer.set(n),e}},r_=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]),i_=new Uint32Array(68),s_=new Uint32Array(64),a_=class extends o_{constructor(){super(64,32,8,!1),t(this,"A",0|r_[0]),t(this,"B",0|r_[1]),t(this,"C",0|r_[2]),t(this,"D",0|r_[3]),t(this,"E",0|r_[4]),t(this,"F",0|r_[5]),t(this,"G",0|r_[6]),t(this,"H",0|r_[7])}get(){const{A:e,B:t,C:n,D:o,E:r,F:i,G:s,H:a}=this;return[e,t,n,o,r,i,s,a]}set(e,t,n,o,r,i,s,a){this.A=0|e,this.B=0|t,this.C=0|n,this.D=0|o,this.E=0|r,this.F=0|i,this.G=0|s,this.H=0|a}process(e,t){for(let f=0;f<16;f++,t+=4)i_[f]=e.getUint32(t,!1);for(let f=16;f<68;f++)i_[f]=(n=i_[f-16]^i_[f-9]^t_(i_[f-3],15))^t_(n,15)^t_(n,23)^t_(i_[f-13],7)^i_[f-6];var n;for(let f=0;f<64;f++)s_[f]=i_[f]^i_[f+4];let{A:o,B:r,C:i,D:s,E:a,F:l,G:c,H:u}=this;for(let f=0;f<64;f++){let e=f>=0&&f<=15,t=e?2043430169:2055708042,n=t_(t_(o,12)+a+t_(t,f),7),d=n^t_(o,12),h=(e?Qb(o,r,i):Jb(o,r,i))+s+d+s_[f]|0,p=(e?Qb(a,l,c):e_(a,l,c))+u+n+i_[f]|0;s=i,i=t_(r,9),r=o,o=h,u=c,c=t_(l,19),l=a,a=n_(p)}o^=this.A,r^=this.B,i^=this.C,s^=this.D,a^=this.E,l^=this.F,c^=this.G,u^=this.H,this.set(o,r,i,s,a,l,c,u)}roundClean(){i_.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}},l_=function(e){const t=t=>e().update(Zb(t)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t}((()=>new a_)),c_=class extends Gb{constructor(e,n){super(),t(this,"oHash"),t(this,"iHash"),t(this,"blockLen"),t(this,"outputLen"),t(this,"finished",!1),t(this,"destroyed",!1);const o=Zb(n);if(this.iHash=e.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const r=this.blockLen,i=new Uint8Array(r);i.set(o.length>r?e.create().update(o).digest():o);for(let t=0;t<i.length;t++)i[t]^=54;this.iHash.update(i),this.oHash=e.create();for(let t=0;t<i.length;t++)i[t]^=106;this.oHash.update(i),i.fill(0)}update(e){return this.iHash.update(e),this}digestInto(e){this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:t,iHash:n,finished:o,destroyed:r,blockLen:i,outputLen:s}=this;return e.finished=o,e.destroyed=r,e.blockLen=i,e.outputLen=s,e.oHash=t._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}},u_=(e,t,n)=>new c_(e,t).update(n).digest();u_.create=(e,t)=>new c_(e,t);var f_=gb(BigInt("115792089210356248756420345214020892766250353991924191454421193933289684991999")),d_=function(e){const t=function(e){const t=_b(e);return eb(t,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...t})}(e),{Fp:n,n:o}=t,r=n.BYTES+1,i=2*n.BYTES+1;function s(e){return cb(e,o)}function a(e){return fb(e,o)}const{ProjectivePoint:l,normPrivateKeyToScalar:c,weierstrassEquation:u,isWithinCurveOrder:f}=kb({...t,toBytes(e,t,o){const r=t.toAffine(),i=n.toBytes(r.x),s=Xy;return o?s(Uint8Array.from([t.hasEvenY()?2:3]),i):s(Uint8Array.from([4]),i,n.toBytes(r.y))},fromBytes(e){const t=e.length,o=e[0],s=e.subarray(1);if(t!==r||2!==o&&3!==o){if(t===i&&4===o){return{x:n.fromBytes(s.subarray(0,n.BYTES)),y:n.fromBytes(s.subarray(n.BYTES,2*n.BYTES))}}throw new Error(`Point of length ${t} was invalid. Expected ${r} compressed bytes or ${i} uncompressed bytes`)}{const e=Vy(s);if(!(Tb<(a=e)&&a<n.ORDER))throw new Error("Point is not on curve");const t=u(e);let r;try{r=n.sqrt(t)}catch(l){const e=l instanceof Error?": "+l.message:"";throw new Error("Point is not on curve"+e)}return!(1&~o)!==((r&Eb)===Eb)&&(r=n.neg(r)),{x:e,y:r}}var a}}),d=e=>Ry(zy(e,t.nByteLength));function h(e){return e>o>>Eb}const p=(e,t,n)=>Vy(e.slice(t,n));class g{constructor(e,t,n){this.r=e,this.s=t,this.recovery=n,this.assertValidity()}static fromCompact(e){const n=t.nByteLength;return e=Yy("compactSignature",e,2*n),new g(p(e,0,n),p(e,n,2*n))}static fromDER(e){const{r:t,s:n}=Sb.toSig(Yy("DER",e));return new g(t,n)}assertValidity(){if(!f(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!f(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(e){return new g(this.r,this.s,e)}recoverPublicKey(e){const{r:o,s:r,recovery:i}=this,c=b(Yy("msgHash",e));if(null==i||![0,1,2,3].includes(i))throw new Error("recovery id invalid");const u=2===i||3===i?o+t.n:o;if(u>=n.ORDER)throw new Error("recovery id 2 or 3 invalid");const f=1&i?"03":"02",h=l.fromHex(f+d(u)),p=a(u),g=s(-c*p),m=s(r*p),v=l.BASE.multiplyAndAddUnsafe(h,g,m);if(!v)throw new Error("point at infinify");return v.assertValidity(),v}hasHighS(){return h(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,s(-this.s),this.recovery):this}toDERRawBytes(){return qy(this.toDERHex())}toDERHex(){return Sb.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return qy(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const m={isValidPrivateKey(e){try{return c(e),!0}catch(t){return!1}},normPrivateKeyToScalar:c,randomPrivateKey:()=>{const e=vb(t.n);return function(e,t,n=!1){const o=e.length,r=mb(t),i=vb(t);if(o<16||o<i||o>1024)throw new Error(`expected ${i}-1024 bytes of input, got ${o}`);const s=cb(n?Vy(e):Hy(e),t-ob)+ob;return n?Wy(s,r):zy(s,r)}(t.randomBytes(e),t.n)},precompute:(e=8,t=l.BASE)=>(t._setWindowSize(e),t.multiply(BigInt(3)),t)};function v(e){const t=Iy(e),n="string"==typeof e,o=(t||n)&&e.length;return t?o===r||o===i:n?o===2*r||o===2*i:e instanceof l}const y=t.bits2int||function(e){const n=Vy(e),o=8*e.length-t.nBitLength;return o>0?n>>BigInt(o):n},b=t.bits2int_modN||function(e){return s(y(e))},_=Ky(t.nBitLength);function w(e){if("bigint"!=typeof e)throw new Error("bigint expected");if(!(Tb<=e&&e<_))throw new Error(`bigint expected < 2^${t.nBitLength}`);return zy(e,t.nByteLength)}function x(e,o,r=S){if(["recovered","canonical"].some((e=>e in r)))throw new Error("sign() legacy options not supported");const{hash:i,randomBytes:u}=t;let{lowS:d,prehash:p,extraEntropy:m}=r;null==d&&(d=!0),e=Yy("msgHash",e),p&&(e=Yy("prehashed msgHash",i(e)));const v=b(e),_=c(o),x=[w(_),w(v)];if(null!=m&&!1!==m){const e=!0===m?u(n.BYTES):m;x.push(Yy("extraEntropy",e))}const T=Xy(...x),E=v;return{seed:T,k2sig:function(e){const t=y(e);if(!f(t))return;const n=a(t),o=l.BASE.multiply(t).toAffine(),r=s(o.x);if(r===Tb)return;const i=s(n*s(E+r*_));if(i===Tb)return;let c=(o.x===r?0:2)|Number(o.y&Eb),u=i;return d&&h(i)&&(u=function(e){return h(e)?s(-e):e}(i),c^=1),new g(r,u,c)}}}const S={lowS:t.lowS,prehash:!1},T={lowS:t.lowS,prehash:!1};return l.BASE._setWindowSize(8),{CURVE:t,getPublicKey:function(e,t=!0){return l.fromPrivateKey(e).toRawBytes(t)},getSharedSecret:function(e,t,n=!0){if(v(e))throw new Error("first arg must be private key");if(!v(t))throw new Error("second arg must be public key");return l.fromHex(t).multiply(c(e)).toRawBytes(n)},sign:function(e,n,o=S){const{seed:r,k2sig:i}=x(e,n,o),s=t;return Jy(s.hash.outputLen,s.nByteLength,s.hmac)(r,i)},verify:function(e,n,o,r=T){var i;const c=e;if(n=Yy("msgHash",n),o=Yy("publicKey",o),"strict"in r)throw new Error("options.strict was renamed to lowS");const{lowS:u,prehash:f}=r;let d,h;try{if("string"==typeof c||Iy(c))try{d=g.fromDER(c)}catch(S){if(!(S instanceof Sb.Err))throw S;d=g.fromCompact(c)}else{if("object"!=typeof c||"bigint"!=typeof c.r||"bigint"!=typeof c.s)throw new Error("PARSE");{const{r:e,s:t}=c;d=new g(e,t)}}h=l.fromHex(o)}catch(E){if("PARSE"===E.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(u&&d.hasHighS())return!1;f&&(n=t.hash(n));const{r:p,s:m}=d,v=b(n),y=a(m),_=s(v*y),w=s(p*y),x=null==(i=l.BASE.multiplyAndAddUnsafe(h,_,w))?void 0:i.toAffine();return!!x&&s(x.x)===p},ProjectivePoint:l,Signature:g,utils:m}}({a:BigInt("115792089210356248756420345214020892766250353991924191454421193933289684991996"),b:BigInt("18505919022281880113072981827955639221458448578012075254857346196103069175443"),Fp:f_,h:Mb,n:BigInt("115792089210356248756420345214020892766061623724957744567843809356293439045923"),Gx:BigInt("22963146547237050559479531362550074578802567295341616970375194840604139615431"),Gy:BigInt("85132369209828568825618990617112496413088388631904505083283536607588877201568"),hash:l_,hmac:(e,...t)=>u_(l_,e,Xy(...t)),randomBytes:function(e=0){const t=new Uint8Array(e);if(qb)return qb.getRandomValues(t);{const t=function(e){if(Hb.length>e){const t=Hb.slice(0,e);return Hb=Hb.slice(e),zb(),t}throw new Error("random number pool is not ready or insufficient, prevent getting too long random values or too often.")}(e);return t}}}),h_=gb(BigInt(d_.CURVE.n));function p_(e){const t=e?zy(cb(BigInt(e),Mb)+Mb,32):d_.utils.randomPrivateKey(),n=d_.getPublicKey(t,!1);return{privateKey:v_(Ry(t),64),publicKey:v_(Ry(n),64)}}function g_(e){if(130!==e.length)throw new Error("Invalid public key to compress");const t=(e.length-2)/2,n=e.substring(2,2+t);let o="03";return cb(Dy(e.substring(t+2,t+t+2)),Bb)===Pb&&(o="02"),o+n}function m_(e){const t=(e=decodeURIComponent(encodeURIComponent(e))).length,n=new Uint32Array(1+(t>>>2));for(let r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;const o=[];for(let r=0;r<t;r++){const e=n[r>>>2]>>>24-r%4*8&255;o.push((e>>>4).toString(16)),o.push((15&e).toString(16))}return o.join("")}function v_(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}function y_(e){return e.map((e=>{const t=e.toString(16);return 1===t.length?"0"+t:t})).join("")}function b_(e){const t=[];for(let n=0,o=e.length;n<o;n++)e[n]>=240&&e[n]<=247?(t.push(String.fromCodePoint(((7&e[n])<<18)+((63&e[n+1])<<12)+((63&e[n+2])<<6)+(63&e[n+3]))),n+=3):e[n]>=224&&e[n]<=239?(t.push(String.fromCodePoint(((15&e[n])<<12)+((63&e[n+1])<<6)+(63&e[n+2]))),n+=2):e[n]>=192&&e[n]<=223?(t.push(String.fromCodePoint(((31&e[n])<<6)+(63&e[n+1]))),n++):t.push(String.fromCodePoint(e[n]));return t.join("")}function __(e){let t=e.length;t%2!=0&&(e=v_(e,t+1)),t=e.length;const n=t/2,o=new Uint8Array(n);for(let r=0;r<n;r++)o[r]=parseInt(e.substring(2*r,2*r+2),16);return o}function w_(e){const t=d_.ProjectivePoint.fromHex(e);if(!t)return!1;try{return t.assertValidity(),!0}catch(n){return!1}}function x_(e,t){const n=d_.ProjectivePoint.fromHex(e);if(!n)return!1;const o=d_.ProjectivePoint.fromHex(t);return!!o&&n.equals(o)}var S_=Dy("80000000000000000000000000000000"),T_=Dy("7fffffffffffffffffffffffffffffff");function E_(e,t,n,o,r,i=!1,s="1234567812345678",a="1234567812345678"){const l=d_.ProjectivePoint.fromHex(t.publicKey),c=d_.ProjectivePoint.fromHex(o),u=d_.ProjectivePoint.fromHex(n);let f=B_(e.publicKey,s),d=B_(n,a);i&&([f,d]=[d,f]);const h=Dy(t.privateKey),p=Dy(e.privateKey),g=l.x,m=S_+(g&T_),v=h_.add(p,h_.mulN(m,h)),y=c.x,b=h_.add(S_,y&T_),_=c.multiply(b).add(u).multiply(v);return function(e,t){let n=new Uint8Array(t),o=1,r=0,i=k_;const s=new Uint8Array(4),a=()=>{s[0]=o>>24&255,s[1]=o>>16&255,s[2]=o>>8&255,s[3]=255&o,i=l_(Xy(e,s)),o++,r=0};a();for(let l=0,c=n.length;l<c;l++)r===i.length&&a(),n[l]=255&i[r++];return n}(Xy(__(v_(Ny(_.x),64)),__(v_(Ny(_.y),64)),f,d),r)}var C_=0,k_=new Uint8Array;function O_(e,t,n=1,o){const r="string"==typeof e?__(m_(e)):Uint8Array.from(e),i="string"==typeof t?d_.ProjectivePoint.fromHex(t):t,s=p_(),a=Dy(s.privateKey);let l=s.publicKey;l.length>128&&(l=l.substring(l.length-128));const c=i.multiply(a),u=__(v_(Ny(c.x),64)),f=__(v_(Ny(c.y),64)),d=Kb(l_(Xy(u,r,f)));A_(u,f,r);const h=Kb(r);if(null==o?void 0:o.asn1){const e=d_.ProjectivePoint.fromHex(s.publicKey);return n===C_?Ub(e.x,e.y,h,d):Ub(e.x,e.y,d,h)}return n===C_?l+h+d:l+d+h}function A_(e,t,n){let o=1,r=0,i=k_;const s=new Uint8Array(4),a=()=>{s[0]=o>>24&255,s[1]=o>>16&255,s[2]=o>>8&255,s[3]=255&o,i=l_(Xy(e,t,s)),o++,r=0};a();for(let l=0,c=n.length;l<c;l++)r===i.length&&a(),n[l]^=255&i[r++]}function $_(e,t,n=1,{output:o="string",asn1:r=!1}={}){const i=Dy(t);let s,a,l;if(r){const{x:t,y:o,cipher:r,hash:i}=function(e){function t(e,t){const n=Fb(e,t),o=Db(e,t),r=e.substring(n,n+2*o);return{value:r,nextStart:n+r.length}}const n=Fb(e,0),{value:o,nextStart:r}=t(e,n),{value:i,nextStart:s}=t(e,r),{value:a,nextStart:l}=t(e,s),{value:c}=t(e,l);return{x:Dy(o),y:Dy(i),hash:a,cipher:c}}(e);s=d_.ProjectivePoint.fromAffine({x:t,y:o}),l=i,a=r,n===C_&&([a,l]=[l,a])}else s=d_.ProjectivePoint.fromHex("04"+e.substring(0,128)),l=e.substring(128,192),a=e.substring(192),n===C_&&(l=e.substring(e.length-64),a=e.substring(128,e.length-64));const c=__(a),u=s.multiply(i),f=__(v_(Ny(u.x),64)),d=__(v_(Ny(u.y),64));A_(f,d,c);return y_(Array.from(l_(Xy(f,c,d))))===l.toLowerCase()?"array"===o?c:b_(c):"array"===o?[]:""}function P_(e,t,n={}){let{pointPool:o,der:r,hash:i,publicKey:s,userId:a}=n,l="string"==typeof e?m_(e):y_(Array.from(e));i&&(s=s||j_(t),l=I_(l,s,a));const c=Dy(t),u=Dy(l);let f=null,d=null,h=null;do{do{let e;e=o&&o.length?o.pop():R_(),f=e.k,d=h_.add(u,e.x1)}while(d===Pb||d+f===d_.CURVE.n);h=h_.mul(h_.inv(h_.addN(c,Mb)),h_.subN(f,h_.mulN(d,c)))}while(h===Pb);return r?function(e,t){const n=new Lb(e),o=new Lb(t);return new Rb([n,o]).getEncodedHex()}(d,h):v_(Ny(d),64)+v_(Ny(h),64)}function M_(e,t,n,o={}){let r;const{hash:i,der:s,userId:a}=o,l="string"==typeof n?n:n.toHex(!1);let c,u;if(r=i?I_("string"==typeof e?m_(e):e,l,a):"string"==typeof e?m_(e):y_(Array.from(e)),s){const e=function(e){const t=Fb(e,0),n=Fb(e,t),o=Db(e,t),r=e.substring(n,n+2*o),i=n+r.length,s=Fb(e,i),a=Db(e,i),l=e.substring(s,s+2*a);return{r:Dy(r),s:Dy(l)}}(t);c=e.r,u=e.s}else c=Dy(t.substring(0,64)),u=Dy(t.substring(64));const f="string"==typeof n?d_.ProjectivePoint.fromHex(n):n,d=Dy(r),h=h_.add(c,u);if(h===Pb)return!1;const p=d_.ProjectivePoint.BASE.multiply(u).add(f.multiply(h));return c===h_.add(d,p.x)}function B_(e,t="1234567812345678"){t=m_(t);const n=v_(Ny(d_.CURVE.a),64),o=v_(Ny(d_.CURVE.b),64),r=v_(Ny(d_.ProjectivePoint.BASE.x),64),i=v_(Ny(d_.ProjectivePoint.BASE.y),64);let s,a;if(128===e.length)s=e.substring(0,64),a=e.substring(64,128);else{const t=d_.ProjectivePoint.fromHex(e);s=v_(Ny(t.x),64),a=v_(Ny(t.y),64)}const l=__(t+n+o+r+i+s+a),c=4*t.length;return l_(Xy(new Uint8Array([c>>8&255,255&c]),l))}function I_(e,t,n="1234567812345678"){const o=B_(t,n);return Kb(l_(Xy(o,"string"==typeof e?__(e):e)))}function L_(e,t){const n=d_.ProjectivePoint.fromHex(e);return d_.utils.precompute(t,n)}function j_(e){return v_(Ry(d_.getPublicKey(e,!1)),64)}function R_(){const e=p_(),t=d_.ProjectivePoint.fromHex(e.publicKey),n=Dy(e.privateKey);return{...e,k:n,x1:t.x}}function N_(e){const t=[];for(let n=0,o=e.length;n<o;n++){const o=e.codePointAt(n);if(o<=127)t.push(o);else if(o<=2047)t.push(192|o>>>6),t.push(128|63&o);else if(o<=55295||o>=57344&&o<=65535)t.push(224|o>>>12),t.push(128|o>>>6&63),t.push(128|63&o);else{if(!(o>=65536&&o<=1114111))throw t.push(o),new Error("input is not supported");n++,t.push(240|o>>>18&28),t.push(128|o>>>12&63),t.push(128|o>>>6&63),t.push(128|63&o)}}return new Uint8Array(t)}var D_={};Ab(D_,{decrypt:()=>Z_,encrypt:()=>K_,sm4:()=>X_});var F_=0,U_=32,q_=16,V_=Uint8Array.from([214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72]),H_=new Uint32Array([462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257]);function z_(e){return(255&V_[e>>>24&255])<<24|(255&V_[e>>>16&255])<<16|(255&V_[e>>>8&255])<<8|255&V_[255&e]}function W_(e,t,n){let o=0,r=0,i=0,s=0,a=0,l=0,c=0,u=0;a=255&e[0],l=255&e[1],c=255&e[2],u=255&e[3],o=a<<24|l<<16|c<<8|u,a=255&e[4],l=255&e[5],c=255&e[6],u=255&e[7],r=a<<24|l<<16|c<<8|u,a=255&e[8],l=255&e[9],c=255&e[10],u=255&e[11],i=a<<24|l<<16|c<<8|u,a=255&e[12],l=255&e[13],c=255&e[14],u=255&e[15],s=a<<24|l<<16|c<<8|u;for(let f=0;f<32;f+=4)a=r^i^s^n[f],a=z_(a),o^=a^(a<<2|a>>>30)^(a<<10|a>>>22)^(a<<18|a>>>14)^(a<<24|a>>>8),l=i^s^o^n[f+1],l=z_(l),r^=l^(l<<2|l>>>30)^(l<<10|l>>>22)^(l<<18|l>>>14)^(l<<24|l>>>8),c=s^o^r^n[f+2],c=z_(c),i^=c^(c<<2|c>>>30)^(c<<10|c>>>22)^(c<<18|c>>>14)^(c<<24|c>>>8),u=o^r^i^n[f+3],u=z_(u),s^=u^(u<<2|u>>>30)^(u<<10|u>>>22)^(u<<18|u>>>14)^(u<<24|u>>>8);t[0]=s>>>24&255,t[1]=s>>>16&255,t[2]=s>>>8&255,t[3]=255&s,t[4]=i>>>24&255,t[5]=i>>>16&255,t[6]=i>>>8&255,t[7]=255&i,t[8]=r>>>24&255,t[9]=r>>>16&255,t[10]=r>>>8&255,t[11]=255&r,t[12]=o>>>24&255,t[13]=o>>>16&255,t[14]=o>>>8&255,t[15]=255&o}var Y_=new Uint8Array(16);function X_(e,t,n,o={}){let{padding:r="pkcs#7",mode:i,iv:s=new Uint8Array(16),output:a}=o;if("cbc"===i&&("string"==typeof s&&(s=__(s)),16!==s.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=__(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?n!==F_?N_(e):__(e):Uint8Array.from(e),("pkcs#5"===r||"pkcs#7"===r)&&n!==F_){const t=q_-e.length%q_,n=new Uint8Array(e.length+t);n.set(e,0);for(let o=0;o<t;o++)n[e.length+o]=t;e=n}const l=new Uint32Array(U_);!function(e,t,n){let o=0,r=0,i=0,s=0,a=0;o=(255&e[0])<<24|(255&e[1])<<16|(255&e[2])<<8|255&e[3],r=(255&e[4])<<24|(255&e[5])<<16|(255&e[6])<<8|255&e[7],i=(255&e[8])<<24|(255&e[9])<<16|(255&e[10])<<8|255&e[11],s=(255&e[12])<<24|(255&e[13])<<16|(255&e[14])<<8|255&e[15],o^=2746333894,r^=1453994832,i^=1736282519,s^=2993693404;for(let l=0;l<32;l+=4)a=r^i^s^H_[l+0],a=z_(a),o^=a^(a<<13|a>>>19)^(a<<23|a>>>9),t[l+0]=o,a=i^s^o^H_[l+1],a=z_(a),r^=a^(a<<13|a>>>19)^(a<<23|a>>>9),t[l+1]=r,a=s^o^r^H_[l+2],a=z_(a),i^=a^(a<<13|a>>>19)^(a<<23|a>>>9),t[l+2]=i,a=o^r^i^H_[l+3],a=z_(a),s^=a^(a<<13|a>>>19)^(a<<23|a>>>9),t[l+3]=s;if(n===F_)for(let l=0;l<16;l++)[t[l],t[31-l]]=[t[31-l],t[l]]}(t,l,n);let c=new Uint8Array(e.length),u=s,f=e.length,d=0;for(;f>=q_;){const t=e.subarray(d,d+16);if("cbc"===i)for(let e=0;e<q_;e++)n!==F_&&(t[e]^=u[e]);W_(t,Y_,l);for(let e=0;e<q_;e++)"cbc"===i&&n===F_&&(Y_[e]^=u[e]),c[d+e]=Y_[e];"cbc"===i&&(u=n!==F_?Y_:t),f-=q_,d+=q_}if(("pkcs#5"===r||"pkcs#7"===r)&&n===F_){const e=c.length,t=c[e-1];for(let n=1;n<=t;n++)if(c[e-n]!==t)throw new Error("padding is invalid");c=c.slice(0,e-t)}return"array"!==a?n!==F_?Kb(c):b_(c):c}function K_(e,t,n={}){return X_(e,t,1,n)}function Z_(e,t,n={}){return X_(e,t,0,n)}
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const G_="function"==typeof Buffer,J_=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder?new TextEncoder:void 0),Q_=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),ew=(e=>{let t={};return Q_.forEach(((e,n)=>t[e]=n)),t})(),tw=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,nw=String.fromCharCode.bind(String),ow="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),rw=e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_")),iw=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),sw=e=>{let t,n,o,r,i="";const s=e.length%3;for(let a=0;a<e.length;){if((n=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=n<<16|o<<8|r,i+=Q_[t>>18&63]+Q_[t>>12&63]+Q_[t>>6&63]+Q_[63&t]}return s?i.slice(0,s-3)+"===".substring(s):i},aw="function"==typeof btoa?e=>btoa(e):G_?e=>Buffer.from(e,"binary").toString("base64"):sw,lw=G_?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let n=0,o=e.length;n<o;n+=4096)t.push(nw.apply(null,e.subarray(n,n+4096)));return aw(t.join(""))},cw=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?nw(192|t>>>6)+nw(128|63&t):nw(224|t>>>12&15)+nw(128|t>>>6&63)+nw(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return nw(240|t>>>18&7)+nw(128|t>>>12&63)+nw(128|t>>>6&63)+nw(128|63&t)},uw=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,fw=e=>e.replace(uw,cw),dw=G_?e=>Buffer.from(e,"utf8").toString("base64"):J_?e=>lw(J_.encode(e)):e=>aw(fw(e)),hw=(e,t=!1)=>t?rw(dw(e)):dw(e),pw=e=>{if(e=e.replace(/\s+/g,""),!tw.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,n,o,r="";for(let i=0;i<e.length;)t=ew[e.charAt(i++)]<<18|ew[e.charAt(i++)]<<12|(n=ew[e.charAt(i++)])<<6|(o=ew[e.charAt(i++)]),r+=64===n?nw(t>>16&255):64===o?nw(t>>16&255,t>>8&255):nw(t>>16&255,t>>8&255,255&t);return r},gw="function"==typeof atob?e=>atob(iw(e)):G_?e=>Buffer.from(e,"base64").toString("binary"):pw,mw=G_?e=>ow(Buffer.from(e,"base64")):e=>ow(gw(e).split("").map((e=>e.charCodeAt(0)))),vw=e=>mw(yw(e)),yw=e=>iw(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),bw=hw,_w=vw;class ww{static stringToArrayBufferInUtf8(e){return(new("undefined"==typeof window?require("util").TextEncoder:window.TextEncoder)).encode(e)}static utf8ArrayBufferToString(e){return new("undefined"==typeof window?require("util").TextDecoder:window.TextDecoder)("utf-8").decode(e)}static arrayBufferToBase64(e){return bw(e)}static base64ToArrayBuffer(e){return _w(e)}}const xw=16,Sw=Uint8Array.from([214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72]),Tw=Uint32Array.from([462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257]),Ew=Uint32Array.from([2746333894,1453994832,1736282519,2993693404]);class Cw{constructor(e){let t=ww.stringToArrayBufferInUtf8(e.key);if(16!==t.length)throw new Error("key should be a 16 bytes string");this.key=t;let n=new Uint8Array(0);if(void 0!==e.iv&&null!==e.iv&&(n=ww.stringToArrayBufferInUtf8(e.iv),16!==n.length))throw new Error("iv should be a 16 bytes string");this.iv=n,this.mode="cbc",["cbc","ecb"].indexOf(e.mode)>=0&&(this.mode=e.mode),this.cipherType="base64",["base64","text"].indexOf(e.outType)>=0&&(this.cipherType=e.outType),this.encryptRoundKeys=new Uint32Array(32),this.spawnEncryptRoundKeys(),this.decryptRoundKeys=Uint32Array.from(this.encryptRoundKeys),this.decryptRoundKeys.reverse()}doBlockCrypt(e,t){let n=new Uint32Array(36);n.set(e,0);for(let r=0;r<32;r++)n[r+4]=n[r]^this.tTransform1(n[r+1]^n[r+2]^n[r+3]^t[r]);let o=new Uint32Array(4);return o[0]=n[35],o[1]=n[34],o[2]=n[33],o[3]=n[32],o}spawnEncryptRoundKeys(){let e=new Uint32Array(4);e[0]=this.key[0]<<24|this.key[1]<<16|this.key[2]<<8|this.key[3],e[1]=this.key[4]<<24|this.key[5]<<16|this.key[6]<<8|this.key[7],e[2]=this.key[8]<<24|this.key[9]<<16|this.key[10]<<8|this.key[11],e[3]=this.key[12]<<24|this.key[13]<<16|this.key[14]<<8|this.key[15];let t=new Uint32Array(36);t[0]=e[0]^Ew[0],t[1]=e[1]^Ew[1],t[2]=e[2]^Ew[2],t[3]=e[3]^Ew[3];for(let n=0;n<32;n++)t[n+4]=t[n]^this.tTransform2(t[n+1]^t[n+2]^t[n+3]^Tw[n]),this.encryptRoundKeys[n]=t[n+4]}rotateLeft(e,t){return e<<t|e>>>32-t}linearTransform1(e){return e^this.rotateLeft(e,2)^this.rotateLeft(e,10)^this.rotateLeft(e,18)^this.rotateLeft(e,24)}linearTransform2(e){return e^this.rotateLeft(e,13)^this.rotateLeft(e,23)}tauTransform(e){return Sw[e>>>24&255]<<24|Sw[e>>>16&255]<<16|Sw[e>>>8&255]<<8|Sw[255&e]}tTransform1(e){let t=this.tauTransform(e);return this.linearTransform1(t)}tTransform2(e){let t=this.tauTransform(e);return this.linearTransform2(t)}padding(e){if(null===e)return null;let t=xw-e.length%xw,n=new Uint8Array(e.length+t);return n.set(e,0),n.fill(t,e.length),n}dePadding(e){if(null===e)return null;let t=e[e.length-1];return e.slice(0,e.length-t)}uint8ToUint32Block(e,t=0){let n=new Uint32Array(4);return n[0]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],n[1]=e[t+4]<<24|e[t+5]<<16|e[t+6]<<8|e[t+7],n[2]=e[t+8]<<24|e[t+9]<<16|e[t+10]<<8|e[t+11],n[3]=e[t+12]<<24|e[t+13]<<16|e[t+14]<<8|e[t+15],n}encrypt(e){let t=ww.stringToArrayBufferInUtf8(e),n=this.padding(t),o=n.length/xw,r=new Uint8Array(n.length);if("cbc"===this.mode){if(null===this.iv||16!==this.iv.length)throw new Error("iv error");let e=this.uint8ToUint32Block(this.iv);for(let t=0;t<o;t++){let o=t*xw,i=this.uint8ToUint32Block(n,o);e[0]=e[0]^i[0],e[1]=e[1]^i[1],e[2]=e[2]^i[2],e[3]=e[3]^i[3];let s=this.doBlockCrypt(e,this.encryptRoundKeys);e=s;for(let e=0;e<xw;e++)r[o+e]=s[parseInt(e/4)]>>(3-e)%4*8&255}}else for(let i=0;i<o;i++){let e=i*xw,t=this.uint8ToUint32Block(n,e),o=this.doBlockCrypt(t,this.encryptRoundKeys);for(let n=0;n<xw;n++)r[e+n]=o[parseInt(n/4)]>>(3-n)%4*8&255}return"base64"===this.cipherType?ww.arrayBufferToBase64(r):ww.utf8ArrayBufferToString(r)}decrypt(e){let t=new Uint8Array;t="base64"===this.cipherType?ww.base64ToArrayBuffer(e):ww.stringToArrayBufferInUtf8(e);let n=t.length/xw,o=new Uint8Array(t.length);if("cbc"===this.mode){if(null===this.iv||16!==this.iv.length)throw new Error("iv error");let e=this.uint8ToUint32Block(this.iv);for(let r=0;r<n;r++){let n=r*xw,i=this.uint8ToUint32Block(t,n),s=this.doBlockCrypt(i,this.decryptRoundKeys),a=new Uint32Array(4);a[0]=e[0]^s[0],a[1]=e[1]^s[1],a[2]=e[2]^s[2],a[3]=e[3]^s[3],e=i;for(let e=0;e<xw;e++)o[n+e]=a[parseInt(e/4)]>>(3-e)%4*8&255}}else for(let i=0;i<n;i++){let e=i*xw,n=this.uint8ToUint32Block(t,e),r=this.doBlockCrypt(n,this.decryptRoundKeys);for(let t=0;t<xw;t++)o[e+t]=r[parseInt(t/4)]>>(3-t)%4*8&255}let r=this.dePadding(o);return ww.utf8ArrayBufferToString(r)}}const kw=e=>{return t="string"==typeof(t=e)?N_(t):t,Kb(l_(t));var t},Ow=e=>{const t=Cy(),n=ky(),o=function(e){for(var t="",n=0;n<e.length;n++)""==t?t=e.charCodeAt(n).toString(16):t+=e.charCodeAt(n).toString(16);return t}(kw(t+n).substring(2,18));return function(e){let t="";const n=new Uint8Array(e),o=n.byteLength;for(let r=0;r<o;r++)t+=String.fromCharCode(n[r]);return window.btoa(t)}(D_.encrypt(JSON.stringify(e),o,{output:"array"}))},Aw=e=>{const t=Cy(),n=ky(),o=kw(t+n).substring(2,18);return new Cw({key:o,mode:"ecb",cipherType:"base64"}).decrypt(e,o)},$w=e=>$b.doSignature(kw(JSON.stringify(e)),Ay(wy),{hash:!0,der:!0}),Pw=(e,t)=>{try{return $b.doVerifySignature(kw(e),t,Ay(xy),{hash:!0,der:!0})}catch(n){}};function Mw(e,t){return"string"==typeof e?t:e}const Bw=e=>(t,n=ts())=>{!ls&&_r(e,t,n)},Iw=Bw(fe),Lw=Bw(de),jw=Bw(he),Rw=Bw(ge),Nw=Bw(ye);
/*!
  * pinia v2.0.27
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */
let Dw;const Fw=e=>Dw=e,Uw=Symbol();function qw(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Vw,Hw;(Hw=Vw||(Vw={})).direct="direct",Hw.patchObject="patch object",Hw.patchFunction="patch function";const zw=()=>{};function Ww(e,t,n,o=zw){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};var i;return!n&&_t()&&(i=r,mt&&mt.cleanups.push(i)),r}function Yw(e,...t){e.slice().forEach((e=>{e(...t)}))}function Xw(e,t){e instanceof Map&&t instanceof Map&&t.forEach(((t,n)=>e.set(n,t))),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];qw(r)&&qw(o)&&e.hasOwnProperty(n)&&!Dn(o)&&!On(o)?e[n]=Xw(r,o):e[n]=o}return e}const{assign:Kw}=Object;function Zw(e){return!(!Dn(e)||!e.effect)}function Gw(e,t,n,o){const{state:r,actions:i,getters:s}=t,a=n.state.value[e];let l;return l=function(e,t,n={},o,r,i){let s;const a=Kw({actions:{}},n),l={deep:!0};let c,u,f,d=Bn([]),h=Bn([]);const p=o.state.value[e];let g;function m(t){let n;c=u=!1,"function"==typeof t?(t(o.state.value[e]),n={type:Vw.patchFunction,storeId:e,events:f}):(Xw(o.state.value[e],t),n={type:Vw.patchObject,payload:t,storeId:e,events:f});const r=g=Symbol();ao().then((()=>{g===r&&(c=!0)})),u=!0,Yw(d,n,o.state.value[e])}Fn({});const v=zw;function y(){s.stop(),d=[],h=[],o._s.delete(e)}function b(t,n){return function(){Fw(o);const r=Array.from(arguments),i=[],s=[];function a(e){i.push(e)}function l(e){s.push(e)}let c;Yw(h,{args:r,name:t,store:w,after:a,onError:l});try{c=n.apply(this&&this.$id===e?this:w,r)}catch(u){throw Yw(s,u),u}return c instanceof Promise?c.then((e=>(Yw(i,e),e))).catch((e=>(Yw(s,e),Promise.reject(e)))):(Yw(i,c),c)}}const _={_p:o,$id:e,$onAction:Ww.bind(null,h),$patch:m,$reset:v,$subscribe(t,n={}){const r=Ww(d,t,n.detached,(()=>i())),i=s.run((()=>Do((()=>o.state.value[e]),(o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:Vw.direct,events:f},o)}),Kw({},l,n))));return r},$dispose:y},w=Tn(_);o._s.set(e,w);const x=o._e.run((()=>(s=bt(),s.run((()=>t())))));for(const S in x){const e=x[S];if(Dn(e)&&!Zw(e)||On(e));else if("function"==typeof e){const t=b(S,e);x[S]=t,a.actions[S]=e}}Kw(w,x),Kw(Mn(w),x),Object.defineProperty(w,"$state",{get:()=>o.state.value[e],set:e=>{m((t=>{Kw(t,e)}))}}),o._p.forEach((e=>{Kw(w,s.run((()=>e({store:w,app:o._a,pinia:o,options:a}))))})),p&&i&&n.hydrate&&n.hydrate(w.$state,p);return c=!0,u=!0,w}(e,(function(){a||(n.state.value[e]=r?r():{});const t=function(e){const t=g(e)?new Array(e.length):{};for(const n in e)t[n]=Xn(e,n);return t}(n.state.value[e]);return Kw(t,i,Object.keys(s||{}).reduce(((t,o)=>(t[o]=Bn(hs((()=>{Fw(n);const t=n._s.get(e);return s[o].call(t,t)}))),t)),{}))}),t,n,0,!0),l.$reset=function(){const e=r?r():{};this.$patch((t=>{Kw(t,e)}))},l}function Jw(e,t,n){let o,r;function i(e,t){const n=ts();(e=e||n&&oi(Uw))&&Fw(e),(e=Dw)._s.has(o)||Gw(o,r,e);return e._s.get(o)}return"string"==typeof e?(o=e,r=t):(r=e,o=e.id),i.$id=o,i}const Qw=function(){const e=bt(!0),t=e.run((()=>Fn({})));let n=[],o=[];const r=Bn({install(e){Fw(r),r._a=e,e.provide(Uw,r),e.config.globalProperties.$pinia=r,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}();function ex(e){return gy({url:"/api/consult/answer",data:e})}function tx(e){return py({url:"/api/medicine/answer",data:e})}function nx(e){return gy({url:"/api/consult/question",data:e})}function ox(e){return gy({url:"/api/feedbacks/feedback",data:e})}function rx(e,t){return n={url:`/api/feedbacks/feedback/${e}`,data:t},vy({...n,method:"PUT"},o);var n,o}function ix(e){return t={url:`/api/feedbacks/feedback/${e}`},vy({...t,method:"DELETE"},n);var t,n}function sx(){return py({url:"/api/consult/record-id"})}function ax(e){return gy({url:"/api/consult/multi-turn-dialogue",data:e})}const lx=Jw({id:"app-feedback",state:()=>({feedbackOPS:[]}),getters:{getFeedback:e=>e.feedbackOPS},actions:{setFeedback(){py({url:"/api/feedbacks/option"}).then((e=>{this.feedbackOPS=e}))}}});function cx(){return lx(Qw)}const ux=or({__name:"App",setup(e){const t=cx();return jw((async e=>{try{const{token:t,openId:n}=null==e?void 0:e.query;t&&n&&($y(by,t),$y(_y,n))}catch(t){}})),Iw((()=>{t.setFeedback()})),Lw((()=>{})),Rw((()=>{})),()=>{}}});Hg(ux,{init:qg,setup(e){const t=ng(),n=()=>{var n;n=e,Object.keys(Ld).forEach((e=>{Ld[e].forEach((t=>{_r(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i,onError:s}=e,a=function({path:e,query:t}){return f(_h,{path:e,query:t}),f(wh,_h),f({},_h)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:nt(t.query)});if(o&&R(o,a),r&&R(r,a),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&R(i,e)}s&&(e.appContext.config.errorHandler=e=>{R(s,e)})};return oi(Ml).isReady().then(n),Sr((()=>{window.addEventListener("resize",it(Wg,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Yg),document.addEventListener("visibilitychange",Xg),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Vv.emit(ge,{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Pi(),ji(Uv));e.setup=(e,o)=>{const r=t&&t(e,o);return y(r)?n:r},e.render=n}});var fx=Object.prototype.toString;function dx(e){return"[object Array]"===fx.call(e)}function hx(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),dx(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.call(null,e[r],r,e)}function px(){let e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=px(e[n],t):e[n]="object"==typeof t?px({},t):t}for(let n=0,o=arguments.length;n<o;n++)hx(arguments[n],t);return e}function gx(e){return void 0===e}function mx(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function vx(e,t,n){if(!t)return e;var o,r;if(n)o=n(t);else if(r=t,"undefined"!=typeof URLSearchParams&&r instanceof URLSearchParams)o=t.toString();else{var i=[];hx(t,(function(e,t){null!=e&&(dx(e)?t+="[]":e=[e],hx(e,(function(e){!function(e){return"[object Date]"===fx.call(e)}(e)?function(e){return null!==e&&"object"==typeof e}(e)&&(e=JSON.stringify(e)):e=e.toISOString(),i.push(mx(t)+"="+mx(e))})))})),o=i.join("&")}if(o){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const yx=(e,t)=>{let n={};return e.forEach((e=>{gx(t[e])||(n[e]=t[e])})),n},bx=e=>(e=>new Promise(((t,n)=>{let o=vx((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(r,i):i),e.params,e.paramsSerializer);var r,i;const s={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e,r.rawData=r.data;try{let t=!1;const n=typeof e.forcedJSONParsing;"boolean"===n?t=e.forcedJSONParsing:"object"===n&&(t=(e.forcedJSONParsing.include||[]).includes(e.method)),t&&"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(i){}!function(e,t,n){const o=n.config.validateStatus,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let a;if("UPLOAD"===e.method){delete s.header["content-type"],delete s.header["Content-Type"];let t={filePath:e.filePath,name:e.name};const n=["files","file","timeout","formData"];a=Ym({...s,...t,...yx(n,e)})}else if("DOWNLOAD"===e.method){const t=["timeout"];a=zm({...s,...yx(t,e)})}else{const t=["data","method","timeout","dataType","responseType","withCredentials"];a=Um({...s,...yx(t,e)})}e.getTask&&e.getTask(a,e)})))(e);function _x(){this.handlers=[]}_x.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},_x.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},_x.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const xx=(e,t,n)=>{let o={};return e.forEach((e=>{gx(n[e])?gx(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},Sx={baseURL:"",header:{},method:"GET",dataType:"json",paramsSerializer:null,responseType:"text",custom:{},timeout:6e4,withCredentials:!1,validateStatus:function(e){return e>=200&&e<300},forcedJSONParsing:!0};var Tx=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(a){t=function(){}}try{n=Set}catch(a){n=function(){}}try{o=Promise}catch(a){o=function(){}}function r(i,a,l,c,u){"object"==typeof a&&(l=a.depth,c=a.prototype,u=a.includeNonEnumerable,a=a.circular);var f=[],d=[],h="undefined"!=typeof Buffer;return void 0===a&&(a=!0),void 0===l&&(l=1/0),function i(l,p){if(null===l)return null;if(0===p)return l;var g,m;if("object"!=typeof l)return l;if(e(l,t))g=new t;else if(e(l,n))g=new n;else if(e(l,o))g=new o((function(e,t){l.then((function(t){e(i(t,p-1))}),(function(e){t(i(e,p-1))}))}));else if(r.__isArray(l))g=[];else if(r.__isRegExp(l))g=new RegExp(l.source,s(l)),l.lastIndex&&(g.lastIndex=l.lastIndex);else if(r.__isDate(l))g=new Date(l.getTime());else{if(h&&Buffer.isBuffer(l))return Buffer.from?g=Buffer.from(l):(g=new Buffer(l.length),l.copy(g)),g;e(l,Error)?g=Object.create(l):void 0===c?(m=Object.getPrototypeOf(l),g=Object.create(m)):(g=Object.create(c),m=c)}if(a){var v=f.indexOf(l);if(-1!=v)return d[v];f.push(l),d.push(g)}for(var y in e(l,t)&&l.forEach((function(e,t){var n=i(t,p-1),o=i(e,p-1);g.set(n,o)})),e(l,n)&&l.forEach((function(e){var t=i(e,p-1);g.add(t)})),l){Object.getOwnPropertyDescriptor(l,y)&&(g[y]=i(l[y],p-1));try{if("undefined"===Object.getOwnPropertyDescriptor(l,y).set)continue;g[y]=i(l[y],p-1)}catch(T){if(T instanceof TypeError)continue;if(T instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(l);for(y=0;y<b.length;y++){var _=b[y];(!(x=Object.getOwnPropertyDescriptor(l,_))||x.enumerable||u)&&(g[_]=i(l[_],p-1),Object.defineProperty(g,_,x))}}if(u){var w=Object.getOwnPropertyNames(l);for(y=0;y<w.length;y++){var x,S=w[y];(x=Object.getOwnPropertyDescriptor(l,S))&&x.enumerable||(g[S]=i(l[S],p-1),Object.defineProperty(g,S,x))}}return g}(i,l)}function i(e){return Object.prototype.toString.call(e)}function s(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=s,r}();function Ex(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function Cx(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function kx(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function Ox(e){return"[object Object]"===Object.prototype.toString.call(e)}function Ax(e){return"function"==typeof e}function $x(e){return Ox(e)&&Ax(e.then)&&Ax(e.catch)}function Px(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)}function Mx(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}const Bx=Object.freeze(Object.defineProperty({__proto__:null,amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},array:kx,carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},contains:function(e,t){return e.indexOf(t)>=0},date:function(e){return!!e&&(Ex(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},digits:function(e){return/^\d+$/.test(e)},email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},empty:Cx,enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},func:Ax,idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},image:Px,jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},mobile:function(e){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(e)},number:Ex,object:Ox,promise:$x,range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},video:Mx},Symbol.toStringTag,{value:"Module"}));function Ix(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function Lx(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function jx(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=Lx(e);return t>0?Ix(Number(e)*Math.pow(10,t)):Number(e)}function Rx(e){e>Number.MAX_SAFE_INTEGER||Number.MIN_SAFE_INTEGER}function Nx(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function Dx(...e){if(e.length>2)return Nx(e,Dx);const[t,n]=e,o=jx(t),r=jx(n),i=Lx(t)+Lx(n),s=o*r;return Rx(s),s/Math.pow(10,i)}function Fx(...e){if(e.length>2)return Nx(e,Fx);const[t,n]=e,o=jx(t),r=jx(n);return Rx(o),Rx(r),Dx(o/r,Ix(Math.pow(10,Lx(n)-Lx(t))))}function Ux(e=void 0){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function qx(e,t=new WeakMap){if(null===e||"object"!=typeof e)return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime());else if(e instanceof RegExp)n=new RegExp(e);else if(e instanceof Map)n=new Map(Array.from(e,(([e,n])=>[e,qx(n,t)])));else if(e instanceof Set)n=new Set(Array.from(e,(e=>qx(e,t))));else if(Array.isArray(e))n=e.map((e=>qx(e,t)));else if("[object Object]"===Object.prototype.toString.call(e)){n=Object.create(Object.getPrototypeOf(e)),t.set(e,n);for(const[o,r]of Object.entries(e))n[o]=qx(r,t)}else n=Object.assign({},e);return t.set(e,n),n}function Vx(e={},t={}){if("object"!=typeof(e=qx(e))||null===e||"object"!=typeof t||null===t)return e;const n=Array.isArray(e)?e.slice():Object.assign({},e);for(const o in t){if(!t.hasOwnProperty(o))continue;const e=t[o],r=n[o];e instanceof Date?n[o]=new Date(e):e instanceof RegExp?n[o]=new RegExp(e):e instanceof Map?n[o]=new Map(e):e instanceof Set?n[o]=new Set(e):n[o]="object"==typeof e&&null!==e?Vx(r,e):e}return n}function Hx(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(null==e?void 0:e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):"string"==typeof e&&e.includes("-")&&!e.includes("T")?new Date(e.replace(/-/g,"/")):new Date(e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function zx(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function Wx(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function Yx(){var e;const t=mg(),n=null==(e=t[t.length-1])?void 0:e.route;return`/${n||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const Xx=Object.freeze(Object.defineProperty({__proto__:null,$parent:Ux,addStyle:function(e,t="object"){if(Cx(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=zx(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[zx(o[0])]=zx(o[1])}return n}let n="";for(const o in e){n+=`${o.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[o]};`}return zx(n)},addUnit:function(e="auto",t=((e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})()?(e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})():"px")){return Ex(e=String(e))?`${e}${t}`:e},deepClone:qx,deepMerge:Vx,error:function(e){},formValidate:function(e,t){const n=Ux.call(e,"uv-form-item"),o=Ux.call(e,"uv-form");n&&o&&o.validateField(n.prop,(()=>{}),t)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},getHistoryPage:function(e=0){const t=mg();return t[t.length-1+e]},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}},getPx:function(e,t=!1){return Ex(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${fd(parseInt(e))}px`:Number(fd(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")},os:function(){return hm().platform.toLowerCase()},padZero:function(e){return`00${e}`.slice(-2)},page:Yx,pages:function(){return mg()},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,s=void 0===o?",":o,a=void 0===n?".":n;let l="";l=(i?function(e,t){const n=Math.pow(10,t);let o=Fx(Math.round(Math.abs(Dx(e,n))),n);return e<0&&0!==o&&(o=Dx(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(l[0]);)l[0]=l[0].replace(c,`$1${s}$2`);return(l[1]||"").length<i&&(l[1]=l[1]||"",l[1]+=new Array(i-l[1].length+1).join("0")),l.join(a)},queryParams:Wx,random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},setConfig:function({props:e={},config:t={},color:n={},zIndex:o={}}){const{deepMerge:r}=uni.$uv;uni.$uv.config=r(uni.$uv.config,t),uni.$uv.props=r(uni.$uv.props,e),uni.$uv.color=r(uni.$uv.color,n),uni.$uv.zIndex=r(uni.$uv.zIndex,o)},setProperty:function(e,t,n){if(!e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n},sleep:function(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))},sys:function(){return hm()},timeFormat:Hx,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:Hx(e,t)}return o},toast:function(e,t=2e3){wv({title:String(e),icon:"none",duration:t})},trim:zx,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n}},Symbol.toStringTag,{value:"Module"}));const Kx=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=Wx(t,!1),e+`&${n}`):(n=Wx(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=Vx(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==Yx())if(t.intercept&&(n.intercept=t.intercept),n.params=t,n=Vx(this.config,n),"function"==typeof n.intercept){await new Promise(((e,t)=>{n.intercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i,events:s}=e;"navigateTo"!=e.type&&"to"!=e.type||Zm({url:t,animationType:r,animationDuration:i,events:s}),"redirectTo"!=e.type&&"redirect"!=e.type||Gm({url:t}),"switchTab"!=e.type&&"tab"!=e.type||ev({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||Jm({url:t}),"navigateBack"!=e.type&&"back"!=e.type||Xm({delta:o})}}).route;let Zx,Gx=null;function Jx(e,t=500,n=!1){if(null!==Gx&&clearTimeout(Gx),n){const n=!Gx;Gx=setTimeout((()=>{Gx=null}),t),n&&"function"==typeof e&&e()}else Gx=setTimeout((()=>{"function"==typeof e&&e()}),t)}function Qx(e,t=500,n=!0){n?Zx||(Zx=!0,"function"==typeof e&&e(),setTimeout((()=>{Zx=!1}),t)):Zx||(Zx=!0,setTimeout((()=>{Zx=!1,"function"==typeof e&&e()}),t))}const eS={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var e,t;return{...Xx,test:Bx,route:Kx,debounce:Jx,throttle:Qx,unit:null==(t=null==(e=null==uni?void 0:uni.$uv)?void 0:e.config)?void 0:t.unit}},bem:()=>function(e,t,n){const o=`uv-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&uni[this.linkType]({url:t})},$uvGetRect(e,t){return new Promise((n=>{Od().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){hd("uvOnReachBottom")},beforeDestroy(){if(this.parent&&kx(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}},unmounted(){if(this.parent&&kx(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},tS={};function nS(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=oS(e,!1),r=o[0],i=o[1],s=o[2],a=oS(t,!1),l=(a[0]-r)/n,c=(a[1]-i)/n,u=(a[2]-s)/n,f=[];for(let d=0;d<n;d++){let o=rS(`rgb(${Math.round(l*d+r)},${Math.round(c*d+i)},${Math.round(u*d+s)})`);0===d&&(o=rS(e)),d===n-1&&(o=rS(t)),f.push(o)}return f}function oS(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function rS(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}const iS="1.1.20";let sS="none";sS="vue3",sS="h5";const aS={route:Kx,config:{v:iS,version:iS,type:["primary","success","info","error","warning"],color:{"uv-primary":"#2979ff","uv-warning":"#ff9900","uv-success":"#19be6b","uv-error":"#fa3534","uv-info":"#909399","uv-main-color":"#303133","uv-content-color":"#606266","uv-tips-color":"#909399","uv-light-color":"#c0c4cc"},unit:"px"},test:Bx,date:Hx,...Xx,colorGradient:nS,hexToRgb:oS,rgbToHex:rS,colorToRgba:function(e,t){e=rS(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n},http:new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={}),this.config=Tx({...Sx,...e}),this.interceptors={request:new _x,response:new _x}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:t.baseURL||e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:px(e.header||{},t.header||{})};if(o={...o,...xx(["getTask","validateStatus","paramsSerializer","forcedJSONParsing"],e,t)},"DOWNLOAD"===n){const n=["timeout"];o={...o,...xx(n,e,t)}}else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["files","file","filePath","name","timeout","formData"].forEach((e=>{gx(t[e])||(o[e]=t[e])})),gx(o.timeout)&&!gx(e.timeout)&&(o.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","withCredentials"];o={...o,...xx(n,e,t)}}return o})(this.config,e);let t=[bx,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}get version(){return"3.1.0"}},debounce:Jx,throttle:Qx,platform:"h5",mixin:eS,mpMixin:tS};uni.$uv=aS;const lS={install:(e,t={})=>{var n,o;const r=qx(eS);null==(n=null==r?void 0:r.props)||delete n.customClass,null==(o=null==r?void 0:r.props)||delete o.customStyle,e.mixin(r),e.config.globalProperties.$uv=aS}};var cS="object"==typeof global&&global&&global.Object===Object&&global,uS="object"==typeof self&&self&&self.Object===Object&&self,fS=cS||uS||Function("return this")(),dS=fS.Symbol,hS=Object.prototype,pS=hS.hasOwnProperty,gS=hS.toString,mS=dS?dS.toStringTag:void 0;var vS=Object.prototype.toString;var yS="[object Null]",bS="[object Undefined]",_S=dS?dS.toStringTag:void 0;function wS(e){return null==e?void 0===e?bS:yS:_S&&_S in Object(e)?function(e){var t=pS.call(e,mS),n=e[mS];try{e[mS]=void 0;var o=!0}catch(i){}var r=gS.call(e);return o&&(t?e[mS]=n:delete e[mS]),r}(e):function(e){return vS.call(e)}(e)}function xS(e){return null!=e&&"object"==typeof e}var SS=Array.isArray;function TS(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}var ES="[object AsyncFunction]",CS="[object Function]",kS="[object GeneratorFunction]",OS="[object Proxy]";function AS(e){if(!TS(e))return!1;var t=wS(e);return t==CS||t==kS||t==ES||t==OS}var $S,PS=fS["__core-js_shared__"],MS=($S=/[^.]+$/.exec(PS&&PS.keys&&PS.keys.IE_PROTO||""))?"Symbol(src)_1."+$S:"";var BS=Function.prototype.toString;function IS(e){if(null!=e){try{return BS.call(e)}catch(t){}try{return e+""}catch(t){}}return""}var LS=/^\[object .+?Constructor\]$/,jS=Function.prototype,RS=Object.prototype,NS=jS.toString,DS=RS.hasOwnProperty,FS=RegExp("^"+NS.call(DS).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function US(e){return!(!TS(e)||(t=e,MS&&MS in t))&&(AS(e)?FS:LS).test(IS(e));var t}function qS(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return US(n)?n:void 0}var VS=qS(fS,"WeakMap"),HS=9007199254740991;function zS(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=HS}var WS=Object.prototype;function YS(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||WS)}function XS(e){return xS(e)&&"[object Arguments]"==wS(e)}var KS=Object.prototype,ZS=KS.hasOwnProperty,GS=KS.propertyIsEnumerable,JS=XS(function(){return arguments}())?XS:function(e){return xS(e)&&ZS.call(e,"callee")&&!GS.call(e,"callee")};var QS="object"==typeof exports&&exports&&!exports.nodeType&&exports,eT=QS&&"object"==typeof module&&module&&!module.nodeType&&module,tT=eT&&eT.exports===QS?fS.Buffer:void 0,nT=(tT?tT.isBuffer:void 0)||function(){return!1},oT={};oT["[object Float32Array]"]=oT["[object Float64Array]"]=oT["[object Int8Array]"]=oT["[object Int16Array]"]=oT["[object Int32Array]"]=oT["[object Uint8Array]"]=oT["[object Uint8ClampedArray]"]=oT["[object Uint16Array]"]=oT["[object Uint32Array]"]=!0,oT["[object Arguments]"]=oT["[object Array]"]=oT["[object ArrayBuffer]"]=oT["[object Boolean]"]=oT["[object DataView]"]=oT["[object Date]"]=oT["[object Error]"]=oT["[object Function]"]=oT["[object Map]"]=oT["[object Number]"]=oT["[object Object]"]=oT["[object RegExp]"]=oT["[object Set]"]=oT["[object String]"]=oT["[object WeakMap]"]=!1;var rT,iT="object"==typeof exports&&exports&&!exports.nodeType&&exports,sT=iT&&"object"==typeof module&&module&&!module.nodeType&&module,aT=sT&&sT.exports===iT&&cS.process,lT=function(){try{var e=sT&&sT.require&&sT.require("util").types;return e||aT&&aT.binding&&aT.binding("util")}catch(t){}}(),cT=lT&&lT.isTypedArray,uT=cT?(rT=cT,function(e){return rT(e)}):function(e){return xS(e)&&zS(e.length)&&!!oT[wS(e)]};var fT=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object),dT=Object.prototype.hasOwnProperty;var hT=qS(fS,"Map"),pT=qS(fS,"DataView"),gT=qS(fS,"Promise"),mT=qS(fS,"Set"),vT="[object Map]",yT="[object Promise]",bT="[object Set]",_T="[object WeakMap]",wT="[object DataView]",xT=IS(pT),ST=IS(hT),TT=IS(gT),ET=IS(mT),CT=IS(VS),kT=wS;(pT&&kT(new pT(new ArrayBuffer(1)))!=wT||hT&&kT(new hT)!=vT||gT&&kT(gT.resolve())!=yT||mT&&kT(new mT)!=bT||VS&&kT(new VS)!=_T)&&(kT=function(e){var t=wS(e),n="[object Object]"==t?e.constructor:void 0,o=n?IS(n):"";if(o)switch(o){case xT:return wT;case ST:return vT;case TT:return yT;case ET:return bT;case CT:return _T}return t});var OT="[object String]";function AT(e){return"string"==typeof e||!SS(e)&&xS(e)&&wS(e)==OT}var $T=Object.prototype.hasOwnProperty;function PT(e){if(null==e)return!0;if(function(e){return null!=e&&zS(e.length)&&!AS(e)}(e)&&(SS(e)||"string"==typeof e||"function"==typeof e.splice||nT(e)||uT(e)||JS(e)))return!e.length;var t=kT(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(YS(e))return!function(e){if(!YS(e))return fT(e);var t=[];for(var n in Object(e))dT.call(e,n)&&"constructor"!=n&&t.push(n);return t}(e).length;for(var n in e)if($T.call(e,n))return!1;return!0}function MT(e,t=!1){if(!e)return t?"":{};const n=(new Date).getTime();return t?`?_t=${n}`:{_t:n}}function BT(e){var t;if("[object Object]"===Object.prototype.toString.call(e))for(const o in e){const r=(null==(t=e[o])?void 0:t.format)??null;if(r&&"function"==typeof r&&(e[o]=e[o].format("YYYY-MM-DD HH:mm:ss")),AT(o)){const t=e[o];if(t)try{e[o]=AT(t)?t.trim():t}catch(n){throw new Error(n)}}TS(e[o])&&BT(e[o])}}var IT=(e=>(e.SUCCESS="200",e[e.ERROR=-1]="ERROR",e[e.UNAUTHORIZED=401]="UNAUTHORIZED",e.TYPE="success",e[e.INTERNAL_SERVER_ERROR=500]="INTERNAL_SERVER_ERROR",e.VERIFY_SIGNATURE_FAIL="506",e))(IT||{}),LT=(e=>(e.GET="GET",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE",e))(LT||{}),jT=(e=>(e.JSON="application/json;charset=UTF-8",e.FORM_URLENCODED="application/x-www-form-urlencoded;charset=UTF-8",e.FORM_DATA="multipart/form-data;charset=UTF-8",e))(jT||{});const RT=Jw({id:"app-user",state:()=>({userInfo:null}),getters:{getUserInfo:e=>e.userInfo||Ay(Ey)||{}},actions:{setUserInfo(e){this.userInfo=e,this.lastUpdateTime=(new Date).getTime(),$y(Ey,e)},confirmLoginOut(){uv({title:"提示",content:"是否确认退出登录？",success:async e=>{e.confirm&&await this.logout(!0)}})}}});function NT(e,t,n,o="message"){RT(Qw);let r="";const{$uv:i}=uni;switch(t){case 400:r=`${n}`;break;case 401:r=n||"用户没有权限（令牌、用户名、密码错误）!";break;case 403:r="用户得到授权，但是访问是被禁止的。!";break;case 404:r="网络请求错误,未找到该资源!";break;case 405:r="网络请求错误,请求方法未允许!";break;case 408:r="网络请求错误,请求超时!";break;case 500:r="服务器繁忙，请稍后再试!";break;case 501:r="网络未实现";break;case 502:r="网络错误!";break;case 503:r="服务不可用,服务器暂时过载或维护!";break;case 504:r="网络超时!";break;case 505:r="http版本不支持该请求!"}r&&("modal"===o?uv({title:"错误提示",content:r,confirmText:"我知道了",showCancel:!1,success:e=>{}}):"message"===o&&i.toast(r))}const DT={themeColor:"#0960bd",showLogo:!0,showTitle:!0,isDeepSeek:!0,prefix:"/ywzz/",baseURL:"http://172.16.15.203:10011"},FT=[],UT=[],qT={joinPrefix:!0,isReturnNativeResponse:!1,isTransformResponse:!0,joinParamsToUrl:!1,formatDate:!0,errorMessageMode:"message",urlPrefix:"",joinTime:!0,withToken:!0,authenticationScheme:""},VT=()=>(hm(),DT.baseURL),HT=e=>{const{$uv:t}=uni;t.http.setConfig((e=>(e.baseURL=VT(),e.timeout=5e4,e.header={"Content-Type":jT.JSON},e.custom={...qT},e))),t.http.interceptors.request.use((async e=>{var t,n,o;const{apiUrl:r,joinPrefix:i,joinParamsToUrl:s,formatDate:a,joinTime:l=!0,urlPrefix:c,withToken:u,authenticationScheme:f}=e.custom||{};i&&(e.url=`${c}${e.url}`),(null==(t=e.url)?void 0:t.startsWith("/gateway"))||(e.url=`${DT.prefix.slice(0,-1)}${e.url}`),r&&AT(r)&&(e.baseURL=r);const d=Cy(),h=ky();d&&!1!==u&&(e.header.Authorization=f?`${f} ${d}`:d,e.header.token=d,e.header.openId=h);const p=e.params||{},g=e.data||!1;if(a&&g&&!AT(g)&&BT(g),(null==(n=e.method)?void 0:n.toUpperCase())===LT.GET||(null==(o=e.method)?void 0:o.toUpperCase())===LT.DELETE)AT(p)?(e.url=e.url+p+`${MT(l,!0)}`,e.params=void 0):e.params=Object.assign(p||{},MT(l,!1));else if(AT(p))e.url=e.url+p,e.params=void 0;else{if(a&&BT(p),Reflect.has(e,"data")&&e.data&&(Object.keys(e.data).length>0||e.data instanceof FormData)){if(FT.includes(e.url)){const t={...g,timestamp:Date.now()},n=Ow(t),o=$w(t);e.sourceData=g,e.data={request:n,sign:o}}else e.data=g;e.params=p}else e.data=p,e.params=void 0;s&&(e.url=function(e,t){let n="";for(const o in t)n+=o+"="+encodeURIComponent(t[o])+"&";return n=n.replace(/&$/,""),/\?$/.test(e)?e+n:e.replace(/\/?$/,"?")+n}(e.url,Object.assign({},e.params,e.data)))}return e}),(e=>Promise.reject(e))),t.http.interceptors.response.use((async e=>{var n;if(UT.includes(e.config.url)){const o=async()=>{if(e.config.header.errCount>2)throw new Error("请求出错，请稍候重试");e.config.header.errCount?e.config.header.errCount++:e.config.header.errCount=1;const n=await gy({url:"/api/users/secret"});return $y(wy,Aw(n.privateKey)),$y(xy,Aw(n.publicKey)),$y(Ty,n.expireTime+(new Date).getTime()),e.config.data=e.config.sourceData,t.http.request(e.config)},r=Ay(Ty);((null==(n=null==e?void 0:e.data)?void 0:n.code)===IT.VERIFY_SIGNATURE_FAIL||(new Date).getTime()>=r)&&o();const i=Aw(e.data.response);if(Pw(i,e.data.sign)&&i){const{data:e,code:t,message:n}=JSON.parse(i);if(t===IT.SUCCESS)return e;uv({title:"错误提示",content:n,confirmText:"确认",showCancel:!1})}else o()}const{data:o}=e;if(PT(o))throw new Error("请求出错，请稍候重试");500===o.code&&o.msg&&t.toast(o.msg);const{successMessageMode:r,isTransformResponse:i,isReturnNativeResponse:s,errorMessageMode:a}=e.config.custom||{};if("blob"===e.config.responseType||"arraybuffer"===e.config.responseType)return e.data;if(s)return e;if(!i)return e.data;const{code:l,data:c,msg:u}=o;if(o&&Reflect.has(o,"code")&&l.toString()===IT.SUCCESS){let e=u;return(null==e||void 0===e||""===e||PT(e))&&(e="操作成功"),"modal"===r?uv({title:"错误提示",content:e,confirmText:"我知道了",showCancel:!1}):"message"===r&&t.toast(e),c}let f="";if(l===IT.UNAUTHORIZED)f="登录超时,请重新登录!";else u&&(f=u);throw"modal"===a?uv({title:"错误提示",content:f,confirmText:"我知道了",showCancel:!1}):"message"===a&&t.toast(f),new Error("请求出错，请稍候重试")}),(e=>function(e){var t;const{data:n,statusCode:o,config:r}=e||{},i=(null==(t=null==r?void 0:r.custom)?void 0:t.errorMessageMode)||"none",s=(null==n?void 0:n.msg)??"";return NT(r.url,o,s,i),Promise.reject(n)}(e)))};(function(){const e=da(ux).use(lS);return function(e){e.use(Qw)}(e),HT(),function(e){const{$uv:t}=uni;t.setConfig({config:{},props:{text:{color:{default:"red"}}}})}(),{app:e}})().app.use(Bg).mount("#app");export{$h as $,Ov as A,Nw as B,sx as C,ax as D,ky as E,Ei as F,Hn as G,cx as H,hs as I,Ho as J,js as K,Dn as L,rx as M,ox as N,ix as O,Op as P,Qg as Q,_m as R,Fm as S,xm as T,Px as U,Mx as V,kx as W,Ax as X,$x as Y,Nm as Z,Br as _,Pi as a,kr as a0,ex as a1,tx as a2,em as a3,Ao as a4,Dp as a5,Bd as a6,Jw as a7,Qw as a8,zl as a9,DT as aa,Zh as ab,Gm as ac,Oy as ad,$y as ae,Sy as af,yy as ag,Qx as ah,lf as ai,nS as aj,mg as ak,Sv as al,IT as am,Tv as an,gy as ao,my as ap,py as aq,qi as b,ji as c,or as d,zi as e,Li as f,nx as g,Mr as h,Fp as i,Mw as j,Po as k,Vi as l,Wi as m,Zm as n,Sr as o,tS as p,eS as q,Fn as r,Ve as s,G as t,He as u,ca as v,So as w,Lp as x,wp as y,ao as z};

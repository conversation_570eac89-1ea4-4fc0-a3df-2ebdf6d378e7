<!doctype html><html><head><link rel="stylesheet" href="/ywzz/assets/uni.0f00e347.css"><script src="/ywzz/_app.config.js?v=0.0.0-42b2d424c442"></script><meta charset="UTF-8"/><script>var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')</script><title>com.ctyk.mobile</title><script type="module" crossorigin src="/ywzz/assets/index-BKmNCtIL.js"></script><link rel="stylesheet" crossorigin href="/ywzz/assets/index-DseYNS6-.css"></head><body><div id="app"></div></body></html>
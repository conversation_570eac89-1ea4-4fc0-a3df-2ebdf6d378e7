package com.ctyk.ywzz.event;

import org.springframework.context.ApplicationEvent;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.event
 * @author: zwj
 * @date: 2025/07/14  11:01
 * @description: 报告生成事件
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public class ReportGenerationEvent extends ApplicationEvent {
    
    private final Long recordId;
    
    public ReportGenerationEvent(Object source, Long recordId) {
        super(source);
        this.recordId = recordId;
    }
    
    public Long getRecordId() {
        return recordId;
    }
}

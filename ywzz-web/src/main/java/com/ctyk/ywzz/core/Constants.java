package com.ctyk.ywzz.core;

/**
 * @Description: 常量类
 * @author: wei
 * @date: 2019年4月16日
 * @version V1.0
 */
public class Constants {
    
    private Constants() {
    }
    
    /**
     * sm2密钥对存储路径
     */
    public static final String SECRET_PATH = "secret:";
    
    public static final String SM2_PATH = SECRET_PATH + "sm2:";
    
    public static final String CLIENT_PRIVATE_KEY = SM2_PATH + "clientPrivateKey";
    
    public static final String CLIENT_PUBLIC_KEY = SM2_PATH + "clientPublicKey";
    
    public static final String SERVER_PRIVATE_KEY = SM2_PATH + "serverPrivateKey";
    
    public static final String SERVER_PUBLIC_KEY = SM2_PATH + "serverPublicKey";
    
    public static final String TOKEN = "token";
    
    public static final String USER_ID = "openId";
    public static final String DEFAULT_USER_ID = "123456";
    
    /**
     * redis 储存
     */
    public static final String REDIS_ANNOTATION = "annotation";
    
    public static final String PROCESS = REDIS_ANNOTATION + ":process";
    
    /**
     * 过滤字段
     */
    public static final String REDIS_FILTER = "filter";
    
    public static final String FIELDS = REDIS_FILTER + ":fields";
    
    /**
     * 接口调用次数限制
     */
    public static final String REDIS_LIMIT = "limit";
    
    public static final String CALLS = REDIS_LIMIT + ":call";
    
    /**
     * 消息存储
     */
    public static final String REDIS_MESSAGE = "message";
    public static final String REDIS_AI_AUTH_PREFIX = "ai:auth:";
    
    public static final String MMPP = REDIS_MESSAGE + ":mmpp";
    
    public static final String BRACKET_OPEN = "[";
    
    public static final String BRACKET_CLOSE = "]";
    
    public static final String SEPARATOR_COLON = ":";
    
    public static final String LLM = "llm";
    
    public static final String RAG = "rag";
    
    public static final String DEFAULT_OPENID = "123456";
    
    public static final String ID_CARD = "居民身份证";
    
    public static final String GENDER = "男";
    
    public static final String GENDER_WOMAN = "女";
    
    public static final String HOME_ID = "Home-Id";
    
    public static final String TIMESTAMP = "timestamp";
    
    public static final String SIGN = "sign";
    
    public static final String SUCCESS = "success";
    public static final String SUCCESS_MESSAGE = "操作成功";
    
    public static final String CODE = "code";
    
    public static final String SUCCESS_CODE = "200";
    
    public static final String DATA = "data";
    public static final String ERROR_CODE = "errcode";
    
    /**
     * SSE 消息类型-智能问药
     */
    public static final String SSE_MESSAGE_MEDICINE = "medicine";
    
    public static final String ENCODE_NONE = "NONE";
    public static final String ENCODE_PARTIAL = "PARTIAL";
    public static final String ENCODE_FRAGMENT = "FRAGMENT";
    public static final String ENCODE_FULL = "FULL";
    
    public static final String CONTENT_TYPE_PDF = "application/pdf";
    public static final String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String CALL_SERVICE_ERROR = "调用第三方服务失败";
    public static final String REPORT_READING = "体检报告解读中";
    
    public static final String TTS_APP_KEY = "appkey";
    public static final String TTS_USER_ID = "userid";
    
    public static final String TTS_TIMESTAMP = "timestamp";
    
    public static final String TTS_SIGNATURE = "signature";
    
    public static final String TTS_TEXT_ID = "task_id";
    
    public static final String TTS_MD5 = "md5";
    public static final String TTS_AUDIO_TYPE = "audiotype";
    public static final String TTS_DOMAIN = "domain";
    public static final String TTS_PUNCTION= "punction";
    public static final String TTS_NUM_CONVERT= "num_convert";
    public static final String TTS_DOMAIN_VALUE= "medical";
    public static final String TTS_FORMAT_NAME = "format";
    public static final String TTS_FORMAT_VALUE = "mp3";
    public static final String TTS_VCN_NAME = "vcn";
    public static final String TTS_SAMPLE_NAME = "sample";
    public static final String TTS_TEXT_NAME = "text";
    
    public static final String TTS_AI = "ai";
    public static final String TTS_YZS = "yzs";
    public static final String SSE_PREFIX = "data:";
    public static final String SSE_END = "[DONE]";
    public static final String REPORT_ERROR = "[ERROR]";
    public static final String ERROR_MESSAGE = "系统繁忙，请稍后再试";
    public static final String ROLE_SYSTEM="system";
    public static final String ROLE_ASSISTANT="assistant";
    public static final String ROLE_USER="user";
}


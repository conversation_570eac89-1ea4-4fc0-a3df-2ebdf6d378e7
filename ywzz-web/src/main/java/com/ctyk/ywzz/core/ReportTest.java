package com.ctyk.ywzz.core;

import java.util.HashMap;
import java.util.Map;

/**
 * @project: IG
 * @package: com.ctyk.igs.constants
 * @author: Liyh
 * @date: 2024/08/22 10:14
 * @description: 报告模块常量数据测试类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public class ReportTest {

    public static Map<String, String> reportListMap = new HashMap<>(20);

    public static Map<String, String> reportDetailMap = new HashMap<>(20);

    static {
        reportListMap.put("0007936186", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList>
                		<PeRecord>
                			<name>方兆玉</name>
                			<sex>女</sex>
                			<age>27</age>
                			<examDate>2024-08-14</examDate>
                			<admId>6608140326</admId>
                			<company>北京大学第一医院</company>
                			<reportStatus>已打印</reportStatus>
                			<peStatus>到达</peStatus>
                			<preDate>2024-08-14</preDate>
                		</PeRecord>
                	</ResultList></Response>""");
        reportDetailMap.put("7995142", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>周凯</summaryDoctor>
                	    <summaryDate>2024-08-14</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-08-15</auditDate>
                	    <summaryList>
                	    	<SummaryItem>
                                <item> 1、脂肪肝</item>
                                <itemDesc>       改善生活方式，摄入低脂饮食，控制血脂，坚持规律运动，限制饮酒，控制体重，定期复查血脂、肝功及腹部超声，全科医学科随诊。</itemDesc>
                            </SummaryItem>
                            <SummaryItem>
                                <item> 2、慢性胃炎</item>
                                <itemDesc>       避免刺激性食物和饮料，如辛辣、油腻、咖啡、浓茶等，保持饮食规律，细嚼慢咽，适量摄入易消化的食物。如有幽门螺杆菌感染，应在医生指导下进行根除治疗。注意胃部保暖，避免过度劳累和精神紧张，消化内科随诊。</itemDesc>
                            </SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");
        /*reportListMap.put("0007936186", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList>
                		<PeRecord>
                			<name>方兆玉</name>
                			<sex>女</sex>
                			<age>27</age>
                			<examDate>2024-08-14</examDate>
                			<admId>6608140326</admId>
                			<company>北京大学第一医院</company>
                			<reportStatus>已打印</reportStatus>
                			<peStatus>到达</peStatus>
                			<preDate>2024-08-14</preDate>
                		</PeRecord>
                		<PeRecord>
                			<name>方兆玉</name>
                			<sex>女</sex>
                			<age>27</age>
                			<examDate>2024-08-14</examDate>
                			<company>北京大学第一医院</company>
                			<admId>6608140415</admId>
                			<reportStatus>已审核</reportStatus>
                			<peStatus>到达</peStatus>
                			<preDate>2024-08-14</preDate>
                		</PeRecord>
                		<PeRecord>
                			<name>方兆玉</name>
                			<sex>女</sex>
                			<age>27</age>
                			<examDate>2024-05-27</examDate>
                			<company>北京大学第一医院</company>
                			<admId>6605270187</admId>
                			<reportStatus>未审核</reportStatus>
                			<peStatus>到达</peStatus>
                			<preDate>2024-05-27</preDate>
                		</PeRecord>
                	</ResultList></Response>""");

        reportListMap.put("0007936187", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList>
                		<PeRecord>
                			<name>王凯</name>
                			<sex>男</sex>
                			<age>42</age>
                			<examDate>2024-02-15</examDate>
                			<admId>6602151245</admId>
                			<company>北京大学第一医院</company>
                			<reportStatus>已打印</reportStatus>
                			<peStatus>到达</peStatus>
                			<preDate>2024-02-15</preDate>
                		</PeRecord>
                		<PeRecord>
                			<name>王凯</name>
                			<sex>男</sex>
                			<age>42</age>
                			<examDate>2024-06-07</examDate>
                			<company>北京大学第一医院</company>
                			<admId>6606070287</admId>
                			<reportStatus>已审核</reportStatus>
                			<peStatus>到达</peStatus>
                			<preDate>2024-06-07</preDate>
                		</PeRecord>
                	</ResultList></Response>""");

        reportListMap.put("0007936188", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList>
                		<PeRecord>
                		    <name>李正东</name>
                		    <sex>男</sex>
                		    <age>16</age>
                		    <examDate>2024-06-08</examDate>
                		    <admId>6606080713</admId>
                		    <company>北京大学第一医院</company>
                		    <reportStatus>已打印</reportStatus>
                		    <peStatus>到达</peStatus>
                		    <preDate>2024-06-08</preDate>
                		</PeRecord>
                	</ResultList></Response>""");

        reportListMap.put("0007936189", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList>
                		<PeRecord>
                		    <name>马云婷</name>
                		    <sex>女</sex>
                		    <age>37</age>
                		    <examDate>2024-06-19</examDate>
                		    <admId>6606192563</admId>
                		    <company>北京大学第一医院</company>
                		    <reportStatus>已打印</reportStatus>
                		    <peStatus>到达</peStatus>
                		    <preDate>2024-06-19</preDate>
                		</PeRecord>
                		<PeRecord>
                		    <name>马云婷</name>
                		    <sex>女</sex>
                		    <age>37</age>
                		    <examDate>2024-06-19</examDate>
                		    <admId>6606191154</admId>
                		    <company>北京大学第一医院</company>
                		    <reportStatus>已打印</reportStatus>
                		    <peStatus>到达</peStatus>
                		    <preDate>2024-06-19</preDate>
                		</PeRecord>
                	</ResultList></Response>""");

        reportListMap.put("0007936190", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList>
                		<PeRecord>
                		    <name>张京平</name>
                		    <sex>男</sex>
                		    <age>23</age>
                		    <examDate>2024-06-14</examDate>
                		    <admId>6606142475</admId>
                		    <company>北京大学第一医院</company>
                		    <reportStatus>已打印</reportStatus>
                		    <peStatus>到达</peStatus>
                		    <preDate>2024-06-14</preDate>
                		</PeRecord>
                	</ResultList></Response>""");

        reportListMap.put("0007936191", """
                <Response><ResultCode>1</ResultCode><ResultDesc>未查询到</ResultDesc></Response>""");
        //报告明细
        reportDetailMap.put("6608140326", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>周凯</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	    	<SummaryItem>
                	    		<item> 1、超重</item>
                	    		<itemDesc>       24≤BMI＜28，建议调整饮食结构，加强锻炼，控制体重。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、脂肪肝</item>
                	    		<itemDesc>       改善生活方式，摄入低脂饮食，控制血脂，坚持规律运动，限制饮酒，控制体重，定期复查血脂、肝功及腹部超声，全科医学科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 3、胆囊息肉</item>
                	    		<itemDesc>       胆囊息肉是指胆囊壁向胆囊腔内突起和隆起性病变，多为良性病变，少数可发生恶变或为恶性。建议全科医学科就诊，请全科医学科医生进行综合评估。暂无需手术者，建议定期复查（6-12个月），全科医学科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 4、血脂偏高</item>
                	    		<itemDesc>       目前血脂水平偏高，建议摄入低脂饮食，规律有氧运动，控制体重，定期复查，必要时全科医学科就诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 5、右肺肿块影</item>
                	    		<itemDesc>       建议您全科医学科就诊，请行肺部CT平扫+增强，进一步明确诊断。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6608140415", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>田梦祺</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	    	<SummaryItem>
                	    		<item> 1、视力下降</item>
                	    		<itemDesc>       注意用眼卫生，避免长时间近距离用眼，如看书、电脑等，每隔一段时间远眺放松眼睛，保持充足的睡眠，定期进行眼科检查，如视力测试、眼底检查等，必要时配戴合适的眼镜或进行视力矫正手术，眼科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、慢性胃炎</item>
                	    		<itemDesc>       避免刺激性食物和饮料，如辛辣、油腻、咖啡、浓茶等，保持饮食规律，细嚼慢咽，适量摄入易消化的食物。如有幽门螺杆菌感染，应在医生指导下进行根除治疗。注意胃部保暖，避免过度劳累和精神紧张，消化内科随诊。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6605270187", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>王立平</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	    	<SummaryItem>
                	    		<item> 1、血脂偏高</item>
                	    		<itemDesc>       调整饮食结构，减少饱和脂肪和反式脂肪的摄入，如红肉、油炸食品等，增加富含膳食纤维的食物，如全谷物、蔬菜和水果，适量摄入富含健康脂肪的食物，如鱼类、坚果和橄榄油。坚持规律的有氧运动，如快走、游泳、骑自行车等，控制体重，必要时在医生指导下使用调脂药物，心血管内科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、慢性胃炎</item>
                	    		<itemDesc>       避免刺激性食物和饮料，如辛辣、油腻、咖啡、浓茶等，保持饮食规律，细嚼慢咽，适量摄入易消化的食物。如有幽门螺杆菌感染，应在医生指导下进行根除治疗。注意胃部保暖，避免过度劳累和精神紧张，消化内科随诊。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6602151245", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>田梦祺</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	    	<SummaryItem>
                	    		<item> 1、高血压</item>
                	    		<itemDesc>       定期监测血压，调整饮食结构，减少盐分摄入，增加富含钾的食物（如香蕉、土豆），适量进行有氧运动。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、骨质疏松</item>
                	    		<itemDesc>       增加富含钙质和维生素D的食物（如牛奶、鱼类、绿叶蔬菜），适量进行负重和力量训练，如散步、慢跑、举重等，戒烟限酒，避免长期大量使用影响骨代谢的药物，必要时在医生指导下进行药物治疗，骨科或内分泌科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 3、脂肪肝</item>
                	    		<itemDesc>       改善生活方式，摄入低脂饮食，控制血脂，坚持规律运动，限制饮酒，控制体重，定期复查血脂、肝功及腹部超声，全科医学科随诊。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6606070287", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>王立平</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	    	<SummaryItem>
                	    		<item> 1、甲状腺结节</item>
                	    		<itemDesc>       定期进行甲状腺超声检查，监测结节的大小和形态变化。如结节较大或有恶性征象，应进一步进行甲状腺功能检查和细针穿刺活检。保持饮食均衡，避免过量摄入碘或缺乏碘。如有需要，在医生指导下进行药物治疗或手术治疗，内分泌科或外科随诊。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6606080713", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>周凯</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	        <SummaryItem>
                	    		<item> 1、超重</item>
                	    		<itemDesc>       24≤BMI＜28，建议调整饮食结构，加强锻炼，控制体重。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、骨质疏松</item>
                	    		<itemDesc>       增加富含钙质和维生素D的食物（如牛奶、鱼类、绿叶蔬菜），适量进行负重和力量训练，如散步、慢跑、举重等，戒烟限酒，避免长期大量使用影响骨代谢的药物，必要时在医生指导下进行药物治疗，骨科或内分泌科随诊。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6606192563", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>王立平</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	        <SummaryItem>
                	    		<item> 1、颈椎病</item>
                	    		<itemDesc>       保持正确的坐姿和站姿，避免长时间低头或伏案工作，适当进行颈部运动，如左右转动、上下点头等，睡觉时选择合适的枕头，避免颈部过度弯曲或伸展，定期进行颈部按摩或理疗，疼痛严重时可考虑佩戴颈托或使用药物缓解疼痛，骨科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、高血压</item>
                	    		<itemDesc>       定期监测血压，调整饮食结构，减少盐分摄入，增加富含钾的食物（如香蕉、土豆），适量进行有氧运动。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6606191154", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>田梦祺</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	        <SummaryItem>
                	    		<item> 1、甲状腺结节</item>
                	    		<itemDesc>       定期进行甲状腺超声检查，监测结节的大小和形态变化。如结节较大或有恶性征象，应进一步进行甲状腺功能检查和细针穿刺活检。保持饮食均衡，避免过量摄入碘或缺乏碘。如有需要，在医生指导下进行药物治疗或手术治疗，内分泌科或外科随诊。</itemDesc>
                	    	</SummaryItem>
                	    	<SummaryItem>
                	    		<item> 2、右肺肿块影</item>
                	    		<itemDesc>       建议您全科医学科就诊，请行肺部CT平扫+增强，进一步明确诊断。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");

        reportDetailMap.put("6606142475", """
                <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc>
                	<ResultList>
                	<ExaminationSummary>
                	    <summaryDoctor>王立平</summaryDoctor>
                	    <summaryDate>2024-01-29</summaryDate>
                	    <auditDoctor>田梦祺</auditDoctor>
                	    <auditDate>2024-01-30</auditDate>
                	    <summaryList>
                	        <SummaryItem>
                	    		<item> 1、血脂偏高</item>
                	    		<itemDesc>       目前血脂水平偏高，建议摄入低脂饮食，规律有氧运动，控制体重，定期复查，必要时全科医学科就诊。</itemDesc>
                	    	</SummaryItem>
                	    </summaryList>
                    </ExaminationSummary></ResultList></Response>""");*/

    }
}

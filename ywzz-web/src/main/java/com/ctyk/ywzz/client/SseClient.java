package com.ctyk.ywzz.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @project: ctyk-mia
 * @package: com.ctyk.mia.client
 * @author: zwj
 * @date: 2024/11/14  15:07
 * @description: sse客户端
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
public class SseClient {
    
    private static final Map<String, SseEmitter> SSE_EMITTER_MAP = new ConcurrentHashMap<>();
    
    
    /**
     * 连接sse
     *
     * @param userId 用户 ID
     * @return {@link SseEmitter }
     */
    public SseEmitter connect(String userId) {
        
        // 超时时间
        SseEmitter sseEmitter = new SseEmitter(0L);
        // 完成后回调
        sseEmitter.onCompletion(() -> SSE_EMITTER_MAP.remove(userId));
        // 超时回调
        sseEmitter.onTimeout(() -> {
            log.error("sse超时:{}", userId);
            sseEmitter.complete();
            SSE_EMITTER_MAP.remove(userId);
        });
        // 异常回调
        sseEmitter.onError(throwable -> {
            log.error("sse出现异常,userId:{},异常信息：{}", userId, throwable.getMessage());
            sseEmitter.completeWithError(throwable);
            SSE_EMITTER_MAP.remove(userId);
        });
        try {
            // 发送初始事件
            sseEmitter.send(SseEmitter.event().reconnectTime(5000));
        } catch (IOException e) {
            log.error("在发送初始事件时sse出现异常:{}", e.getMessage());
            // 发送失败时关闭连接
            sseEmitter.complete();
            SSE_EMITTER_MAP.remove(userId);
            return null;
        }
        SSE_EMITTER_MAP.put(userId, sseEmitter);
        log.info("sse连接成功:{}", userId);
        return sseEmitter;
    }
    
    /**
     * 给指定用户发送消息
     *
     * @param userId 用户id
     * @param message 消息
     * @return boolean
     */
    public boolean sendMessage(String userId, String message) {
        
        if (ObjectUtils.isEmpty(message)) {
            log.warn("{}消息为空", userId);
            return false;
        }
        SseEmitter sseEmitter = SSE_EMITTER_MAP.get(userId);
        if (ObjectUtils.isEmpty(sseEmitter)) {
            log.warn("消息推送失败userId:{},没有创建连接，请重试。消息内容：{}", userId, message);
            return false;
        }
        try {
            sseEmitter.send(SseEmitter.event().data(message));
            return true;
        } catch (IOException e) {
            SSE_EMITTER_MAP.remove(userId);
            return false;
        }
    }
    
    /**
     * 关闭 SSE 连接
     *
     * @param userId 用户 ID
     */
    public void closeSseConnect(String userId) {
        
        log.info("{}关闭sse连接", userId);
        if (SSE_EMITTER_MAP.containsKey(userId)) {
            SseEmitter sseEmitter = SSE_EMITTER_MAP.get(userId);
            sseEmitter.complete();
            SSE_EMITTER_MAP.remove(userId);
        } else {
            log.info("用户{}已关闭sse连接", userId);
        }
    }
    
    /**
     * 心跳
     *
     */
    public void heartbeat() {
        
        SSE_EMITTER_MAP.forEach((key, value) -> {
            sendMessage(key, "heartbeat");
        });
    }
    
}

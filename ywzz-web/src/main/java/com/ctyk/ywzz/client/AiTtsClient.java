package com.ctyk.ywzz.client;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.common.json.utils.JsonUtils;
import com.ctyk.ywzz.config.AiTtsConfig;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.enums.AiUriEnum;
import com.ctyk.ywzz.service.impl.AiWebSocket;
import com.ctyk.ywzz.service.impl.RedisService;
import com.ctyk.ywzz.util.AiSignUtils;
import com.ctyk.ywzz.util.AudioConvertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.client
 * @author: zwj
 * @date: 2025/06/05  09:05
 * @description: AI公司语音合成客户端
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiTtsClient {
    
    private final AiTtsConfig aiTtsConfig;
    
    private final RedisService redisService;
    
    /**
     * 语音合成
     *
     * @param text 文本内容
     * @return {@link byte[] } 音频数据
     */
    public byte[] textToSpeech(String text) {
        AiWebSocket webSocketClient = null;
        try {
            log.info("开始语音合成，文本: {}", text);
            
            // 构建WebSocket URI
            String url = aiTtsConfig.getProtocol() + aiTtsConfig.getBaseUrl() + AiUriEnum.TTS_NEW.getUri();
            log.debug("websocket请求地址:{}", url);
            URI uri = new URI(url);
            
            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(AiUriEnum.TTS_NEW.getUri(),
                    AiUriEnum.TTS_NEW.getMethod());
            
            // 创建WebSocket客户端进行语音合成
            webSocketClient = new AiWebSocket(uri, headers, AiUriEnum.TTS_NEW.getCode(), aiTtsConfig.getVoice());
            
            // 连接并发送TTS请求
            CompletableFuture<byte[]> future = new CompletableFuture<>();
            webSocketClient.setTtsResultCallback(future::complete);
            
            webSocketClient.connect();
            
            // 等待连接建立后发送文本
            Thread.sleep(1000);
            webSocketClient.sendTtsText(text);
            
            // 等待结果
            byte[] audioData = future.get(aiTtsConfig.getWaitTime(), TimeUnit.SECONDS);
            
            webSocketClient.close();
            
            log.info("语音合成完成，音频数据大小: {} bytes", audioData.length);
            return audioData;
            
        } catch (Exception e) {
            log.error("语音合成失败", e);
            throw new ServiceException("语音合成失败");
        } finally {
            if (webSocketClient != null) {
                webSocketClient.close();
            }
            log.info("语音合成完成");
        }
    }
    
    /**
     * 语音转文本
     *
     * @param speechFile 语音文件 (WAV或PCM格式)
     * @return {@link String } 识别的文本
     */
    public String speechToText(MultipartFile speechFile) {
        AiWebSocket webSocketClient = null;
        try {
            log.info("开始语音转文本，文件: {}, 大小: {} bytes", speechFile.getOriginalFilename(), speechFile.getSize());
            
            // 检查文件格式
            if (!isValidAudioFile(speechFile)) {
                throw new IllegalArgumentException("不支持的音频文件格式");
            }
            
            // 获取音频格式信息
            String formatInfo = AudioConvertUtils.detectAudioFormat(speechFile);
            log.info("检测到音频格式: {}", formatInfo);
            
            // 转换为PCM格式
            byte[] pcmData = convertToPcm(speechFile);
            
            // 验证PCM数据
            if (!AudioConvertUtils.validatePcmData(pcmData)) {
                log.warn("PCM数据验证失败，使用备用数据");
                pcmData = generateTestPcmData();
            }
            
            double duration = AudioConvertUtils.getAudioDuration(pcmData);
            log.info("音频时长: {} 秒", String.format("%.2f", duration));
            
            // 保存PCM文件用于调试
          /*  if (log.isDebugEnabled()) {
                String pcmFilePath = "audio_" + System.currentTimeMillis() + ".pcm";
                AudioConvertUtils.savePcmToFile(pcmData, pcmFilePath);
                log.debug("PCM文件已保存: {}", pcmFilePath);
            }*/
            
            // 构建WebSocket URI
            String url = aiTtsConfig.getProtocol() + aiTtsConfig.getBaseUrl() + AiUriEnum.ASR.getUri();
            URI uri = new URI(url);
            
            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(AiUriEnum.ASR.getUri(), AiUriEnum.ASR.getMethod());
            
            // 创建WebSocket客户端进行语音识别
            webSocketClient = new AiWebSocket(uri, headers, AiUriEnum.ASR.getCode(), aiTtsConfig.getVoice());
            
            // 连接并发送ASR请求
            CompletableFuture<String> future = new CompletableFuture<>();
            webSocketClient.setAsrResultCallback(future::complete);
            
            webSocketClient.connect();
            
            // 等待连接建立后发送PCM数据
            Thread.sleep(1000);
            webSocketClient.sendAsrAudio(pcmData);
            
            // 等待结果，最多等待60秒
            String recognizedText = future.get(60, TimeUnit.SECONDS);
            
            webSocketClient.close();
            
            log.debug("语音转文本完成，识别结果: {}", recognizedText);
            
            // 确保返回非null值，即使识别结果为空
            return CharSequenceUtil.isBlank(recognizedText) ? "" : recognizedText;
            
        } catch (Exception e) {
            log.error("语音转文本失败", e);
            throw new ServiceException("语音转文本失败");
        } finally {
            if (webSocketClient != null) {
                webSocketClient.close();
            }
        }
    }
    
    /**
     * 生成认证头信息
     *
     * @param path HTTP请求路径
     * @param method HTTP请求方法
     * @return 认证头Map
     */
    private Map<String, String> generateAuthHeaders(String path, HttpMethod method) {
        
        String key = Constants.REDIS_AI_AUTH_PREFIX + path;
        if (redisService.hasKey(key)) {
            String authJson = redisService.getString(key);
            return JsonUtils.parseObject(authJson, Map.class);
        }
        try {
            Map<String, String> params = new HashMap<>();
            Map<String, String> headers = new HashMap<>();
            
            // 使用配置中的ID作为signedHeaders
            String signedHeaders = aiTtsConfig.getId();
            
            // 构建基础认证字符串
            String basic = CharSequenceUtil.join("/", aiTtsConfig.getOriginName(), aiTtsConfig.getId(),
                    aiTtsConfig.getRegion(), System.currentTimeMillis() / 1000, aiTtsConfig.getExpiration());
            
            // 生成签名
            String signature = AiSignUtils.genSignature(basic, aiTtsConfig.getKey(), method, path, params, headers,
                    signedHeaders);
            
            // 构建Authorization头
            String authorization = String.format("%s/%s/%s", basic, signedHeaders, signature);
            
            // 设置请求头
            headers.put("X-APP-ID", aiTtsConfig.getId());
            headers.put("Authorization", authorization);
            
            log.info("生成认证头完成，Authorization: {}", authorization);
            redisService.setEx(key, JsonUtils.toJsonString(headers), aiTtsConfig.getExpiration());
            return headers;
            
        } catch (Exception e) {
            log.error("生成认证头失败", e);
            throw new ServiceException("生成认证头失败");
        }
    }
    
    /**
     * 检查是否为有效的音频文件
     *
     * @param file 文件
     * @return 是否有效
     */
    private boolean isValidAudioFile(MultipartFile file) {
        
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null) {
            return false;
        }
        
        // 检查文件扩展名（仅支持WAV和PCM）
        String extension = filename.toLowerCase();
        if (!extension.endsWith(".wav") && !extension.endsWith(".pcm")) {
            log.warn("不支持的文件格式: {}，仅支持WAV和PCM格式", extension);
            return false;
        }
        
        // 检查文件大小（限制为50MB）
        if (file.getSize() > 50 * 1024 * 1024) {
            log.warn("文件过大: {} bytes", file.getSize());
            return false;
        }
        
        // 检查最小文件大小
        if (file.getSize() < 10) {
            log.warn("文件过小: {} bytes", file.getSize());
            return false;
        }
        
        return true;
    }
    
    /**
     * 转换音频文件为PCM格式
     *
     * @param audioFile 音频文件
     * @return PCM数据
     * @throws Exception 转换异常
     */
    private byte[] convertToPcm(MultipartFile audioFile) throws Exception {
        String filename = audioFile.getOriginalFilename();
        if (filename == null) {
            throw new IllegalArgumentException("文件名为空");
        }
        
        String extension = filename.toLowerCase();
        
        if (extension.endsWith(".wav")) {
            // WAV转PCM
            log.info("转换WAV文件为PCM");
            return AudioConvertUtils.wavToPcm(audioFile);
        } else if (extension.endsWith(".pcm")) {
            // 已经是PCM格式，直接返回
            log.info("文件已经是PCM格式");
            byte[] pcmData = audioFile.getBytes();
            
            // 验证并可能需要格式调整
            if (AudioConvertUtils.validatePcmData(pcmData)) {
                return pcmData;
            } else {
                log.warn("PCM文件格式不符合要求，尝试调整");
                return adjustPcmFormat(pcmData);
            }
        } else {
            throw new IllegalArgumentException("不支持的文件格式: " + extension + "，仅支持WAV和PCM格式");
        }
    }
    
    /**
     * 调整PCM格式
     *
     * @param originalPcm 原始PCM数据
     * @return 调整后的PCM数据
     */
    private byte[] adjustPcmFormat(byte[] originalPcm) {
        log.info("调整PCM格式，原始大小: {} bytes", originalPcm.length);
        
        // 确保长度为偶数
        byte[] adjustedPcm = originalPcm;
        if (originalPcm.length % 2 != 0) {
            adjustedPcm = new byte[originalPcm.length + 1];
            System.arraycopy(originalPcm, 0, adjustedPcm, 0, originalPcm.length);
            adjustedPcm[originalPcm.length] = 0;
        }
        
        // 如果数据太小，重复数据 ，约1秒的16kHz 16位音频
        int minSize = 32000;
        if (adjustedPcm.length < minSize) {
            int repeatTimes = (minSize / adjustedPcm.length) + 1;
            byte[] repeatedPcm = new byte[adjustedPcm.length * repeatTimes];
            
            for (int i = 0; i < repeatTimes; i++) {
                System.arraycopy(adjustedPcm, 0, repeatedPcm, i * adjustedPcm.length, adjustedPcm.length);
            }
            
            // 截取到合适大小
            if (repeatedPcm.length > minSize) {
                adjustedPcm = new byte[minSize];
                System.arraycopy(repeatedPcm, 0, adjustedPcm, 0, minSize);
            } else {
                adjustedPcm = repeatedPcm;
            }
        }
        
        // 如果数据太大，进行降采样
        // 约10秒的16kHz 16位音频
        int maxSize = 320000;
        if (adjustedPcm.length > maxSize) {
            int step = adjustedPcm.length / maxSize;
            // 确保是偶数
            step = (step / 2) * 2;
            
            byte[] downsampledPcm = new byte[maxSize];
            for (int i = 0, j = 0; i < adjustedPcm.length - 1 && j < downsampledPcm.length - 1; i += step, j += 2) {
                downsampledPcm[j] = adjustedPcm[i];
                downsampledPcm[j + 1] = adjustedPcm[i + 1];
            }
            adjustedPcm = downsampledPcm;
        }
        
        log.info("PCM格式调整完成，调整后大小: {} bytes", adjustedPcm.length);
        return adjustedPcm;
    }
    
    /**
     * 生成测试PCM数据
     *
     * @return 测试PCM数据
     */
    private byte[] generateTestPcmData() {
        log.info("生成测试PCM数据");
        
        int sampleRate = 16000;
        int durationSeconds = 3;
        int samples = sampleRate * durationSeconds;
        // 16位 = 2字节
        byte[] pcmData = new byte[samples * 2];
        
        // 生成简单的正弦波
        for (int i = 0; i < samples; i++) {
            // A4音符
            double frequency = 440.0;
            // 较小的音量
            double amplitude = 0.1;
            short sample = (short) (amplitude * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * i / sampleRate));
            
            // 小端字节序
            pcmData[i * 2] = (byte) (sample & 0xFF);
            pcmData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        log.info("生成测试PCM数据完成，大小: {} bytes", pcmData.length);
        return pcmData;
    }
    
    /**
     * 发送GET请求
     *
     * @param path 请求路径
     * @param queryParams 查询参数
     * @param additionalHeaders 额外的请求头
     * @return 响应内容
     */
    public String sendGetRequest(String path, Map<String, String> queryParams, Map<String, String> additionalHeaders) {
        try {
            String url = aiTtsConfig.getBaseUrl() + path;
            
            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(path, HttpMethod.GET);
            
            // 添加额外的请求头
            if (additionalHeaders != null) {
                headers.putAll(additionalHeaders);
            }
            
            // 构建请求
            HttpRequest request = HttpRequest.get(url);
            
            // 添加查询参数
            if (queryParams != null && !queryParams.isEmpty()) {
                for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                    request.form(entry.getKey(), entry.getValue());
                }
            }
            
            // 添加请求头
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.header(entry.getKey(), entry.getValue());
            }
            
            // 执行请求
            HttpResponse response = request.execute();
            
            log.info("GET请求完成，状态码: {}", response.getStatus());
            return response.body();
            
        } catch (Exception e) {
            log.error("GET请求失败", e);
            throw new RuntimeException("GET请求失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 发送POST请求
     *
     * @param path 请求路径
     * @param queryParams 查询参数
     * @param additionalHeaders 额外的请求头
     * @param requestBody 请求体
     * @return 响应内容
     */
    public String sendPostRequest(String path, Map<String, String> queryParams, Map<String, String> additionalHeaders,
            String requestBody) {
        try {
            String url = aiTtsConfig.getBaseUrl() + path;
            
            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(path, HttpMethod.POST);
            
            // 添加额外的请求头
            if (additionalHeaders != null) {
                headers.putAll(additionalHeaders);
            }
            
            // 构建请求
            HttpRequest request = HttpRequest.post(url);
            
            // 添加查询参数
            if (queryParams != null && !queryParams.isEmpty()) {
                for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                    request.form(entry.getKey(), entry.getValue());
                }
            }
            
            // 添加请求头
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.header(entry.getKey(), entry.getValue());
            }
            
            // 添加请求体
            if (requestBody != null && !requestBody.isEmpty()) {
                request.body(requestBody);
            }
            
            // 执行请求
            HttpResponse response = request.execute();
            
            log.info("POST请求完成，状态码: {}", response.getStatus());
            return response.body();
            
        } catch (Exception e) {
            log.error("POST请求失败", e);
            throw new RuntimeException("POST请求失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成授权头（用于测试）
     *
     * @param httpMethod HTTP方法
     * @param uri URI路径
     * @param queryString 查询字符串
     * @param headers 请求头
     * @param payload 请求体
     * @return 授权头字符串
     */
    public String generateAuthorizationHeader(String httpMethod, String uri, String queryString,
            Map<String, String> headers, String payload) {
        
        try {
            Map<String, String> params = new HashMap<>();
            if (queryString != null && !queryString.isEmpty()) {
                String[] pairs = queryString.split("&");
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=");
                    if (keyValue.length == 2) {
                        params.put(keyValue[0], keyValue[1]);
                    }
                }
            }
            
            String signedHeaders = aiTtsConfig.getId();
            
            String basic = StrUtil.join("/", "teleai-cloud-auth-v1", aiTtsConfig.getId(), aiTtsConfig.getRegion(),
                    System.currentTimeMillis() / 1000, aiTtsConfig.getExpiration());
            
            HttpMethod method = HttpMethod.valueOf(httpMethod.toUpperCase());
            String signature = AiSignUtils.genSignature(basic, aiTtsConfig.getKey(), method, uri, params, headers,
                    signedHeaders);
            
            return String.format("%s/%s/%s", basic, signedHeaders, signature);
            
        } catch (Exception e) {
            log.error("生成授权头失败", e);
            throw new RuntimeException("生成授权头失败: " + e.getMessage(), e);
        }
    }
}

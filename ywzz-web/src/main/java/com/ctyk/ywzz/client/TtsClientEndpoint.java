package com.ctyk.ywzz.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctyk.ywzz.util.UserUtil;
import jakarta.websocket.ClientEndpoint;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.client
 * @author: zwj
 * @date: 2025/05/12  13:40
 * @description: 云之声语音合成客户端
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@ClientEndpoint
public class TtsClientEndpoint {
    
    // 用于收集音频数据的缓冲区
    private final ByteArrayOutputStream audioBuffer;
    
    // 用于异步返回结果
    private final CompletableFuture<byte[]> resultFuture;
    
    private final String text;
    
    // 发音人
    private final String vcn;
    
    private static final String LOG_CLOSE_CONNECTION_ERROR = "在关闭连接时出现异常:{}";
    
    public TtsClientEndpoint(String text, String vcn, CompletableFuture<byte[]> resultFuture) {
        this.text = text;
        this.vcn = vcn;
        this.resultFuture = resultFuture;
        this.audioBuffer = new ByteArrayOutputStream();
    }
    
    @OnOpen
    public void onOpen(Session session) {
        
        log.info("【文本转语音websocket】创建连接成功");
        
        // 发送start请求
        JSONObject frame = new JSONObject();
        frame.put("format", "pcm");
        frame.put("sample", "16000");
        frame.put("vcn", vcn);
        frame.put("speed", 50);
        frame.put("volume", 50);
        frame.put("pitch", 50);
        frame.put("bright", 50);
        frame.put("text", text);
        frame.put("user_id", UserUtil.getUserIdOrDefault());
        
        try {
            log.info("开始发送文本:{}", text);
            session.getBasicRemote().sendText(frame.toJSONString());
            log.info("发送文本完成");
        } catch (IOException e) {
            log.error("在发送start请求时出现异常:{}", e.getMessage(), e);
            // 出错时完成future并异常
            resultFuture.completeExceptionally(e);
            try {
                session.close();
            } catch (IOException ex) {
                log.error(LOG_CLOSE_CONNECTION_ERROR, ex.getMessage(), ex);
            }
        }
    }
    
    @OnMessage
    public void processBinaryMessage(Session session, byte[] data) throws IOException {
        
        log.info("【文本转语音websocket】收到音频数据");
        // 将音频数据追加到内存缓冲区，而不是写入文件
        try {
            audioBuffer.write(data);
        } catch (IOException e) {
            log.error("在追加音频数据时出现异常:{}", e.getMessage(), e);
            resultFuture.completeExceptionally(e);
            try {
                session.close();
            } catch (IOException ex) {
                log.error(LOG_CLOSE_CONNECTION_ERROR, ex.getMessage(), ex);
            }
        }
    }
    
    @OnMessage
    public void processTextMessage(Session session, String data) {
        
        log.info("【文本转语音websocket】收到文本消息:{}", data);
        JSONObject jsonObject = JSON.parseObject(data);
        boolean end = jsonObject.getBooleanValue("end");
        if (end && session.isOpen()) {
            try {
                // 收到结束标志，完成future并返回收集到的音频数据
                resultFuture.complete(audioBuffer.toByteArray());
                session.close();
            } catch (IOException e) {
                log.error(LOG_CLOSE_CONNECTION_ERROR, e.getMessage(), e);
                resultFuture.completeExceptionally(e);
            }
        }
        log.info("sid:{}主动断开连接", session.getId());
    }
    
    @OnError
    public void processError(Throwable t) {
        
        // 发生错误时完成future并异常
        resultFuture.completeExceptionally(t);
    }
    
    @OnClose
    public void onClose(Session session) {
        
        log.info("【文本转语音websocket】连接关闭");
        // 如果连接关闭但future尚未完成，则用当前收集到的数据完成
        if (!resultFuture.isDone()) {
            resultFuture.complete(audioBuffer.toByteArray());
        }
    }
}


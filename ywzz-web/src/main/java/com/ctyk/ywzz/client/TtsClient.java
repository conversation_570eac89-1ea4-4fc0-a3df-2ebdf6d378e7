package com.ctyk.ywzz.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.common.json.utils.JsonUtils;
import com.ctyk.ywzz.config.TtsConfig;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.TtsInitResponseDTO;
import com.ctyk.ywzz.enums.TtsUriEnum;
import com.ctyk.ywzz.util.UserUtil;
import jakarta.websocket.ContainerProvider;
import jakarta.websocket.Session;
import jakarta.websocket.WebSocketContainer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.client
 * @author: zwj
 * @date: 2025/05/12  13:40
 * @description: 语音文本互转客户端
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TtsClient {
    
    private final TtsConfig ttsConfig;
    
    private static final String PARAM_TIME = "time";
    
    private static final String ERROR_CODE = "error_code";
    
    private static final String SPEECH_TO_TEXT_ERROR = "语音合成失败";
    
    /**
     * 音频文件转写-初始化
     *
     * @return {@link String }
     */
    public String appendUploadInit() {
        
        try {
            String url = ttsConfig.getHost() + TtsUriEnum.INIT.getUri();
            
            Map<String, String> params = new TreeMap<>();
            params.put(Constants.TTS_APP_KEY, ttsConfig.getAppKey());
            params.put(Constants.TTS_USER_ID, UserUtil.getUserIdOrDefault());
            params.put(Constants.TTS_TIMESTAMP, System.currentTimeMillis() + "");
            params.put(Constants.TTS_SIGNATURE, buildSign(ttsConfig.getAppSecret(), params));
            String reqUrl = buildReqUrl(url, params);
            byte[] data = httpPost(reqUrl, null, null);
            return new String(data);
        } catch (Exception e) {
            log.error("音频文件转写-初始化失败", e);
        }
        return null;
    }
    
    /**
     * 音频文件转写-追加上传音频
     *
     * @param fileBytes 文件字节
     * @param textId 文本 ID
     * @param format 格式
     * @param md5 md5
     * @return {@link String }
     */
    public String appendUpload(byte[] fileBytes, String textId, String format, String md5) {
        
        try {
            String url = ttsConfig.getHost() + TtsUriEnum.UPLOAD.getUri();
            Map<String, String> params = new TreeMap<>();
            params.put(Constants.TTS_APP_KEY, ttsConfig.getAppKey());
            params.put(Constants.TTS_USER_ID, UserUtil.getUserIdOrDefault());
            params.put(Constants.TTS_TEXT_ID, textId);
            params.put(Constants.TTS_MD5, md5);
            params.put(Constants.TTS_AUDIO_TYPE, format);
            params.put(Constants.TTS_TIMESTAMP, System.currentTimeMillis() + "");
            params.put(Constants.TTS_SIGNATURE, buildSign(ttsConfig.getAppSecret(), params));
            String reqUrl = buildReqUrl(url, params);
            byte[] data = httpPost(reqUrl, fileBytes, null);
            
            return new String(data);
        } catch (Exception e) {
            log.error("音频文件转写-追加上传音频失败", e);
        }
        
        return null;
    }
    
    /**
     * 音频文件转写-追加完成，开始转写
     *
     * @param textId 文本 ID
     * @param format 格式
     * @param domain 域
     * @param md5 md5
     * @return {@link String }
     */
    public String appendUploadFinish(String textId, String format, String domain, String md5) {
        
        try {
            String url = ttsConfig.getHost() + TtsUriEnum.TRANSCRIBE.getUri();
            
            Map<String, String> params = new TreeMap<>();
            
            params.put(Constants.TTS_APP_KEY, ttsConfig.getAppKey());
            params.put(Constants.TTS_USER_ID, UserUtil.getUserIdOrDefault());
            params.put(Constants.TTS_TEXT_ID, textId);
            params.put(Constants.TTS_AUDIO_TYPE, format);
            params.put(Constants.TTS_DOMAIN, domain);
            params.put(Constants.TTS_MD5, md5);
            params.put(Constants.TTS_PUNCTION, "beauty");
            params.put(Constants.TTS_NUM_CONVERT, "true");
            params.put(Constants.TTS_TIMESTAMP, System.currentTimeMillis() + "");
            params.put(Constants.TTS_SIGNATURE, buildSign(ttsConfig.getAppSecret(), params));
            
            String reqUrl = buildReqUrl(url, params);
            
            byte[] data = httpPost(reqUrl, null, null);
            
            return new String(data);
        } catch (Exception e) {
            log.error("音频文件转写-追加完成，开始转写失败", e);
        }
        
        return null;
    }
    
    /**
     * 音频文件转写-获取转写结果
     *
     * @param textId 文本 ID
     * @return {@link String }
     */
    public String getText(String textId) {
        
        String url = ttsConfig.getHost() + TtsUriEnum.TEXT.getUri();
        
        Map<String, String> params = new TreeMap<>();
        
        params.put(Constants.TTS_TEXT_ID, textId);
        params.put(Constants.TTS_APP_KEY, ttsConfig.getAppKey());
        params.put(Constants.TTS_TIMESTAMP, System.currentTimeMillis() + "");
        params.put(Constants.TTS_SIGNATURE, buildSign(ttsConfig.getAppSecret(), params));
        
        String reqUrl = buildReqUrl(url, params);
        
        byte[] ret = new byte[500];
        return new String(httpGet(reqUrl, ret));
    }
    
    /**
     * 短文本语音合成
     * @param text 要转换的文本
     * @return 音频数据的字节数组
     */
    public byte[] textToSpeech(String text) throws Exception {
        
        // 创建CompletableFuture用于接收结果
        CompletableFuture<byte[]> resultFuture = new CompletableFuture<>();
        
        long time = System.currentTimeMillis();
        String appKey = ttsConfig.getAppKey();
        String sign = getSign(String.valueOf(time));
        
        String param = "appkey=" + appKey + "&" + "time=" + time + "&" + "sign=" + sign;
        String str = ttsConfig.getWssHost() + param;
        log.info("【文本转语音接口鉴权地址】:{}", str);
        URI uri = new URI(str);
        
        WebSocketContainer container = ContainerProvider.getWebSocketContainer();
        Session session = container.connectToServer(new TtsClientEndpoint(text, ttsConfig.getVcn(), resultFuture), uri);
        session.setMaxBinaryMessageBufferSize(1024 * 1024 * 10);
        
        try {
            // 等待结果，设置超时时间
            return resultFuture.get(60, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            // 超时处理
            if (session.isOpen()) {
                session.close();
            }
            throw new ServiceException("文本转语音请求超时");
        } catch (InterruptedException | ExecutionException e) {
            // 其他异常处理
            if (session.isOpen()) {
                session.close();
            }
            throw new ServiceException("文本转语音请求失败");
        }
    }
    
    /**
     * 短文本语音合成 -获取签名
     *
     * @param timestamp 时间戳
     * @return {@link String }
     */
    private String getSign(String timestamp) {
        
        String appKey = ttsConfig.getAppKey();
        String secret = ttsConfig.getAppSecret();
        String originalStr = appKey + timestamp + secret;
        StringBuilder sign = new StringBuilder();
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] bytes = md.digest(originalStr.getBytes(StandardCharsets.UTF_8));
            for (byte aByte : bytes) {
                String hex = Integer.toHexString(aByte & 0xFF);
                if (hex.length() == 1) {
                    sign.append("0");
                }
                sign.append(hex.toUpperCase());
            }
        } catch (Exception unused) {
            log.error("SHA-256加密失败");
        }
        return sign.toString();
    }
    
    /**
     * 音频文件转写-获取签名
     *
     * @param secret 秘密
     * @param params 参数
     * @return {@link String }
     */
    private String buildSign(String secret, Map<String, String> params) {
        
        StringBuilder signStr = new StringBuilder(secret);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!Constants.TTS_SIGNATURE.equals(entry.getKey())) {
                signStr.append(entry.getValue());
            }
        }
        
        signStr.append(secret);
        return generateSHA1Hash(signStr.toString()).toUpperCase();
        
    }
    
    /**
     * SHA1加密
     *
     * @param input 明文
     * @return {@link String }
     */
    private String generateSHA1Hash(String input) {
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(input.getBytes(StandardCharsets.UTF_8));
            byte[] messageDigest = digest.digest();
            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            // 字节数组转换为 十六进制 数
            for (byte b : messageDigest) {
                String shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();
            
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 构建请求URL
     *
     * @param url 网址
     * @param params 参数
     * @return {@link String }
     */
    private String buildReqUrl(String url, Map<String, String> params) {
        
        StringBuilder ret = new StringBuilder(url);
        
        // 检查URL是否已包含参数
        boolean hasQueryParams = url.contains("?");
        
        // 添加问号或&作为第一个参数的前缀
        if (!params.isEmpty()) {
            ret.append(hasQueryParams ? '&' : '?');
        }
        
        // 使用entrySet迭代，避免重复查找
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (first) {
                first = false;
            } else {
                ret.append('&');
            }
            ret.append(entry.getKey())
               .append('=')
               .append(entry.getValue());
        }
        
        return ret.toString();
    }
    
    /**
     * 发送POST请求
     *
     * @param url 网址
     * @param sdata SDATA
     * @param requestParam 请求参数
     * @return {@link byte[] }
     */
    private byte[] httpPost(String url, byte[] sdata, Map<String, Object> requestParam) {
        
        try {
            if (url == null || url.isEmpty()) {
                return new byte[0];
            }
            
            HttpRequest request = HttpRequest.post(url)
                                             .timeout(30000)
                                             .contentType("binary")
                                             .body(sdata);
            
            // 添加 header 参数
            if (requestParam != null && !requestParam.isEmpty()) {
                requestParam.forEach((key, value) -> request.header(key, String.valueOf(value)));
            }
            
            try (HttpResponse response = request.execute()) {
                // 直接返回 byte[]
                return response.bodyBytes();
            }
            
        } catch (Exception e) {
            log.error("tts发送POST请求出现异常:{}", e.getMessage(), e);
        }
        return new byte[0];
    }
    
    /**
     * 发送GET请求
     *
     * @param url 网址
     * @param sdata SDATA
     * @return {@link byte[] }
     */
    public byte[] httpGet(String url, byte[] sdata) {
        
        try {
            if (url == null || url.isEmpty()) {
                return new byte[0];
            }
            
            HttpRequest request = HttpRequest.get(url)
                                             .timeout(60000);
            
            // 注意：GET 请求通常不推荐带 body，某些服务端可能不支持
            if (sdata != null) {
                request.body(sdata);
            }
            
            try (HttpResponse response = request.execute()) {
                return response.bodyBytes();
            }
            
        } catch (Exception e) {
            log.error("tts发送GET请求出现异常:{}", e.getMessage(), e);
        }
        
        return new byte[0];
    }
    
    /**
     * 长文本转语音
     *
     * @param text 文本
     * @return {@link byte[] }
     */
    public byte[] longTextToSpeech(String text) {
        String host = ttsConfig.getLttsHost();
        
        // 1. 开始合成任务
        String startResult = longTextToSpeechStart(host, text);
        log.info("【长文本合成语音-开始合成】响应:{}", startResult);
        TtsInitResponseDTO startResultDto = JsonUtils.parseObject(startResult, TtsInitResponseDTO.class);
        Integer startErrorCode = startResultDto.getErrorCode();
        if (startErrorCode != null && startErrorCode != 0) {
            log.error("【长文本合成语音-开始合成】响应:{}", startResultDto.getMessage());
            throw new ServiceException(SPEECH_TO_TEXT_ERROR);
        }
        // 2. 使用CompletableFuture和ScheduledExecutorService异步等待结果
        CompletableFuture<byte[]> resultFuture = new CompletableFuture<>();
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "tts-progress-checker");
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        });
        
        // 3. 创建任务状态检查器
        AtomicInteger attemptCount = new AtomicInteger(0);
        Runnable progressChecker = () -> {
            try {
                // 获取任务进度
                String result = getProgress(host, startResultDto.getTaskId());
                int attempts = attemptCount.incrementAndGet();
                log.info("【长文本合成语音-获取合成状态】第{}次检查，响应:{}", attempts, result);
                JSONObject jo = JSON.parseObject(result);
                
                // 判断任务是否完成
                if (isEnd(jo)) {
                    // 下载语音文件
                    String audioAddress = jo.getString("audio_address");
                    byte[] audioData = HttpUtil.downloadBytes(audioAddress);
                    resultFuture.complete(audioData);
                    scheduler.shutdown();
                }
            } catch (Exception e) {
                log.error("【长文本合成语音-获取合成状态】出现异常:{}", e.getMessage(), e);
                resultFuture.completeExceptionally(e);
                scheduler.shutdown();
            }
        };
        
        // 4. 定期执行检查任务
        // 立即开始第一次检查
        scheduler.scheduleAtFixedRate(progressChecker, 0, ttsConfig.getRetryInterval(), TimeUnit.MILLISECONDS);
        
        try {
            // 5. 等待结果，设置最大等待时间
            long maxWaitTime = ttsConfig.getMaxWaitTime() != null ? ttsConfig.getMaxWaitTime() : 300000;
            return resultFuture.get(maxWaitTime, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread()
                  .interrupt();
            log.error("【长文本合成语音】等待过程被中断", e);
            throw new ServiceException(SPEECH_TO_TEXT_ERROR);
        } catch (ExecutionException e) {
            log.error("【长文本合成语音】执行异常", e.getCause());
            throw new ServiceException(SPEECH_TO_TEXT_ERROR);
        } catch (TimeoutException e) {
            log.error("【长文本合成语音】等待超时", e);
            throw new ServiceException(SPEECH_TO_TEXT_ERROR);
        } finally {
            // 确保线程池关闭
            if (!scheduler.isShutdown()) {
                scheduler.shutdownNow();
            }
        }
    }
    
    /**
     * 长文本语音合成-查询合成结果
     *
     * @param host 主机
     * @param taskId 任务 ID
     * @return {@link String }
     */
    private String getProgress(String host, String taskId) {
        
        String url = host + "/progress?";
        
        String time = System.currentTimeMillis() + "";
        String sign = getSign(time);
        Map<String, String> params = new TreeMap<>();
        params.put(Constants.TTS_TEXT_ID, taskId);
        params.put(Constants.TTS_APP_KEY, ttsConfig.getAppKey());
        params.put(PARAM_TIME, time);
        params.put(Constants.SIGN, sign);
        return HttpUtil.post(url, JSON.toJSONString(params));
    }
    
    /**
     * 长文本语音合成-合成是否结束
     *
     * @param jo 乔
     * @return boolean
     */
    private boolean isEnd(JSONObject jo) {
        
        if (null == jo) {
            return false;
        }
        String status = jo.getString("task_status");
        
        return "done".equals(status);
    }
    
    /**
     * 长文本语音合成-开始合成
     *
     * @param host 主机
     * @param text 发短信
     * @return {@link String }
     */
    private String longTextToSpeechStart(String host, String text) {
        
        try {
            String url = host + "/start?";
            String time = System.currentTimeMillis() + "";
            String appKey = ttsConfig.getAppKey();
            String sign = getSign(time);
            Map<String, Object> params = new TreeMap<>();
            params.put(Constants.TTS_APP_KEY, appKey);
            params.put(PARAM_TIME, time);
            params.put(Constants.SIGN, sign);
            params.put(Constants.TTS_USER_ID, UserUtil.getUserIdOrDefault());
            params.put(Constants.TTS_FORMAT_NAME, Constants.TTS_FORMAT_VALUE);
            params.put(Constants.TTS_VCN_NAME, ttsConfig.getVcn());
            params.put(Constants.TTS_SAMPLE_NAME, 16000);
            params.put(Constants.TTS_TEXT_NAME, text);
            
            return HttpUtil.post(url, JSON.toJSONString(params));
            
        } catch (Exception e) {
            log.error("长文本语音合成出现异常:{}", e.getMessage(), e);
        }
        
        return null;
    }
}

package com.ctyk.ywzz;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz
 * @author: zwj
 * @date: 2025/01/09  11:28
 * @description: 翼问智诊启动类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.ctyk.common.system.api.client"})
public class YwzzApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(YwzzApplication.class, args);
        
    }
    
}

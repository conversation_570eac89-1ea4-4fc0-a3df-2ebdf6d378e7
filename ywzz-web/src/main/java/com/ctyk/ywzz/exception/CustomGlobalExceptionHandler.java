package com.ctyk.ywzz.exception;

import com.ctyk.common.core.domain.R;
import com.ctyk.common.web.handler.GlobalExceptionHandler;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.exception
 * @author: zwj
 * @date: 2025/02/18  16:06
 * @description: 全局异常处理
 *               引入依赖中已经有全局异常处理{@link com.ctyk.common.web.handler.GlobalExceptionHandler}
 *               但是不满足，这里自定义覆盖之前的处理
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestControllerAdvice
public class CustomGlobalExceptionHandler extends GlobalExceptionHandler {
    
    @Value("${spring.servlet.multipart.max-file-size}")
    private String maxFileSize;
    /**
     * 处理运行时异常
     *
     * @param e 异常
     * @param request 请求资源
     * @return {@link R }<{@link Void }>
     */
    @Override
    @ExceptionHandler({RuntimeException.class})
    public R<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        
        String requestUri = request.getRequestURI();
        log.error("自定义异常处理器 - 请求地址'{}',发生运行时异常:{}", requestUri, e.getMessage(), e);
        return R.fail("系统繁忙，请稍后再试");
    }
    
    /**
     * 处理异常
     *
     * @param e 异常
     * @param request 请求资源
     * @return {@link R }<{@link Void }>
     */
    @Override
    @ExceptionHandler({Exception.class})
    public R<Void> handleException(Exception e, HttpServletRequest request) {
        
        String requestUri = request.getRequestURI();
        log.error("自定义异常处理器 - 请求地址'{}',发生系统异常:{}", requestUri, e.getMessage(), e);
        return R.fail("系统繁忙，请稍后再试");
    }
    
    /**
     * 上传大小超过异常
     *
     * @param e e
     * @param request 请求
     * @return {@link R }<{@link Void }>
     */
    @ExceptionHandler({MaxUploadSizeExceededException.class})
    public R<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        
        String requestUri = request.getRequestURI();
        log.error("自定义异常处理器 - 请求地址'{}',发生系统异常:{}", requestUri, e.getMessage(), e);
        return R.fail("上传文件过大,请上传小于" + maxFileSize + "的文件");
    }
    
}

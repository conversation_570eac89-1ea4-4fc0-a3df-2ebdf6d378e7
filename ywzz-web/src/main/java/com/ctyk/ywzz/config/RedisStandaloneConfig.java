package com.ctyk.ywzz.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: redis单机配置
 * @date 2021/4/9 14:58
 */
@Configuration
@ConditionalOnProperty(value = "redis.type.standalone")
public class RedisStandaloneConfig {

    @Value("${spring.data.redis.host}")
    private String host;

    @Value("${spring.data.redis.port}")
    private Integer port;

    @Value("${spring.data.redis.database}")
    private Integer database;

    @Value("${spring.data.redis.password}")
    private String redisPassword;

    /**
     * redis单机配置加载
     * @return
     */
    @Bean
    public RedisStandaloneConfiguration redisStandaloneConfiguration() {
        RedisStandaloneConfiguration redisStandalone = new RedisStandaloneConfiguration();
        redisStandalone.setHostName(host);
        redisStandalone.setDatabase(database);
        redisStandalone.setPassword(RedisPassword.of(redisPassword));
        redisStandalone.setPort(port);
        return redisStandalone;
    }
}

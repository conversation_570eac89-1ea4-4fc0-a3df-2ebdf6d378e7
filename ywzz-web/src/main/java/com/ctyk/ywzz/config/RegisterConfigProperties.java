package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/03/14  09:00
 * @description: 挂号链接配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "register")
public class RegisterConfigProperties {
    
    private String baseUrl;
    
    private String urlTemplate;
    
    private Map<String, String> defaultParams = new HashMap<>();
    
    private Map<String, String> encoding = new HashMap<>();
    
    private String urlTemplateEncode;
    
    private String baseUrlEncode;
}

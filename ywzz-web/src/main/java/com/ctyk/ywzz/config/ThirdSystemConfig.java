package com.ctyk.ywzz.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: <PERSON>yh
 * @date: 2024/10/21 09:05
 * @description: 第三方系统配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "third-system")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThirdSystemConfig {

    /**
     * 系统编码
     */
    private String appCode;

    /**
     * 私钥
     */
    private String secretKey;
}

package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/05/12  13:41
 * @description: 语音文本互转配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tts.yzs")
public class TtsConfig {
    
    private String host;
    
    private String appKey;
    
    private String appSecret;
    
    private String wssHost;
    
    private String vcn;
    
    private Long textToSpeechTimeout;
    
    private Long retryTimes;
    
    private Long retryInterval;
    
    private String emptyText;
    
    private String lttsHost;
    private Long maxWaitTime;
}

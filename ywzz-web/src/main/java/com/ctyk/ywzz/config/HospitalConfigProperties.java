package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/03/14  09:00
 * @description: 医院相关配置
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "hospital")
public class HospitalConfigProperties {
    
    
    /**
     * 挂号链接配置
     */
    private Register register;
    
    private Tip tip;
    
    private API api;
    
    /**
     * 接口相关配置
     *
     * <AUTHOR>
     * @date 2025-03-18
     */
    @Data
    public static class API {
        
        private String hospitalId;
        
        private Boolean sourceEnable;
        
        /**
         * 线下挂号科室编码
         */
        private String offlineDeptCode;
        
        /**
         *预约挂号科室编码
         */
        private String bookDeptCode;
        
        /**
         * 未来n日
         */
        private Integer futureDay;
        
        private String scheduleInfoUrl;
        
        private String visitInfoUrl;
        
        private String reportUrl;
        
        private String deptUrl;
        
        private String objectSystem;
        
        private String sourceSystem;
        
        private String accessKey;
        
        private Action action;
        
        private String version;
        
        private String code;
    }
    @Data
    public static class Action {
        
        private String deptInfo;
        private String doctorScheduling;
        private String medicalInstitution;
    }
    /**
     * 提示信息配置
     *
     * <AUTHOR>
     * @date 2025-03-18
     */
    @Data
    public static class Tip {
        
        private String message;
        
        private String specialChars;
        
        private String knowledgeMessage;
    }
    
    /**
     * 挂号链接配置
     *
     * <AUTHOR>
     * @date 2025-03-18
     */
    @Data
    public static class Register {
        
        private String baseUrl;
        
        private String urlTemplate;
        
        private Map<String, String> defaultParams = new HashMap<>();
        
        private Map<String, String> encoding = new HashMap<>();
        
        private String urlTemplateEncode;
        
        private String baseUrlEncode;
    }
}

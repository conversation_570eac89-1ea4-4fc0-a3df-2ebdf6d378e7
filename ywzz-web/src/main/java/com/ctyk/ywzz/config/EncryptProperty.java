package com.ctyk.ywzz.config;


import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.service.impl.RedisService;
import com.ctyk.ywzz.util.Sm2Util;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.security.InvalidAlgorithmParameterException;
import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;

/**
 * @project: zhyy-cloud-igs
 * @package: com.ctyk.gateway.config.property
 * @author: wangjm
 * @date: 2024/07/18 15:53
 * @description: 加密配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "spring.encrypt")
public class EncryptProperty {
    
    @Resource
    private RedisService redisService;
    
    @Value("${spring.encrypt.enabled:false}")
    private boolean flag;
    
    /**
     * 表示Redis键的有效时间阈值,单位：秒
     */
    private static final int REDIS_KEY_EXPIRE_THRESHOLD = 2;
    
    @PostConstruct
    public void init() {
        if (!flag) {
            return;
        }
        // 初始化配置
        log.info("EncryptProperty init");
        if (redisService.hasKey(Constants.SERVER_PRIVATE_KEY)
                && redisService.getExpire(Constants.SERVER_PRIVATE_KEY) > REDIS_KEY_EXPIRE_THRESHOLD) {
            this.sm2ServerPrivateKey = (redisService.getString(Constants.SERVER_PRIVATE_KEY));
            this.sm2ServerPublicKey = (redisService.getString(Constants.SERVER_PUBLIC_KEY));
            this.sm2ClientPrivateKey = (redisService.getString(Constants.CLIENT_PRIVATE_KEY));
            this.sm2ClientPublicKey = (redisService.getString(Constants.CLIENT_PUBLIC_KEY));
            return;
        }
        fresh();
    }
    
    public void fresh() {
        KeyPair clientKeyPair;
        KeyPair serverKeyPair;
        try {
            clientKeyPair = Sm2Util.generateSm2KeyPair();
            serverKeyPair = Sm2Util.generateSm2KeyPair();
        } catch (NoSuchAlgorithmException | InvalidAlgorithmParameterException e) {
            throw new RuntimeException(e);
        }
        this.sm2ServerPrivateKey = (Sm2Util.getPriKeyHexString(serverKeyPair.getPrivate()));
        this.sm2ClientPrivateKey = (Sm2Util.getPriKeyHexString(clientKeyPair.getPrivate()));
        this.sm2ServerPublicKey = (Sm2Util.getPubKeyHexString(serverKeyPair.getPublic()));
        this.sm2ClientPublicKey = (Sm2Util.getPubKeyHexString(clientKeyPair.getPublic()));
        this.createTime = System.currentTimeMillis();
        // 	考虑分布式 存入redis
        redisService.setEx(Constants.SERVER_PRIVATE_KEY, this.sm2ServerPrivateKey, this.expire);
        redisService.setEx(Constants.CLIENT_PRIVATE_KEY, this.sm2ClientPrivateKey, this.expire);
        redisService.setEx(Constants.SERVER_PUBLIC_KEY, this.sm2ServerPublicKey, this.expire);
        redisService.setEx(Constants.CLIENT_PUBLIC_KEY, this.sm2ClientPublicKey, this.expire);
    }
    
    /**
     * 是否开启
     */
    private Boolean enabled = false;
    
    /**
     * SM2 服务端 私钥
     */
    private String sm2ServerPrivateKey;
    
    /**
     * SM2 客户端 私钥
     */
    private String sm2ClientPrivateKey;
    
    /**
     * SM2 服务端 公钥
     */
    private String sm2ServerPublicKey;
    
    /**
     * SM2 客户端 公钥
     */
    private String sm2ClientPublicKey;
    
    /**
     * 到期时间
     */
    private Long expire;
    
    /**
     * 秘钥生成时间
     */
    private Long createTime;
}

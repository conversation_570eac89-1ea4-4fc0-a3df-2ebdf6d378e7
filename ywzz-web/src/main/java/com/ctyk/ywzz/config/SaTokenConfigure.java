package com.ctyk.ywzz.config;


import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @project: zhyy-cloud-igs
 * @package: com.ctyk.ywzz.config
 * @author: wangjm
 * @date: 2024/07/05 10:09
 * @description: sa-token 全局过滤器
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {
    
    private static final Logger log = LoggerFactory.getLogger(SaTokenConfigure.class);
    
    @Resource
    private UrlConfig urlConfig;
    
    @Resource
    private ThirdSystemConfig thirdSystemConfig;
    
    private static final String DEV_APP_KEY = "YWZZ";
    
    /**
     * 注册 [Sa-Token 全局过滤器]
     */
    @Bean
    public SaServletFilter getSaServletFilter() {
        
        return new SaServletFilter()
                
                // 指定 [拦截路由]
                .setIncludeList(urlConfig.getInclude())
                // 指定放行路由
                .setExcludeList(urlConfig.getExclude())
                // 认证函数: 每次请求执行
                .setAuth(obj -> {
                    log.info("---------- 登录系统编码 {}", thirdSystemConfig.getAppCode());
                    if (DEV_APP_KEY.equals(thirdSystemConfig.getAppCode())) {
                        return;
                    }
                    
                    log.info("---------- sa全局认证 {}", SaHolder.getRequest().getRequestPath());
                    SaRouter.match("/**", "/api/users/token", r -> StpUtil.checkLogin());
                })
                // 异常处理函数：每次认证函数发生异常时执行此函数
                .setError(e -> {
                    log.error("---------- sa全局异常{}", e.getMessage());
                    return SaResult.error("请退出系统，重新授权");
                })
                // 前置函数：在每次认证函数之前执行（BeforeAuth 不受 includeList 与 excludeList 的限制，所有请求都会进入）
                .setBeforeAuth(r ->
                        // ---------- 设置跨域响应头 ----------
                        SaHolder.getResponse()
                                // 允许指定域访问跨域资源
                                .setHeader("Access-Control-Allow-Origin", "*")
                                // 允许所有请求方式
                                .setHeader("Access-Control-Allow-Methods", "POST,GET,OPTIONS,DELETE,PUT")
                                // 有效时间
                                .setHeader("Access-Control-Max-Age", "3600")
                                // 允许的header参数
                                .setHeader("Access-Control-Allow-Headers", "*"));
    }
    
}
package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/05/15  15:20
 * @description: 伴随症状配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "large-model.accompany-symptoms")
public class AccompanySymptomsConfig {
    
    private Boolean enable;
    
    private String url;
    
}

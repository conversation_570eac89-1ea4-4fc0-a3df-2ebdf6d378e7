package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.config
 * @author: zwj
 * @date: 2025/07/10  10:17
 * @description: maas v2基础信息配置
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "large-model.v2-info")
public class MaasV2Config {
    
    private String accessToken;
    
    private String apiKey;
    
    private String homeId;
    
    private String source;
    
    /**
     * 智能体url
     */
    private String agentUrl;
    
    /**
     * 知识库url
     */
    private String knowledgeUrl;
    
    /**
     * 模型服务URL
     */
    private String modelServerUrl;
    
    /**
     * 智慧分诊
     */
    private String smartTriageCode;
    
    /**
     * 智慧分诊-deepseek
     */
    private String smartTriageDsCode;
    
    /**
     * 意图识别
     */
    private String intentRecognizerCode;
    
    /**
     * 智能问药
     */
    private String smartMedicineCode;
    
    /**
     * 信息咨询
     */
    private String consultCode;
    
    /**
     * 思考模型编码
     */
    private String thinkModelCode;
    
    /**
     * 没有思考模型编码
     */
    private String noThinkModelCode;
}

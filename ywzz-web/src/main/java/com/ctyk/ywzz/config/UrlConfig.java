package com.ctyk.ywzz.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 定义路径的白名单和黑名单
 * @date 2021/11/26 16:06
 */
@Component
@ConfigurationProperties(prefix = "url")
public class UrlConfig {
    
    /**
     * 包含路径
     */
    private List<String> include;
    
    /**
     * 排除路径
     * 无需token校验
     */
    private List<String> exclude;

    private List<String> image;

    public List<String> getInclude() {
        return include;
    }

    public void setInclude(List<String> include) {
        this.include = include;
    }

    public List<String> getExclude() {
        return exclude;
    }

    public void setExclude(List<String> exclude) {
        this.exclude = exclude;
    }

    public List<String> getImage() {
        return image;
    }

    public void setImage(List<String> image) {
        this.image = image;
    }

}

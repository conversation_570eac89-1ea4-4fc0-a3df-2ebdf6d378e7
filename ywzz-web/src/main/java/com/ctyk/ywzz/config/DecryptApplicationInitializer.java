package com.ctyk.ywzz.config;

import com.ctyk.ywzz.util.Sm4Util;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;

import java.util.Properties;

/**
 * @project: ctyk-cloud
 * @package: com.ctyk.ywzz.config
 * @author: wangjm
 * @date: 2024/07/30 10:30
 * @description: 初始化时对配置文件部分的配置进行解密（nacos配置文件加载之后）
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class DecryptApplicationInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

	private static final String SECRET_KEY = "eb509ca65a703264";

	@Override
	public void initialize(ConfigurableApplicationContext applicationContext) {
		ConfigurableEnvironment environment = applicationContext.getEnvironment();
		MutablePropertySources propertySources = environment.getPropertySources();
		// 临时存储需要替换的配置
		Properties props = new Properties();
		for (PropertySource<?> propertySource : propertySources) {
			if (propertySource instanceof EnumerablePropertySource<?> enumerablePropertySource) {
				String[] propertyNames = enumerablePropertySource.getPropertyNames();
				// 遍历所有配置key:value
				for (String propertyName : propertyNames) {
					String propertyVal = environment.getProperty(propertyName);
					// 根据自己写的规则来解析那些配置是需要解密的
					if (propertyVal != null && propertyVal.startsWith("ENC(") && propertyVal.endsWith(")")) {
						// 解析得到加密的数据
						String encryptedValue = propertyVal.substring(4, propertyVal.length() - 1);
						// 调用自定义工具类解密
						String decryptedValue = null;
						try {
							decryptedValue = Sm4Util.decryptEcbBase64(Hex.toHexString(SECRET_KEY.getBytes()), encryptedValue, "UTF-8");
						} catch (Exception e) {
							throw new RuntimeException(e);
						}
						// 保存需要替换的配置
						props.put(propertyName, decryptedValue);
					}
				}
			}
		}
		// 添加解密后的属性到环境中
		if (!props.isEmpty()) {
			PropertiesPropertySource pps = new PropertiesPropertySource("decryptedProperties", props);
			environment.getPropertySources().addFirst(pps);
		}
	}
}

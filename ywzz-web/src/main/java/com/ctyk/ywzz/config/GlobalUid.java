package com.ctyk.ywzz.config;

import com.baidu.fsg.uid.UidGenerator;
import com.ctyk.uid.UidGeneratorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-mia
 * @package: com.ctyk.mia.config
 * @author: zwj
 * @date: 2024/08/08  20:21
 * @description: 全局id
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Configuration
public class GlobalUid {
    
    @Bean("cachedUidGenerator")
    public UidGenerator globalUid() {
        return UidGeneratorBuilder.commonGenerator(127);
    }
}

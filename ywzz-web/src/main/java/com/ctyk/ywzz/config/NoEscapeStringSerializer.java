package com.ctyk.ywzz.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/06/25  09:54
 * @description: 自定义不转义String序列化器
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public class NoEscapeStringSerializer extends JsonSerializer<String>{
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeRawValue("\"" + value + "\"");
    }

}

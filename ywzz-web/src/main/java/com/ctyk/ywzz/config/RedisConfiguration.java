package com.ctyk.ywzz.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;


/**
 * @project: zhyy-cloud-igs
 * @package: com.ctyk.redis.config
 * @author: wangjm
 * @date: 2024/07/04 11:09
 * @description: redis配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Configuration
@Slf4j
public class RedisConfiguration {
	@Bean
	public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
		RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
		// 设置redis连接工厂对象
		redisTemplate.setConnectionFactory(redisConnectionFactory);
		// 设置 redis key 的序列化器，可以解决乱码问题
		redisTemplate.setKeySerializer(new StringRedisSerializer());
		// 设置 redis 值的序列化器，可以解决乱码问题
		redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));
		return redisTemplate;
	}
}

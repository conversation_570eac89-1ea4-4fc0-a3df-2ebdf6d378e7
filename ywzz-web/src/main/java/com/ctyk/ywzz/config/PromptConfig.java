package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/02/17  10:17
 * @description: 提示词配置
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "prompt-file-path")
public class PromptConfig {
    
    private String assistant;
    
    @Value("${prompt-file-path.assistant}")
    private Resource assistantPrompt;
    
    @Value("${prompt-file-path.assistant-ds}")
    private Resource assistantPromptDs;
    
    @Bean
    public String assistantPromptContent() throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(assistantPrompt.getInputStream(), StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining("\n"));
        }
    }
    
    @Bean
    public String assistantPromptDsContent() throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(assistantPromptDs.getInputStream(), StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining("\n"));
        }
    }
}

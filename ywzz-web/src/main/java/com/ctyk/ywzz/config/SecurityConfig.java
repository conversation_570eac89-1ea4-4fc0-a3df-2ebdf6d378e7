package com.ctyk.ywzz.config;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/04/16  11:10
 * @description: SecurityConfig配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
/*
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    */
/**
     * 安全过滤器链
     * @description: 由于项目中已经使用sa-token认证，但是license又依赖security，所以禁用security认证
     * @param http http
     * @return {@link org.springframework.security.web.SecurityFilterChain }
     * @throws Exception 异常
     *//*

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 配置所有请求均不需要认证
        http.authorizeHttpRequests(authorize -> authorize.anyRequest().permitAll())
                .csrf(AbstractHttpConfigurer::disable)
                // 禁用session创建
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        
        return http.build();
    }
}*/

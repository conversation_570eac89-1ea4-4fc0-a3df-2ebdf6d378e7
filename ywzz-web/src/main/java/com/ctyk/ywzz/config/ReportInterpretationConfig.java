package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/03/21  08:47
 * @description: 报告解读配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "large-model.report-interpretation")
public class ReportInterpretationConfig {
    
    private String uploadUrl;
    private Integer timeout;
    private Long cacheTime;

}

package com.ctyk.ywzz.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.config
 * @author: zwj
 * @date: 2025/06/05  09:07
 * @description: ai人工智能公司语言相关配置类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tts.ai")
public class AiTtsConfig {
    
    @Value("${tts.ai.protocol:wss://}")
    private String protocol;
    
    private String baseUrl;
    
    private String id;
    
    private String key;
    
    private String originName;
    
    private String region;
    
    private Long waitTime;
    
    private Long expiration;
    
    private String voice;
}

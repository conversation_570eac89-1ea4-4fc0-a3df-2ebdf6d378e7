package com.ctyk.ywzz.util;

import com.ctyk.ywzz.core.Constants;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: zwj
 * @date: 2025/05/12  15:10
 * @description: 用户工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Component
public class UserUtil {
    
    /**
     * 获取用户ID或默认用户ID
     *
     * @return {@link String }
     */
    public static String getUserIdOrDefault() {
        
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(
                RequestContextHolder.getRequestAttributes())).getRequest();
        String userId = request.getHeader(Constants.USER_ID);
        userId = ObjectUtils.isEmpty(userId) ? Constants.DEFAULT_OPENID : userId;
        return userId;
    }
}

package com.ctyk.ywzz.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.DigestUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: md5加密工具类
 * @date 2021/11/26 16:10
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Md5Util {
    
    /**
     * 获取MD5加密后的字符串
     *
     * @description: 首先对apiPath、accessToken、homeId、时间戳（毫秒）排序，再将排序后的字符使用“-”连接并MD5加密
     * @param data 数据
     * @return {@link String }
     */
    public static String getSignMd5(String... data) {
        
        Arrays.sort(data);
        String join = String.join("-", data);
        return DigestUtils.md5DigestAsHex(join.getBytes());
    }
    
}

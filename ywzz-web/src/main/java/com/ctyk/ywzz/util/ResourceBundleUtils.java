package com.ctyk.ywzz.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Locale;
import java.util.ResourceBundle;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.core.util
 * @author: Liyh
 * @date: 2024/11/23 10:45
 * @description: 资源文件工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResourceBundleUtils {
    
    private static final String BASE_NAME = "messages/shc";
    
    private static final String KEY_TIP_PREFIX = "error.tip.";
    
    /**
     * 获取全路径下的值
     *
     * @param key key
     * @return Object
     * <AUTHOR>
     * @date 2024/3/21 17:24
     */
    public static Object getResourceBundleValue(String key) {
        // 指定要读取的资源文件名和Locale
        // 使用默认Locale，也可以指定特定的Locale
        Locale locale = Locale.ROOT;
        // 加载资源文件
        ResourceBundle bundle = ResourceBundle.getBundle(BASE_NAME, locale);
        // 读取配置文件中的常量值
        try {
            return bundle.getString(key);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取tip路径下的值
     *
     * @param key key
     * @return String
     * <AUTHOR>
     * @date 2024/3/21 17:24
     */
    public static String getResourceBundleTipValue(String key) {
        // 指定要读取的资源文件名和Locale
        // 使用默认Locale，也可以指定特定的Locale
        Locale locale = Locale.ROOT;
        // 加载资源文件
        ResourceBundle bundle = ResourceBundle.getBundle(BASE_NAME, locale);
        // 读取配置文件中的常量值
        key = KEY_TIP_PREFIX + key;
        try {
            return bundle.getString(key);
        } catch (Exception e) {
            return null;
        }
    }
    
}

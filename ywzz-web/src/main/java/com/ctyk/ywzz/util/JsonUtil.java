package com.ctyk.ywzz.util;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * json工具类
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JsonUtil {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(
                    DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).registerModule(
                    new JavaTimeModule().addSerializer(LocalDateTime.class,
                                    new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)))
                            .addSerializer(LocalDate.class,
                                    new LocalDateSerializer(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)))
                            .addSerializer(LocalTime.class,
                                    new LocalTimeSerializer(DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN))));
    
    /**
     * 对象转字符串
     *
     * @param object 代转数据
     * @return 字符串
     */
    public static String toJsonString(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转json出现异常:{}", e.getMessage(), e);
            // 由于调用方没有处理异常，所以这里返回null
            return null;
        }
    }
    
    /**
     * 字符串转对象
     *
     * @param json json字符串
     * @return 对象
     */
    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            log.error("json字符串转对象出现异常:{}", e.getMessage(), e);
            return null;
        }
    }
    
    
    /**
     * 字符串转ObjectNode (ObjectNode类似 fastjson 的 JSONObject)
     *
     * @param json 待转字符串
     * @return ObjectNode
     */
    public static ObjectNode parseObject(String json) throws JsonProcessingException {
        
        return (ObjectNode) OBJECT_MAPPER.readTree(json);
        
    }
    
    /**
     * 字符串转对象列表
     *
     * @param json 代转json字符串
     * @param clazz 目标对象
     * @param <T> 目标对象类型
     * @return 对象集合
     */
    public static <T> List<T> parseArray(String json, Class<T> clazz) throws JsonProcessingException {
        
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
        return OBJECT_MAPPER.readValue(json, javaType);
        
    }
    
    /**
     * 字符串转ArrayNode ,  (ArrayNode类似 fastjson 的 JSONArray)
     *
     * @param json 待转json字符串
     * @return ArrayNode
     */
    public static ArrayNode parseArray(String json) throws JsonProcessingException {
        
        return (ArrayNode) OBJECT_MAPPER.readTree(json);
    }
    
    /**
     * 构建ObjectNode
     *
     * @return com.fasterxml.jackson.databind.node.ObjectNode
     */
    public static ObjectNode createObjectNode() {
        return OBJECT_MAPPER.createObjectNode();
    }
    
}

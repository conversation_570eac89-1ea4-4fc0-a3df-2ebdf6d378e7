package com.ctyk.ywzz.util;

import com.ctyk.ywzz.enums.WebRequestCodeEnum;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.util
 * @author: Liyh
 * @date: 2024/08/06 16:25
 * @description: WebService接口调用工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WebServiceClientUtil {
    private static final JaxWsDynamicClientFactory DCF = JaxWsDynamicClientFactory.newInstance();

    public static String callWebservice(String webUrl, String requestCode, String requestXml) throws Exception {
        Object[] results;
        try (Client client = DCF.createClient(webUrl)) {
            // 如果需要认证，取消注释并适当配置
            results = client.invoke(WebRequestCodeEnum.find(requestCode).getMethodName(), requestCode, requestXml);
        }
        
        if (results != null && results.length > 0) {
            return results[0].toString();
        }
        // 或者抛出异常，取决于您的业务逻辑
        return null;
    }
}
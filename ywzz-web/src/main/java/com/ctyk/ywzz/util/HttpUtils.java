package com.ctyk.ywzz.util;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;


/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: Liyh
 * @date: 2024/12/09 14:56
 * @description: http请求
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HttpUtils {
    
    /**
     * https请求
     * <AUTHOR>
     * @date 2024/12/09 15:45
     * @param requestUrl  请求URL
     * @param requestType 请求类型
     * @param requestParam 请求参数
     * @return JsonNode
     */
    public static JsonNode httpsRequest(String requestUrl, String requestType, String requestParam) {
        JsonNode jsonNode = null;
        log.info("请求url:{}, 请求类型:{}, 请求参数:{}", requestUrl, requestType, requestParam);
        try {
            // 创建SSLContext对象，并使用我们指定的信任管理器初始化
            TrustManager[] tm = {new MyX509TrustManagerUtil()};
            SSLContext sslContext = SSLContext.getInstance("TLS", "SunJSSE");
            sslContext.init(null, tm, new java.security.SecureRandom());
            // 从上述SSLContext对象中得到SSLSocketFactory对象
            SSLSocketFactory ssf = sslContext.getSocketFactory();
            URL url = new URL(requestUrl);
            HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
            httpUrlConn.setSSLSocketFactory(ssf);
            /*允许输出*/
            httpUrlConn.setDoOutput(true);
            /*允许输入*/
            httpUrlConn.setDoInput(true);
            /*不允许缓存*/
            httpUrlConn.setUseCaches(false);
            // 设置请求方式（GET/POST）
            httpUrlConn.setRequestMethod(requestType);
            /*如果是get请求，明文连接*/
            if (HttpMethod.GET.toString().equalsIgnoreCase(requestType)) {
                httpUrlConn.connect();
            }
            if (StringUtil.isNotBlank(requestParam)) {
                try (OutputStream outputStream = httpUrlConn.getOutputStream();) {
                    outputStream.write(requestParam.getBytes(StandardCharsets.UTF_8));
                }
            }
            try (InputStream inputStream = httpUrlConn.getInputStream()) {
                ObjectMapper objectMapper = new ObjectMapper();
                jsonNode = objectMapper.readTree(inputStream);
                log.info("https请求成功,响应数据:{}", jsonNode);
            }
            httpUrlConn.disconnect();
        } catch (Exception e) {
            log.error("https请求失败,异常信息：{}", e.getMessage(), e);
        }
        return jsonNode;
    }
    
    
}

package com.ctyk.ywzz.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: zwj
 * @date: 2025/06/05  16:00
 * @description: 音频格式转换工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class AudioConvertUtils {
    
    // 目标音频格式：16kHz, 16bit, 单声道
    private static final float SAMPLE_RATE = 16000.0f;
    private static final int SAMPLE_SIZE_IN_BITS = 16;
    private static final int CHANNELS = 1;
    private static final boolean SIGNED = true;
    private static final boolean BIG_ENDIAN = false;
    /**
     * 使用MP3SPI库进行转换
     */
    private static byte[] convertMp3ToPcmWithMp3Spi(byte[] mp3Data) throws Exception {
        try (ByteArrayInputStream mp3InputStream = new ByteArrayInputStream(mp3Data)) {
            // 获取音频输入流
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(mp3InputStream);

            // 获取原始音频格式
            AudioFormat sourceFormat = audioInputStream.getFormat();
            log.info("原始音频格式: {}", sourceFormat);

            // 定义目标PCM格式 (16kHz, 16bit, 单声道)
            AudioFormat targetFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,  // PCM编码
                    16000.0f,                         // 采样率 16kHz
                    16,                               // 位深度 16bit
                    1,                                // 单声道
                    2,                                // 帧大小 (16bit * 1声道 / 8 = 2字节)
                    16000.0f,                         // 帧率
                    false                             // 小端字节序
            );

            // 检查是否支持格式转换
            if (!AudioSystem.isConversionSupported(targetFormat, sourceFormat)) {
                log.warn("不支持直接转换，尝试中间格式转换");
                // 先转换为标准PCM格式
                AudioFormat intermediateFormat = new AudioFormat(
                        AudioFormat.Encoding.PCM_SIGNED,
                        sourceFormat.getSampleRate(),
                        16,
                        sourceFormat.getChannels(),
                        sourceFormat.getChannels() * 2,
                        sourceFormat.getSampleRate(),
                        false
                );

                if (AudioSystem.isConversionSupported(intermediateFormat, sourceFormat)) {
                    audioInputStream = AudioSystem.getAudioInputStream(intermediateFormat, audioInputStream);
                    sourceFormat = intermediateFormat;
                }
            }

            // 转换为目标格式
            if (AudioSystem.isConversionSupported(targetFormat, sourceFormat)) {
                audioInputStream = AudioSystem.getAudioInputStream(targetFormat, audioInputStream);
            }

            // 读取PCM数据
            ByteArrayOutputStream pcmOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = audioInputStream.read(buffer)) != -1) {
                pcmOutputStream.write(buffer, 0, bytesRead);
            }

            audioInputStream.close();
            byte[] pcmData = pcmOutputStream.toByteArray();

            log.info("MP3转PCM完成，原始大小: {} bytes, PCM大小: {} bytes", mp3Data.length, pcmData.length);

            return pcmData;

        } catch (UnsupportedAudioFileException e) {
            throw new Exception("MP3SPI不支持此音频格式: " + e.getMessage());
        }
    }
    
    /**
     * 将PCM数据保存到文件
     *
     * @param pcmData PCM数据
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void savePcmToFile(byte[] pcmData, String filePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(pcmData);
            log.info("PCM文件保存成功: {}", filePath);
        }
    }
    
    /**
     * WAV文件转PCM
     *
     * @param wavFile WAV文件
     * @return PCM数据
     * @throws Exception 转换异常
     */
    public static byte[] wavToPcm(MultipartFile wavFile) throws Exception {
        return wavToPcm(wavFile.getBytes());
    }
    
    /**
     * WAV字节数组转PCM
     *
     * @param wavData WAV数据
     * @return PCM数据
     * @throws Exception 转换异常
     */
    public static byte[] wavToPcm(byte[] wavData) throws Exception {
        log.info("开始WAV转PCM，输入数据大小: {} bytes", wavData.length);
        
        try (ByteArrayInputStream wavInputStream = new ByteArrayInputStream(wavData)) {
            // 获取音频输入流
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(wavInputStream);
            
            // 获取原始音频格式
            AudioFormat sourceFormat = audioInputStream.getFormat();
            log.info("原始WAV格式: 采样率={}Hz, 位深={}bit, 声道={}, 编码={}",
                    sourceFormat.getSampleRate(),
                    sourceFormat.getSampleSizeInBits(),
                    sourceFormat.getChannels(),
                    sourceFormat.getEncoding());
            
            // 定义目标PCM格式
            AudioFormat targetFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    SAMPLE_RATE,
                    SAMPLE_SIZE_IN_BITS,
                    CHANNELS,
                    CHANNELS * SAMPLE_SIZE_IN_BITS / 8, // frameSize
                    SAMPLE_RATE,
                    BIG_ENDIAN
            );
            
            // 转换音频格式
            AudioInputStream convertedStream = convertAudioFormat(audioInputStream, sourceFormat, targetFormat);
            
            // 读取PCM数据
            ByteArrayOutputStream pcmOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            
            while ((bytesRead = convertedStream.read(buffer)) != -1) {
                pcmOutputStream.write(buffer, 0, bytesRead);
            }
            
            convertedStream.close();
            audioInputStream.close();
            
            byte[] pcmData = pcmOutputStream.toByteArray();
            log.info("WAV转PCM完成，输出数据大小: {} bytes", pcmData.length);
            
            return pcmData;
            
        } catch (UnsupportedAudioFileException e) {
            log.error("不支持的WAV文件格式", e);
            throw new Exception("不支持的WAV文件格式: " + e.getMessage());
        } catch (IOException e) {
            log.error("WAV文件读取失败", e);
            throw new Exception("WAV文件读取失败: " + e.getMessage());
        }
    }
    
    /**
     * PCM转WAV
     *
     * @param pcmData PCM数据
     * @return WAV数据
     * @throws Exception 转换异常
     */
    public static byte[] pcmToWav(byte[] pcmData) throws Exception {
        log.info("开始PCM转WAV，输入数据大小: {} bytes", pcmData.length);
        
        // 创建音频格式
        AudioFormat audioFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                SAMPLE_RATE,
                SAMPLE_SIZE_IN_BITS,
                CHANNELS,
                CHANNELS * SAMPLE_SIZE_IN_BITS / 8, // frameSize
                SAMPLE_RATE,
                BIG_ENDIAN
        );
        
        // 创建音频输入流
        ByteArrayInputStream pcmInputStream = new ByteArrayInputStream(pcmData);
        AudioInputStream audioInputStream = new AudioInputStream(
                pcmInputStream,
                audioFormat,
                pcmData.length / audioFormat.getFrameSize()
        );
        
        // 转换为WAV格式
        ByteArrayOutputStream wavOutputStream = new ByteArrayOutputStream();
        
        try {
            AudioSystem.write(audioInputStream, AudioFileFormat.Type.WAVE, wavOutputStream);
            byte[] wavData = wavOutputStream.toByteArray();
            
            log.info("PCM转WAV完成，输出数据大小: {} bytes", wavData.length);
            return wavData;
            
        } catch (IOException e) {
            log.error("PCM转WAV失败", e);
            throw new Exception("PCM转WAV失败: " + e.getMessage());
        } finally {
            try {
                audioInputStream.close();
                wavOutputStream.close();
            } catch (IOException e) {
                log.warn("关闭流失败", e);
            }
        }
    }
    
    /**
     * 转换音频格式
     */
    private static AudioInputStream convertAudioFormat(AudioInputStream sourceStream,
            AudioFormat sourceFormat,
            AudioFormat targetFormat) throws Exception {
        // 如果格式已经匹配，直接返回
        if (sourceFormat.matches(targetFormat)) {
            log.info("音频格式已匹配，无需转换");
            return sourceStream;
        }
        
        // 检查是否支持直接转换
        if (AudioSystem.isConversionSupported(targetFormat, sourceFormat)) {
            log.info("支持直接格式转换");
            return AudioSystem.getAudioInputStream(targetFormat, sourceStream);
        }
        
        // 尝试分步转换
        log.info("尝试分步格式转换");
        
        // 第一步：转换编码格式
        AudioFormat intermediateFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sourceFormat.getSampleRate(),
                16, // 统一为16位
                sourceFormat.getChannels(),
                sourceFormat.getChannels() * 2, // 16位 = 2字节
                sourceFormat.getSampleRate(),
                false // 小端
        );
        
        AudioInputStream intermediateStream = sourceStream;
        if (AudioSystem.isConversionSupported(intermediateFormat, sourceFormat)) {
            intermediateStream = AudioSystem.getAudioInputStream(intermediateFormat, sourceStream);
            log.info("第一步转换完成：编码格式");
        }
        
        // 第二步：转换采样率和声道
        if (AudioSystem.isConversionSupported(targetFormat, intermediateStream.getFormat())) {
            AudioInputStream finalStream = AudioSystem.getAudioInputStream(targetFormat, intermediateStream);
            log.info("第二步转换完成：采样率和声道");
            return finalStream;
        }
        
        // 如果还是不支持，手动转换
        log.warn("使用手动转换方法");
        return manualConvert(intermediateStream, targetFormat);
    }
    
    /**
     * 手动转换音频格式
     */
    private static AudioInputStream manualConvert(AudioInputStream sourceStream, AudioFormat targetFormat) throws Exception {
        AudioFormat sourceFormat = sourceStream.getFormat();
        
        log.info("手动转换: {}Hz {}声道 -> {}Hz {}声道",
                sourceFormat.getSampleRate(), sourceFormat.getChannels(),
                targetFormat.getSampleRate(), targetFormat.getChannels());
        
        // 读取源数据
        ByteArrayOutputStream sourceData = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = sourceStream.read(buffer)) != -1) {
            sourceData.write(buffer, 0, bytesRead);
        }
        
        byte[] sourceBytes = sourceData.toByteArray();
        byte[] convertedBytes = convertSampleRateAndChannels(sourceBytes, sourceFormat, targetFormat);
        
        // 创建新的音频输入流
        ByteArrayInputStream convertedInputStream = new ByteArrayInputStream(convertedBytes);
        return new AudioInputStream(
                convertedInputStream,
                targetFormat,
                convertedBytes.length / targetFormat.getFrameSize()
        );
    }
    
    /**
     * 转换采样率和声道数
     */
    private static byte[] convertSampleRateAndChannels(byte[] sourceData, AudioFormat sourceFormat, AudioFormat targetFormat) {
        int sourceSampleRate = (int) sourceFormat.getSampleRate();
        int targetSampleRate = (int) targetFormat.getSampleRate();
        int sourceChannels = sourceFormat.getChannels();
        int targetChannels = targetFormat.getChannels();
        int bytesPerSample = sourceFormat.getSampleSizeInBits() / 8;
        
        // 计算转换比例
        double sampleRateRatio = (double) targetSampleRate / sourceSampleRate;
        
        // 计算输出样本数
        int sourceSamples = sourceData.length / (sourceChannels * bytesPerSample);
        int targetSamples = (int) (sourceSamples * sampleRateRatio);
        
        byte[] targetData = new byte[targetSamples * targetChannels * bytesPerSample];
        
        for (int i = 0; i < targetSamples; i++) {
            // 计算对应的源样本位置
            int sourceIndex = (int) (i / sampleRateRatio);
            if (sourceIndex >= sourceSamples) {
                sourceIndex = sourceSamples - 1;
            }
            
            // 读取源样本
            short[] sourceSample = new short[sourceChannels];
            for (int ch = 0; ch < sourceChannels; ch++) {
                int byteIndex = (sourceIndex * sourceChannels + ch) * bytesPerSample;
                if (byteIndex + 1 < sourceData.length) {
                    sourceSample[ch] = (short) ((sourceData[byteIndex] & 0xFF) |
                            ((sourceData[byteIndex + 1] & 0xFF) << 8));
                }
            }
            
            // 转换声道
            short targetSample;
            if (sourceChannels == 1 && targetChannels == 1) {
                // 单声道到单声道
                targetSample = sourceSample[0];
            } else if (sourceChannels == 2 && targetChannels == 1) {
                // 立体声到单声道（取平均值）
                targetSample = (short) ((sourceSample[0] + sourceSample[1]) / 2);
            } else if (sourceChannels == 1 && targetChannels == 2) {
                // 单声道到立体声（复制）
                targetSample = sourceSample[0];
            } else {
                // 其他情况，取第一个声道
                targetSample = sourceSample[0];
            }
            
            // 写入目标数据
            for (int ch = 0; ch < targetChannels; ch++) {
                int byteIndex = (i * targetChannels + ch) * bytesPerSample;
                if (byteIndex + 1 < targetData.length) {
                    targetData[byteIndex] = (byte) (targetSample & 0xFF);
                    targetData[byteIndex + 1] = (byte) ((targetSample >> 8) & 0xFF);
                }
            }
        }
        
        log.info("手动转换完成: {} samples -> {} samples", sourceSamples, targetSamples);
        return targetData;
    }
    
    /**
     * 检测音频文件格式
     *
     * @param audioFile 音频文件
     * @return 格式信息
     */
    public static String detectAudioFormat(MultipartFile audioFile) {
        try {
            byte[] data = audioFile.getBytes();
            return detectAudioFormat(data, audioFile.getOriginalFilename());
        } catch (IOException e) {
            log.error("读取文件失败", e);
            return "读取失败: " + e.getMessage();
        }
    }
    
    /**
     * 检测音频数据格式
     */
    public static String detectAudioFormat(byte[] data, String filename) {
        if (data.length < 12) {
            return "文件太小";
        }
        
        // 检查WAV格式
        if (data[0] == 'R' && data[1] == 'I' && data[2] == 'F' && data[3] == 'F' &&
                data[8] == 'W' && data[9] == 'A' && data[10] == 'V' && data[11] == 'E') {
            return "WAV格式";
        }
        
        // 根据文件扩展名判断
        if (filename != null) {
            String ext = filename.toLowerCase();
            if (ext.endsWith(".pcm")) {
                return "PCM格式（原始音频数据）";
            } else if (ext.endsWith(".wav")) {
                return "可能是WAV格式（但文件头不标准）";
            }
        }
        
        return "未知格式";
    }
    
    /**
     * 验证PCM数据
     *
     * @param pcmData PCM数据
     * @return 是否有效
     */
    public static boolean validatePcmData(byte[] pcmData) {
        if (pcmData == null || pcmData.length == 0) {
            return false;
        }
        
        // 检查长度是否为偶数（16位数据）
        if (pcmData.length % 2 != 0) {
            log.warn("PCM数据长度不是偶数: {}", pcmData.length);
            return false;
        }
        
        // 检查最小长度（至少0.1秒的音频）
        int minLength = (int) (SAMPLE_RATE * CHANNELS * SAMPLE_SIZE_IN_BITS / 8 * 0.1);
        if (pcmData.length < minLength) {
            log.warn("PCM数据太短: {} bytes, 最小需要: {} bytes", pcmData.length, minLength);
            return false;
        }
        
        // 检查最大长度（不超过60秒）
        int maxLength = (int) (SAMPLE_RATE * CHANNELS * SAMPLE_SIZE_IN_BITS / 8 * 60);
        if (pcmData.length > maxLength) {
            log.warn("PCM数据太长: {} bytes, 最大允许: {} bytes", pcmData.length, maxLength);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取音频时长（秒）
     *
     * @param pcmData PCM数据
     * @return 时长
     */
    public static double getAudioDuration(byte[] pcmData) {
        int bytesPerSecond = (int) (SAMPLE_RATE * CHANNELS * SAMPLE_SIZE_IN_BITS / 8);
        return (double) pcmData.length / bytesPerSecond;
    }
}

package com.ctyk.ywzz.util;

import com.ctyk.common.core.exception.ServiceException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.util
 * @author: Liyh
 * @date: 2024/10/14 09:26
 * @description: sha1加密工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Sha1Util {
    
    private static final Logger log = LoggerFactory.getLogger(Sha1Util.class);
    
    public static String sha1(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("sha1加密出现异常:{}", e.getMessage(), e);
            throw new ServiceException("SHA-1加密失败");
        }
    }
}

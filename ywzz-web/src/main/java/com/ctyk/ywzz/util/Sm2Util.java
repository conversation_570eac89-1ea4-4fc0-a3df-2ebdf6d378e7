package com.ctyk.ywzz.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.Strings;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.ECGenParameterSpec;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Sm2工具类
 * @date 2022/10/26 17:46
 */
@Slf4j
public class Sm2Util {
    
    /** 椭圆曲线 **/
    private static final String STD_NAME = "sm2p256v1";
    
    /** 加密算法 **/
    private static final String ALGORITHM_NAME = "EC";
    
    /**
     * 未压缩公钥前缀
     */
    private static final String UNCOMPRESSED_PUBLIC_KEY_PREFIX = "04";
    
    /**
     * 魔法值 "00"，用于处理私钥头部的前导零
     */
    private static final String MAGIC_ZERO = "00";
    
    /**
     * SM2 用户 ID
     */
    private static final String SM2_USER_ID = "1234567812345678";
    
    /**
     * 私钥头部前导零的长度
     */
    private static final int MAGIC_ZERO_LENGTH = 2;
    
    /**
     * 私钥长度（包括前导零）
     */
    private static final int PRIVATE_KEY_LENGTH_WITH_LEADING_ZERO = 66;
    
    /**
     * 公钥长度
     */
    private static final int PUBLIC_KEY_LENGTH = 130;
    
    /**
     * 生成 SM2 公私钥对
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidAlgorithmParameterException
     */
    public static KeyPair generateSm2KeyPair() throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
        final ECGenParameterSpec sm2Spec = new ECGenParameterSpec(STD_NAME);
        // 获取一个椭圆曲线类型的密钥对生成器
        final KeyPairGenerator kpg = KeyPairGenerator.getInstance(ALGORITHM_NAME, new BouncyCastleProvider());
        // 使用SM2参数初始化生成器
        kpg.initialize(sm2Spec);
        // 获取密钥对
        return kpg.generateKeyPair();
    }
    
    /**
     * 获取私钥（16进制字符串，头部不带00长度共64）
     * @param privateKey 私钥
     * @return
     */
    public static String getPriKeyHexString(PrivateKey privateKey) {
        BCECPrivateKey s = (BCECPrivateKey) privateKey;
        String priKeyHexString = Hex.toHexString(s.getD()
                                                  .toByteArray());
        if (priKeyHexString.length() == PRIVATE_KEY_LENGTH_WITH_LEADING_ZERO && MAGIC_ZERO.equals(
                priKeyHexString.substring(0, 2))) {
            return priKeyHexString.substring(MAGIC_ZERO_LENGTH);
        }
        return priKeyHexString;
    }
    
    /**
     * 获取公钥（16进制字符串，头部带04长度共130）
     * @param publicKey
     * @return
     */
    public static String getPubKeyHexString(PublicKey publicKey) {
        BCECPublicKey p = (BCECPublicKey) publicKey;
        return Hex.toHexString(p.getQ()
                                .getEncoded(false));
    }
    
    /**
     * SM2加密算法
     * @param publicKey 公钥
     * @param data      明文数据
     * @return
     */
    public String encrypt(PublicKey publicKey, String data) {
        BCECPublicKey p = (BCECPublicKey) publicKey;
        return encrypt(Hex.toHexString(p.getQ()
                                        .getEncoded(false)), data);
    }
    
    /**
     * SM2解密算法
     * @param privateKey  私钥（16进制字符串）
     * @param cipherData  密文数据
     * @return
     */
    public String decrypt(PrivateKey privateKey, String cipherData) {
        BCECPrivateKey s = (BCECPrivateKey) privateKey;
        return decrypt(Hex.toHexString(s.getD()
                                        .toByteArray()), cipherData);
    }
    
    /**
     * SM2加密算法
     * @param pubKeyHexString  公钥（16进制字符串）
     * @param data             明文数据
     * @return
     */
    public String encrypt(String pubKeyHexString, String data) {
        // 获取一条SM2曲线参数
        X9ECParameters sm2EcParameters = GMNamedCurves.getByName(STD_NAME);
        // 构造ECC算法参数，曲线方程、椭圆曲线G点、大整数N
        ECDomainParameters domainParameters = new ECDomainParameters(sm2EcParameters.getCurve(), sm2EcParameters.getG(),
                sm2EcParameters.getN());
        // 提取公钥点
        ECPoint pukPoint = sm2EcParameters.getCurve()
                                          .decodePoint(Hex.decode(pubKeyHexString));
        // 公钥前面的02或者03表示是压缩公钥，04表示未压缩公钥, 04的时候，可以去掉前面的04
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);
        
        SM2Engine sm2Engine = new SM2Engine();
        // 设置sm2为加密模式
        sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
        
        byte[] arrayOfBytes = null;
        try {
            byte[] in = data.getBytes();
            arrayOfBytes = sm2Engine.processBlock(in, 0, in.length);
        } catch (Exception e) {
            log.error("SM2加密失败");
        }
        return Hex.toHexString(arrayOfBytes);
        
    }
    
    /**
     * SM2解密算法
     * @param priKeyHexString  私钥（16进制字符串）
     * @param cipherData       密文数据
     * @return
     */
    public static String decrypt(String priKeyHexString, String cipherData) {
        // 使用BC库加解密时密文以04开头，传入的密文前面没有04则补上
        
        if (!cipherData.startsWith(UNCOMPRESSED_PUBLIC_KEY_PREFIX)) {
            cipherData = UNCOMPRESSED_PUBLIC_KEY_PREFIX + cipherData;
        }
        byte[] cipherDataByte = Hex.decode(cipherData);
        
        // 获取一条SM2曲线参数
        X9ECParameters sm2EcParameters = GMNamedCurves.getByName(STD_NAME);
        // 构造domain参数
        ECDomainParameters domainParameters = new ECDomainParameters(sm2EcParameters.getCurve(), sm2EcParameters.getG(),
                sm2EcParameters.getN());
        
        BigInteger privateKeyD = new BigInteger(priKeyHexString, 16);
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKeyD, domainParameters);
        
        SM2Engine sm2Engine = new SM2Engine();
        // 设置sm2为解密模式
        sm2Engine.init(false, privateKeyParameters);
        String result = "";
        try {
            byte[] arrayOfBytes = sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length);
            return new String(arrayOfBytes);
        } catch (Exception e) {
            log.error("SM2解密失败");
        }
        return result;
    }
    
    /**
     * 签名
     * @param cipherData
     * @param privateKeyString
     * @return
     * @throws Exception
     */
    public static String sign(String privateKeyString, String cipherData) throws Exception {
        // 待签名内容转为字节数组
        byte[] message = cipherData.getBytes();
        // 获取一条SM2曲线参数
        X9ECParameters sm2EcParameters = GMNamedCurves.getByName(STD_NAME);
        // 构造domain参数
        ECDomainParameters domainParameters = new ECDomainParameters(sm2EcParameters.getCurve(), sm2EcParameters.getG(),
                sm2EcParameters.getN());
        BigInteger privateKeyD = new BigInteger(privateKeyString, 16);
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKeyD, domainParameters);
        // 创建签名实例
        SM2Signer sm2Signer = new SM2Signer();
        // 初始化签名实例,带上ID,国密的要求,ID默认值:1234567812345678
        try {
            sm2Signer.init(true, new ParametersWithID(
                    new ParametersWithRandom(privateKeyParameters, SecureRandom.getInstance("SHA1PRNG")),
                    Strings.toByteArray(SM2_USER_ID)));
        } catch (NoSuchAlgorithmException e) {
            log.error("签名时出现异常:", e);
        }
        sm2Signer.update(message, 0, message.length);
        // 生成签名,签名分为两部分r和s,分别对应索引0和1的数组
        byte[] signBytes = sm2Signer.generateSignature();
        return Hex.toHexString(signBytes);
    }
    
    /**
     * 验签
     * @param publicKey 公钥
     * @param cipherData 密文
     * @param sign
     * @return
     */
    public static boolean verify(String publicKey, String cipherData, String sign) {
        // 待签名内容
        byte[] message = cipherData.getBytes();
        byte[] signData = Hex.decode(sign);
        // 获取一条SM2曲线参数
        X9ECParameters sm2EcParameters = GMNamedCurves.getByName(STD_NAME);
        // 构造domain参数
        ECDomainParameters domainParameters = new ECDomainParameters(sm2EcParameters.getCurve(), sm2EcParameters.getG(),
                sm2EcParameters.getN());
        // 提取公钥点
        ECPoint pukPoint = sm2EcParameters.getCurve()
                                          .decodePoint(Hex.decode(publicKey));
        // 公钥前面的02或者03表示是压缩公钥，04表示未压缩公钥, 04的时候，可以去掉前面的04
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);
        // 创建签名实例
        SM2Signer sm2Signer = new SM2Signer();
        ParametersWithID parametersWithId = new ParametersWithID(publicKeyParameters, Strings.toByteArray(SM2_USER_ID));
        sm2Signer.init(false, parametersWithId);
        sm2Signer.update(message, 0, message.length);
        // 验证签名结果
        return sm2Signer.verifySignature(signData);
    }
    
    
    /**
     * 大模型接口SM2加密
     * <AUTHOR>
     * @date 2024/12/03 09:51
     * @param publicKey 公钥
     * @param data 明文
     * @return String
     */
    public static String llmSmEncrypt(String publicKey, String data) {
        if (StringUtil.isBlank(publicKey)) {
            publicKey = STD_NAME;
        }
        SM2 sm2 = getSm2(null, publicKey);
        byte[] encryptedData = sm2.encrypt(data, KeyType.PublicKey);
        // 转换为十六进制字符串（根据需求选择Hex或Base64）
        return HexUtil.encodeHexStr(encryptedData);
    }
    
    /**
     * 获取SM2加密工具对象
     * @param privateKey 加密私钥
     * @param publicKey  加密公钥
     */
    private static SM2 getSm2(String privateKey, String publicKey) {
        
        ECPrivateKeyParameters ecPrivateKeyParameters = null;
        ECPublicKeyParameters ecPublicKeyParameters = null;
        if (StringUtils.isNotBlank(privateKey)) {
            ecPrivateKeyParameters = BCUtil.toSm2Params(privateKey);
        }
        if (StringUtils.isNotBlank(publicKey)) {
            
            if (publicKey.length() == PUBLIC_KEY_LENGTH) {
                // 这里需要去掉开始第一个字节 第一个字节表示标记
                publicKey = publicKey.substring(2);
            }
            String xhex = publicKey.substring(0, 64);
            String yhex = publicKey.substring(64, 128);
            ecPublicKeyParameters = BCUtil.toSm2Params(xhex, yhex);
        }
        // 创建sm2 对象
        SM2 sm2 = new SM2(ecPrivateKeyParameters, ecPublicKeyParameters);
        sm2.usePlainEncoding();
        sm2.setMode(SM2Engine.Mode.C1C2C3);
        return sm2;
    }
    // 以下加密方式模式为maasV2.0,和之前不一样，之前C1C2C3,v2.0 C1C3C2
    
    /**
     * SM2 Base64加密
     *
     * @param original  原文
     * @param publicKey 公钥
     * @return 密文
     */
    public static String tryEncryptBase64(String original, String publicKey) {
        try {
            return encryptBase64(original, publicKey);
        } catch (Exception e) {
            log.error("SM2 Base64加密失败,[original]: {}, [publicKey]: {}, error->", original, publicKey, e);
            return null;
        }
    }
    
    /**
     * SM2 Base64解密
     *
     * @param cipher     密文
     * @param privateKey 私钥
     * @return 明文
     */
    public static String tryDecryptBase64(String cipher, String privateKey) {
        try {
            return decrypt(cipher, privateKey);
        } catch (Exception e) {
            log.error("SM2 Base64解密失败,[cipher]: {}, [privateKey]: {}, error->", cipher, privateKey, e);
            return null;
        }
    }
    
    /**
     * SM2 Hex加密
     *
     * @param original  原文
     * @param publicKey 公钥
     * @return 密文
     */
    public static String tryEncryptHex(String original, String publicKey) {
        try {
            return encryptHex(original, publicKey);
        } catch (Exception e) {
            log.error("SM2 Hex加密失败,[original]: {}, [publicKey]: {}, error->", original, publicKey, e);
            return null;
        }
    }
    
    /**
     * SM2 Hex解密
     *
     * @param cipher     密文
     * @param privateKey 私钥
     * @return 明文
     */
    public static String tryDecryptHex(String cipher, String privateKey) {
        try {
            return sm2Decrypt(cipher, privateKey);
        } catch (Exception e) {
            log.error("SM2 Hex解密失败,[cipher]: {}, [privateKey]: {}, error->", cipher, privateKey, e);
            return null;
        }
    }
    
    /**
     * 加密十六进制
     *
     * @param original 源语言
     * @param publicKey 公钥
     * @return {@link String }
     */
    public static String encryptHex(String original, String publicKey) {
        final SM2 sm2 = new SM2(null, publicKey);
        return sm2.encryptHex(original, KeyType.PublicKey);
    }
    
    /**
     * 加密 Base64
     *
     * @param original 待加密数据
     * @param publicKey 公钥
     * @return {@link String }
     */
    public static String encryptBase64(String original, String publicKey) {
        final SM2 sm2 = new SM2(null, publicKey);
        return sm2.encryptBase64(original, KeyType.PublicKey);
    }
    
    /**
     *
     *
     * @param cipher 密码
     * @param privateKey 私钥
     * @return {@link String }
     */
    private static String sm2Decrypt(String cipher, String privateKey) {
        final SM2 sm2 = new SM2(privateKey, null);
        return sm2.decryptStr(cipher, KeyType.PrivateKey);
    }
}


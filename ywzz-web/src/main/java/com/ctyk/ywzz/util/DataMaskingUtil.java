package com.ctyk.ywzz.util;

import java.util.regex.Pattern;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: zwj
 * @date: 2025/07/21  16:48
 * @description: 数据脱敏工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public class DataMaskingUtil {
    
    private DataMaskingUtil() {
    }
    
    // 中文字符正则表达式
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    
    /**
     * 中文姓名脱敏
     * 规则：
     * - 单字姓名：保留第一个字符，其余用*替代
     * - 两字姓名：保留第一个字符，第二个字符用*替代
     * - 三字及以上姓名：保留第一个和最后一个字符，中间字符用*替代
     *
     * @param name 原始姓名
     * @return 脱敏后的姓名
     */
    public static String maskChineseName(String name) {
        // 参数校验
        if (name == null || name.trim()
                                .isEmpty()) {
            return name;
        }
        
        String trimmedName = name.trim();
        int length = trimmedName.length();
        
        // 单字姓名
        if (length == 1) {
            return trimmedName;
        }
        // 两字姓名
        else if (length == 2) {
            return trimmedName.charAt(0) + "*";
        }
        // 三字及以上姓名
        else {
            StringBuilder masked = new StringBuilder();
            // 保留第一个字符
            masked.append(trimmedName.charAt(0));
            
            // 中间字符用*替代
            masked.append("*".repeat(length - 2));
            // 保留最后一个字符
            masked.append(trimmedName.charAt(length - 1));
            return masked.toString();
        }
    }
    
    /**
     * 验证是否为中文姓名
     *
     * @param name 姓名
     * @return 是否为中文姓名
     */
    public static boolean isChineseName(String name) {
        if (name == null || name.trim()
                                .isEmpty()) {
            return false;
        }
        
        String trimmedName = name.trim();
        // 检查是否全部为中文字符
        return CHINESE_PATTERN.matcher(trimmedName)
                              .matches() && trimmedName.length() >= 2 && trimmedName.length() <= 4;
    }
}

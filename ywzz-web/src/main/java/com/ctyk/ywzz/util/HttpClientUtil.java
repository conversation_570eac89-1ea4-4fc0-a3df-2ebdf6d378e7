package com.ctyk.ywzz.util;

import com.ctyk.ywzz.entity.vo.EnvelopeVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: Liuyl
 * @date: 2024/10/09 19:03
 * @description: http客户端工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HttpClientUtil {
    
    /**
     * 默认字符集
     */
    private static final String CHARSET = "UTF-8";
    
    /**
     * 超时时间
     */
    private static final int TIMEOUT = 60000;
    
    /**
     * 创建默认的 HttpClient 配置
     */
    private static final RequestConfig REQUEST_CONFIG = RequestConfig.custom().setConnectTimeout(TIMEOUT)
            .setSocketTimeout(TIMEOUT).setConnectionRequestTimeout(TIMEOUT).build();
    
    /**
     * HttpClient 调用 WebService
     *
     * @param wsUrl webService地址，格式：<a href="http://ip:port/xxx/xxx/soap?wsdl">...</a>
     * @param action 消息的类型或目的
     * @param jsonStr JSON字符串
     * @return 结果
     */
    public static String callServiceHc(String wsUrl, String action, String jsonStr) {
        String xml = createSoapContent(action, jsonStr);
        String returnDatabase = doPostSoap(wsUrl, xml, "");
        XmlMapper xmlMapper = new XmlMapper();
        String result = null;
        try {
            EnvelopeVO envelopeVO = xmlMapper.readValue(returnDatabase, EnvelopeVO.class);
            result = envelopeVO.getBody().getHIPMessageServerResponse().getHIPMessageServerResult();
        } catch (JsonProcessingException e) {
            log.error("http调用webservice出现异常:{}",e.getMessage(), e);
            throw new RuntimeException(e);
        }
        log.info("result ===>{}", result);
        return result;
    }
    
    /**
     * 根据拼接 xml 字符串
     *
     * @param action 请求
     * @param jsonStr JSON str
     * @return 拼接后的xml
     */
    public static String createSoapContent(String action, String jsonStr) {
        log.info("开始拼接请求报文");
        // 开始拼接请求报文
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:dhcc=\"http://www.dhcc.com.cn\">\n");
        stringBuilder.append("<soapenv:Header/>\n");
        stringBuilder.append("<soapenv:Body>\n");
        stringBuilder.append("<dhcc:HIPMessageServer>\n");
        stringBuilder.append("<dhcc:action>" + action + "</dhcc:action>\n");
        stringBuilder.append("<dhcc:xml><![CDATA[\n");
        stringBuilder.append(jsonStr + "\n");
        stringBuilder.append("]]></dhcc:xml>\n");
        stringBuilder.append("</dhcc:HIPMessageServer>\n");
        stringBuilder.append("</soapenv:Body>\n");
        stringBuilder.append("</soapenv:Envelope>");
        log.info("拼接后的参数" + stringBuilder.toString());
        return stringBuilder.toString();
    }
    
    /**
     * HTTPClient 调用 WebService
     * @param url 请求地址
     * @param soap 请求体
     * @param soapAction
     * @return
     */
    public static String doPostSoap(String url, String soap, String soapAction) {
        // 请求体
        String retStr = "";
        // 创建HttpClientBuilder
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        // HttpClient
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");
            httpPost.setHeader("SOAPAction", soapAction);
            StringEntity data = new StringEntity(soap, StandardCharsets.UTF_8);
            httpPost.setEntity(data);
            CloseableHttpResponse response = closeableHttpClient.execute(httpPost);
            HttpEntity httpEntity = response.getEntity();
            if (httpEntity != null) {
                // 打印响应内容
                retStr = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            }
            // 释放资源
            closeableHttpClient.close();
        } catch (Exception e) {
            log.error("号源接口请求失败:" + e.getMessage());
            throw new RuntimeException(e);
        }
        return retStr;
    }
    
    
    /**
     * 创建HTTP POST请求
     *
     * @param url 请求地址
     * @param headers 请求头
     * @return {@link HttpPost }
     */
    private static HttpPost createHttpPost(String url, Map<String, String> headers) {
        
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(REQUEST_CONFIG);
        
        // 设置请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        
        return httpPost;
    }
    
    /**
     * 发送 POST 请求（表单格式）
     * @param url 请求URL
     * @param params 请求参数，值可以是任意Object类型
     * @param headers 请求头
     * @return 响应结果
     */
    public static String doPost(String url, Map<String, Object> params, Map<String, String> headers)
            throws IOException {
        
        HttpPost httpPost = createHttpPost(url, headers);
        
        if (params != null && !params.isEmpty()) {
            List<NameValuePair> parameters = new ArrayList<>();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                // 将Object值转换为String，null则转为空字符串
                String value = entry.getValue() == null ? "" : entry.getValue().toString();
                parameters.add(new BasicNameValuePair(entry.getKey(), value));
            }
            httpPost.setEntity(new UrlEncodedFormEntity(parameters, CHARSET));
        }
        
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            return executeRequest(client, httpPost);
        }
    }
    
    /**
     * 发送post请求，json格式
     *
     * @param url 请求地址
     * @param jsonBody JSON格式的请求体
     * @param headers 请求头
     * @return {@link String }
     * @throws IOException io异常
     */
    public static String doPostJson(String url, String jsonBody, Map<String, String> headers) throws IOException {
        
        // 如果headers为null，创建新的Map
        headers = headers != null ? new HashMap<>(headers) : new HashMap<>(6);
        // 添加JSON内容类型头
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        
        HttpPost httpPost = createHttpPost(url, headers);
        
        if (jsonBody != null && !jsonBody.isEmpty()) {
            StringEntity entity = new StringEntity(jsonBody, CHARSET);
            httpPost.setEntity(entity);
        }
        
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            return executeRequest(client, httpPost);
        }
    }
    
    
    /**
     * 不带请求头的POST请求
     *
     * @param url 请求地址
     * @param params 参数
     * @return {@link String }
     * @throws IOException io异常
     */
    public static String doPost(String url, Map<String, Object> params) throws IOException {
        return doPost(url, params, null);
    }
    
    /**
     * 不带请求头的POST JSON请求
     *
     * @param url 网址
     * @param jsonBody JSON 正文
     * @return {@link String }
     * @throws IOException io异常
     */
    public static String doPostJson(String url, String jsonBody) throws IOException {
        return doPostJson(url, jsonBody, null);
    }
    
    
    /**
     * 执行请求的通用方法
     *
     * @param client 请求客户端
     * @param request 请求
     * @return {@link String }
     * @throws IOException io异常
     */
    private static String executeRequest(CloseableHttpClient client, HttpRequestBase request) throws IOException {
        
        try (CloseableHttpResponse response = client.execute(request)) {
            HttpEntity entity = response.getEntity();
            return entity != null ? EntityUtils.toString(entity, CHARSET) : null;
        }
    }
}
package com.ctyk.ywzz.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.util
 * @author: zwj
 * @date: 2025/07/10  09:12
 * @description: base64加密解密工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class Base64Util {
    
    private Base64Util() {
    }
    
    private static final Base64.Encoder encoder = Base64.getEncoder();
    
    private static final Base64.Decoder decoder = Base64.getDecoder();
    
    /**
     * base64加密
     *
     * @param src 待加密字符串
     * @return {@link String }
     */
    public static String encodeStrBase64(String src) {
        
        if (StringUtils.isEmpty(src)) {
            return "";
        }
        return encodeBase64(src.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * base64解密
     *
     * @param src 待解密字符串
     * @return {@link String }
     */
    public static String decodeStrBase64(String src) {
        
        byte[] bytes = decodeBase64(src);
        return new String(bytes, StandardCharsets.UTF_8);
    }
    
    /**
     * base64加密
     *
     * @param src 待加密字节数组
     * @return {@link String }
     */
    public static String encodeBase64(byte[] src) {
        return encoder.encodeToString(src);
    }
    
    /**
     * base64解密
     *
     * @param src 待解密字符串
     * @return {@link byte[] }
     */
    public static byte[] decodeBase64(String src) {
        
        return decoder.decode(src);
    }
}

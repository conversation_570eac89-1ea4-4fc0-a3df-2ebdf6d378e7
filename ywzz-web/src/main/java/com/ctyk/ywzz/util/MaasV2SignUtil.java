package com.ctyk.ywzz.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.util
 * @author: zwj
 * @date: 2025/07/10  09:06
 * @description: maas平台V2.0版本接口签名工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class MaasV2SignUtil {
    
    private MaasV2SignUtil() {
    }
    
    /**
     * 签名
     *
     * @param key 钥匙
     * @param data 数据
     * @return {@link String }
     */
    public static String sign(String key, String... data) {
        String hash = HmacUtil.generateHMACSHA256((sort(data)));
        // (C1C3C2格式)
        return Sm2Util.tryEncryptHex(hash, key);
    }
    
    /**
     * 排序
     *
     * @param data 数据
     * @return {@link String }
     */
    private static String sort(String... data) {
        Arrays.sort(data);
        return String.join("-", data);
    }
}

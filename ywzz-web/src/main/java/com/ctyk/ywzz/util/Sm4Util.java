package com.ctyk.ywzz.util;


import cn.hutool.core.codec.Base64;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Arrays;
import java.util.HexFormat;

/**
 * <AUTHOR>
 * @version 1.0
 *  国密SM4工具类
 * @date 2022/10/26 14:50
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Sm4Util {
   private static final HexFormat HEX_FORMAT = HexFormat.of();
    /** 编码 **/
    private static final String ENCODING = "UTF-8";
    
    /** 加密算法 **/
    private static final String ALGORITHM_NAME = "SM4";
    
    /** 加密模式 **/
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS7Padding";
    
    /** 密钥长度 **/
    public static final int DEFAULT_KEY_SIZE = 128;
    
    static {
        Security.addProvider(new BouncyCastleProvider());
    }
    
    /**
     * 生成密钥
     * @return
     * @throws Exception
     */
    public static String generateKey() throws NoSuchAlgorithmException, NoSuchProviderException {
        return Hex.toHexString(generateKey(DEFAULT_KEY_SIZE));
    }
    
    /**
     *  生成ecb暗号
     */
    private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key)
            throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeyException {
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
        cipher.init(mode, sm4Key);
        return cipher;
    }
    
    /**
     *  生成密钥
     */
    private static byte[] generateKey(int keySize) throws NoSuchAlgorithmException, NoSuchProviderException {
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }
    
    /**
     *  加密模式之ecb
     */
    private static byte[] encryptEcbPadding(byte[] key, byte[] data)
            throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, key);
        return cipher.doFinal(data);
    }
    
    /**
     *  ECB加密
     */
    public static String encryptEcb(String hexKey, String paramStr, String charset)
            throws UnsupportedEncodingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, NoSuchProviderException, InvalidKeyException {
        
        String cipherText = "";
        if (null != paramStr && !paramStr.isEmpty()) {
            byte[] keyData = HEX_FORMAT.parseHex(hexKey);
            charset = charset.trim();
            if (charset.isEmpty()) {
                charset = ENCODING;
            }
            byte[] srcData = paramStr.getBytes(charset);
            byte[] cipherArray = encryptEcbPadding(keyData, srcData);
            cipherText = HEX_FORMAT.formatHex(cipherArray);
        }
        return cipherText;
    }
    
    /**
     * 加密base64
     * @param hexKey
     * @param paramStr
     * @param charset
     * @return
     * @throws Exception
     */
    public static String encryptEcbBase64(String hexKey, String paramStr, String charset)
            throws UnsupportedEncodingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, NoSuchProviderException, InvalidKeyException {
        String cipherText = "";
        if (null != paramStr && !paramStr.isEmpty()) {
            byte[] keyData = HEX_FORMAT.parseHex(hexKey);
            charset = charset.trim();
            if (charset.isEmpty()) {
                charset = ENCODING;
            }
            byte[] srcData = paramStr.getBytes(charset);
            byte[] cipherArray = encryptEcbPadding(keyData, srcData);
            cipherText = Base64.encode(cipherArray);
        }
        return cipherText;
    }
    
    /**
     *  解密模式ECB
     */
    private static byte[] decryptEcbPadding(byte[] key, byte[] cipherText)
            throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, key);
        return cipher.doFinal(cipherText);
    }
    
    /**
     *  sm4解密
     */
    public static String decryptEcb(String hexKey, String cipherText, String charset)
            throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, NoSuchProviderException, InvalidKeyException, UnsupportedEncodingException {
        String decryptStr = "";
        byte[] keyData = Hex.decode(hexKey);
        byte[] cipherData = HEX_FORMAT.parseHex(cipherText);
        byte[] srcData = decryptEcbPadding(keyData, cipherData);
        charset = charset.trim();
        if (charset.isEmpty()) {
            charset = ENCODING;
        }
        decryptStr = new String(srcData, charset);
        return decryptStr;
    }
    
    /**
     *  sm4解密
     */
    public static String decryptEcbBase64(String hexKey, String cipherText, String charset)
            throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, NoSuchProviderException, InvalidKeyException, UnsupportedEncodingException {
        String decryptStr = "";
        byte[] keyData = HEX_FORMAT.parseHex(hexKey);
        byte[] cipherData = Base64.decode(cipherText);
        byte[] srcData = decryptEcbPadding(keyData, cipherData);
        charset = charset.trim();
        if (charset.isEmpty()) {
            charset = ENCODING;
        }
        decryptStr = new String(srcData, charset);
        return decryptStr;
    }
    
    /**
     *  加密串校验
     */
    public static boolean verifyEcb(String hexKey, String cipherText, String paramStr)
            throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, NoSuchProviderException, InvalidKeyException {
        boolean flag;
        byte[] keyData = HEX_FORMAT.parseHex(hexKey);
        byte[] cipherData =HEX_FORMAT.parseHex(cipherText);
        byte[] decryptData = decryptEcbPadding(keyData, cipherData);
        byte[] srcData = paramStr.getBytes(StandardCharsets.UTF_8);
        flag = Arrays.equals(decryptData, srcData);
        return flag;
    }
    
    
}

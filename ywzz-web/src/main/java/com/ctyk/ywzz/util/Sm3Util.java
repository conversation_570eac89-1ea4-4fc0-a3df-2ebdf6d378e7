package com.ctyk.ywzz.util;


import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.macs.HMac;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @project: zhyy-cloud-igs
 * @package: com.ctyk.util
 * @author: wangjm
 * @date: 2024/07/11 09:09
 * @description: sm3算法加密工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public class Sm3Util {
    
    private Sm3Util() {
    }
    
    /**
     * 返回长度=32的byte数组
     * @explain 生成对应的hash值
     * @param srcData 源数据
     * @return byte数组
     */
    public static String hash(byte[] srcData) {
        SM3Digest digest = new SM3Digest();
        digest.update(srcData, 0, srcData.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return Arrays.toString(hash);
    }
    
    /**
     * sm3算法加密
     * @param paramStr 待加密字符串
     * @param key 密钥
     * @return 返回加密后，固定长度=32的16进制字符串
     */
    public static String encryptPlus(String paramStr, String key) {
        // 将返回的hash值转换成16进制字符串
        // 将字符串转换成byte数组
        byte[] srcData = paramStr.getBytes(StandardCharsets.UTF_8);
        // 调用hash()
        byte[] resultHash = hmac(srcData, key.getBytes(StandardCharsets.UTF_8));
        // 将返回的hash值转换成16进制字符串
        return Hex.toHexString(resultHash);
    }
    
    
    /**
     * 通过密钥进行加密
     * @explain 指定密钥进行加密
     * @param key 密钥
     * @param srcData 被加密的byte数组
     * @return 加密后的数据
     */
    public static byte[] hmac(byte[] key, byte[] srcData) {
        KeyParameter keyParameter = new KeyParameter(key);
        SM3Digest digest = new SM3Digest();
        HMac mac = new HMac(digest);
        mac.init(keyParameter);
        mac.update(srcData, 0, srcData.length);
        byte[] result = new byte[mac.getMacSize()];
        mac.doFinal(result, 0);
        return result;
    }
    
    /**
     * 判断源数据与加密数据是否一致
     * @explain 通过验证原数组和生成的hash数组是否为同一数组，验证2者是否为同一数据
     * @param srcStr 原字符串
     * @param sm3HexString 16进制字符串
     * @return 校验结果
     */
    public static boolean verify(String srcStr, String sm3HexString) {
        boolean flag = false;
        byte[] srcData = srcStr.getBytes(StandardCharsets.UTF_8);
        byte[] sm3Hash = Hex.decode(sm3HexString);
        
        byte[] newHash = hash(srcData).getBytes();
        if (Arrays.equals(newHash, sm3Hash)) {
            flag = true;
        }
        return flag;
    }
    
    
    /**
     * sm3算法加密
     * <AUTHOR>
     * @date 2024/07/15 16:02
     * @param srcData 源数据
     * @return String
     */
    public static String encrypt(String srcData) {
        SM3Digest digest = new SM3Digest();
        byte[] input;
        input = srcData.getBytes(StandardCharsets.UTF_8);
        digest.update(input, 0, input.length);
        byte[] result = new byte[digest.getDigestSize()];
        digest.doFinal(result, 0);
        return Hex.toHexString(result);
    }
    
}

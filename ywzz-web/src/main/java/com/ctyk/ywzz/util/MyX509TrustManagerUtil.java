package com.ctyk.ywzz.util;

import javax.net.ssl.X509TrustManager;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: Liyh
 * @date: 2024/12/09 15:12
 * @description: https请求配置
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public class MyX509TrustManagerUtil implements X509TrustManager {
    @Override
    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

    }

    @Override
    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return new X509Certificate[0];
    }
}

package com.ctyk.ywzz.util;

import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.common.json.utils.JsonUtils;
import com.ctyk.ywzz.config.MaasV2Config;
import com.ctyk.ywzz.config.PromptConfig;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.MaasV2AgentRequestDTO;
import com.ctyk.ywzz.entity.dto.MaasV2AgentResponseDTO;
import com.ctyk.ywzz.entity.dto.MaasV2KnowledgeRequestDTO;
import com.ctyk.ywzz.entity.dto.MaasV2KnowledgeResponseDTO;
import com.ctyk.ywzz.entity.dto.MaasV2ModelServerRequestDTO;
import com.ctyk.ywzz.entity.dto.MaasV2ModelServerResponseDTO;
import com.ctyk.ywzz.entity.dto.ModelServerResponseDTO;
import com.ctyk.ywzz.entity.dto.OpenAiCompletionMessageDTO;
import com.ctyk.ywzz.entity.dto.SmartMedicineDTO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordDetailPO;
import com.ctyk.ywzz.entity.vo.ConsultInfoVO;
import com.ctyk.ywzz.entity.vo.IntentionInfoVO;
import com.ctyk.ywzz.service.impl.LlmClientService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.util
 * @author: zwj
 * @date: 2025/07/11  09:51
 * @description: maas平台V2.0版本接口工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
public class MaasV2HttpUtil {
    
    @Resource
    private MaasV2Config maasV2Config;
    
    @Resource
    private WebClient.Builder webClientBuilder;
    
    @Resource
    private LlmClientService llmClientService;
    
    @Resource
    private PromptConfig promptConfig;
    
    private MaasV2HttpUtil() {
    }
    
    /**
     * 构建请求头
     *
     * @param includeStreamAccept 包括 Stream 接受
     * @return {@link MultiValueMap }<{@link String }, {@link String }>
     */
    public MultiValueMap<String, String> buildHeaders(boolean includeStreamAccept) {
        
        String accessToken = maasV2Config.getAccessToken();
        String apiKey = maasV2Config.getApiKey();
        String homeId = maasV2Config.getHomeId();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = MaasV2SignUtil.sign(accessToken, apiKey, homeId, timestamp);
        
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("source", maasV2Config.getSource());
        headers.add("api-key", apiKey);
        headers.add("home-Id", homeId);
        headers.add("timestamp", timestamp);
        headers.add("sign", sign);
        
        if (includeStreamAccept) {
            headers.add("Accept", "text/event-stream");
        }
        
        log.debug("构建MaaS V2请求头: {}", JsonUtil.toJsonString(headers));
        return headers;
    }
    
    /**
     * 意图识别
     * 返回类型
     * @param content 内容
     * @return {@link String }
     */
    public String getIntentRecognition(String content) {
        
        MultiValueMap<String, String> headers = buildHeaders(false);
        MaasV2AgentRequestDTO body = new MaasV2AgentRequestDTO();
        body.setCode(maasV2Config.getIntentRecognizerCode());
        body.setContent(content);
        
        StringBuilder responseBuilder = new StringBuilder();
        // 发送请求并收集结果
        webClientBuilder.build()
                        .post()
                        .uri(maasV2Config.getAgentUrl())
                        .contentType(MediaType.APPLICATION_JSON)
                        .headers(httpHeaders -> httpHeaders.addAll(headers))
                        .body(BodyInserters.fromValue(body))
                        .retrieve()
                        .bodyToFlux(String.class)
                        .doOnNext(line -> {
                            log.debug("【maasV2.0意图识别响应】: {}", line);
                            if (Constants.SSE_END.equals(line) || ObjectUtils.isEmpty(line)) {
                                return;
                            }
                            responseBuilder.append(line);
                        })
                        .blockLast();
        if (!ObjectUtils.isEmpty(responseBuilder)) {
            MaasV2AgentResponseDTO dto = JsonUtils.parseObject(responseBuilder.toString(),
                    MaasV2AgentResponseDTO.class);
            IntentionInfoVO vo = JsonUtils.parseObject(dto.getMsg(), IntentionInfoVO.class);
            return vo.getType();
        }
        throw new ServiceException(Constants.ERROR_MESSAGE);
    }
    
    /**
     * 智慧分诊(deepseek)
     *
     * @param content 内容
     * @return {@link ConsultInfoVO }
     */
    public ConsultInfoVO getDsTriage(String content, String openId, Long recordId) {
        
        MultiValueMap<String, String> headers = buildHeaders(false);
        MaasV2AgentRequestDTO body = new MaasV2AgentRequestDTO();
        body.setCode(maasV2Config.getSmartTriageDsCode());
        body.setContent(content);
        
        StringBuilder responseBuilder = new StringBuilder();
        // 发送请求并收集结果
        webClientBuilder.build()
                        .post()
                        .uri(maasV2Config.getAgentUrl())
                        .contentType(MediaType.APPLICATION_JSON)
                        .headers(httpHeaders -> httpHeaders.addAll(headers))
                        .body(BodyInserters.fromValue(body))
                        .retrieve()
                        .bodyToFlux(String.class)
                        .doOnNext(line -> {
                            log.debug("【maasV2.0deepseek智慧分诊响应】: {}", line);
                            if (Constants.SSE_END.equals(line) || ObjectUtils.isEmpty(line)) {
                                return;
                            }
                            MaasV2AgentResponseDTO dto = JsonUtils.parseObject(line, MaasV2AgentResponseDTO.class);
                            responseBuilder.append(dto.getMsg());
                        })
                        .blockLast();
        log.info("【maasV2.0 deepseek智慧分诊响应结果收集数据】:{}", responseBuilder);
        return llmClientService.parseString(responseBuilder.toString());
    }
    
    /**
     * 智慧分诊
     *
     * @param content 内容
     * @return {@link ConsultInfoVO }
     */
    public ConsultInfoVO getTriage(String content, String openId, Long recordId) {
        
        MultiValueMap<String, String> headers = buildHeaders(false);
        MaasV2AgentRequestDTO body = new MaasV2AgentRequestDTO();
        body.setCode(maasV2Config.getSmartTriageCode());
        body.setContent(content);
        
        StringBuilder responseBuilder = new StringBuilder();
        // 发送请求并收集结果
        webClientBuilder.build()
                        .post()
                        .uri(maasV2Config.getAgentUrl())
                        .contentType(MediaType.APPLICATION_JSON)
                        .headers(httpHeaders -> httpHeaders.addAll(headers))
                        .body(BodyInserters.fromValue(body))
                        .retrieve()
                        .bodyToFlux(String.class)
                        .doOnNext(line -> {
                            log.debug("【maasV2.0智慧分诊响应】: {}", line);
                            if (Constants.SSE_END.equals(line) || ObjectUtils.isEmpty(line)) {
                                return;
                            }
                            MaasV2AgentResponseDTO dto = JsonUtils.parseObject(line, MaasV2AgentResponseDTO.class);
                            responseBuilder.append(dto.getMsg());
                        })
                        .blockLast();
        log.info("【MaasV2.0智慧分诊响应结果收集信息】:{}", responseBuilder);
        return llmClientService.parseString(responseBuilder.toString());
    }
    
    /**
     * 获取知识库信息
     *
     * @param content 内容
     * @param openId openId
     * @param recordId 记录 ID
     * @return {@link String }
     */
    public String getKnowledgeInfo(String content, String openId, Long recordId) {
        
        MaasV2KnowledgeRequestDTO body = new MaasV2KnowledgeRequestDTO();
        body.setCode(maasV2Config.getConsultCode());
        body.setQuery(content);
        log.debug("【maasV2.0知识库请求入参】:{}", body);
        String knowledgeInfo = webClientBuilder.build()
                                               .post()
                                               .uri(maasV2Config.getKnowledgeUrl())
                                               .contentType(MediaType.APPLICATION_JSON)
                                               .headers(httpHeaders -> httpHeaders.addAll(buildHeaders(false)))
                                               .body(BodyInserters.fromValue(body))
                                               .retrieve()
                                               .bodyToMono(String.class)
                                               .doOnNext(line -> {
                                                   log.debug("【maasV2.0知识库响应】: {}", line);
                                               })
                                               .block();
        log.info("【MassV2.0知识库响应结果收集信息】:{}", knowledgeInfo);
        if (knowledgeInfo != null) {
            MaasV2KnowledgeResponseDTO responseDto = JsonUtils.parseObject(knowledgeInfo,
                    MaasV2KnowledgeResponseDTO.class);
            if (!ObjectUtils.isEmpty(responseDto) && Integer.valueOf(200)
                                                            .equals(responseDto.getCode())) {
                // 获取score最高的一个，如果分数相同取第一个
                if (responseDto.getData() != null && !responseDto.getData()
                                                                 .isEmpty()) {
                    // 使用reduce来确保在分数相同时取第一个
                    MaasV2KnowledgeResponseDTO.DataDTO bestMatch = responseDto.getData()
                                                                              .stream()
                                                                              .reduce((item1, item2) -> {
                                                                                  // 如果item1的score >= item2的score，则保留item1（第一个优先）
                                                                                  int comparison = item1.getScore()
                                                                                                        .compareTo(
                                                                                                                item2.getScore());
                                                                                  return comparison >= 0 ? item1
                                                                                          : item2;
                                                                              })
                                                                              .orElse(null);
                    
                    log.debug("选择最高分数的知识库结果 - score: {}, content: {}", bestMatch.getScore(),
                            bestMatch.getContent());
                    return bestMatch.getContent();
                }
            } else {
                log.warn("知识库响应失败 - code: {}, message: {}", responseDto != null ? responseDto.getCode() : "null",
                        responseDto != null ? responseDto.getMessage() : "null");
            }
        }
        return null;
    }
    
    /**
     * 智能问药
     *
     * @param question 问题
     * @return {@link SmartMedicineDTO }
     */
    public SmartMedicineDTO getSmartMedicine(String question) {
        
        MultiValueMap<String, String> headers = buildHeaders(false);
        MaasV2AgentRequestDTO body = new MaasV2AgentRequestDTO();
        body.setCode(maasV2Config.getSmartMedicineCode());
        body.setContent(question);
        
        StringBuilder responseBuilder = new StringBuilder();
        // 发送请求并收集结果
        webClientBuilder.build()
                        .post()
                        .uri(maasV2Config.getAgentUrl())
                        .contentType(MediaType.APPLICATION_JSON)
                        .headers(httpHeaders -> httpHeaders.addAll(headers))
                        .body(BodyInserters.fromValue(body))
                        .retrieve()
                        .bodyToFlux(String.class)
                        .doOnNext(line -> {
                            log.debug("【maasV2.0智能问药响应】: {}", line);
                            if (Constants.SSE_END.equals(line) || ObjectUtils.isEmpty(line)) {
                                return;
                            }
                            MaasV2AgentResponseDTO responseDto = JsonUtils.parseObject(line,
                                    MaasV2AgentResponseDTO.class);
                            responseBuilder.append(responseDto.getMsg());
                        })
                        .blockLast();
        log.info("【MaasV2.0智能问药收集结果】:{}", responseBuilder);
        if (!ObjectUtils.isEmpty(responseBuilder)) {
            SmartMedicineDTO medicineDto = new SmartMedicineDTO();
            medicineDto.setData(responseBuilder.toString());
            return medicineDto;
        }
        throw new ServiceException(Constants.ERROR_MESSAGE);
    }
    
    /**
     * 智能问药-流式
     *
     * @param question 问题
     * @param callback 流式回调函数，接收每次返回的数据片段
     * @return {@link Flux}<{@link String}> 流式响应
     */
    public Flux<String> getSmartMedicineStream(String question, Consumer<String> callback) {
        
        MultiValueMap<String, String> headers = buildHeaders(true);
        MaasV2AgentRequestDTO body = new MaasV2AgentRequestDTO();
        body.setCode(maasV2Config.getSmartMedicineCode());
        body.setContent(question);
        
        return webClientBuilder.build()
                               .post()
                               .uri(maasV2Config.getAgentUrl())
                               .contentType(MediaType.APPLICATION_JSON)
                               .headers(httpHeaders -> httpHeaders.addAll(headers))
                               .body(BodyInserters.fromValue(body))
                               .retrieve()
                               .bodyToFlux(String.class)
                               .doOnNext(line -> {
                                   log.debug("【maasV2.0智能问药流式响应】: {}", line);
                                   if (Constants.SSE_END.equals(line) || ObjectUtils.isEmpty(line)) {
                                       return;
                                   }
                                   try {
                                       MaasV2AgentResponseDTO responseDto = JsonUtils.parseObject(line,
                                               MaasV2AgentResponseDTO.class);
                                       String message = responseDto.getMsg();
                                       if (!ObjectUtils.isEmpty(message) && callback != null) {
                                           callback.accept(message);
                                       }
                                   } catch (Exception e) {
                                       log.warn("MaasV2.0解析智能问药流式响应失败: {}", e.getMessage());
                                   }
                               })
                               .filter(line -> !Constants.SSE_END.equals(line) && !ObjectUtils.isEmpty(line))
                               .map(line -> {
                                   try {
                                       MaasV2AgentResponseDTO responseDto = JsonUtils.parseObject(line,
                                               MaasV2AgentResponseDTO.class);
                                       return responseDto.getMsg();
                                   } catch (Exception e) {
                                       log.warn("解析智能问药流式响应失败: {}", e.getMessage());
                                       return "";
                                   }
                               })
                               .filter(msg -> !ObjectUtils.isEmpty(msg));
        /*
            使用方式1：带回调的流式调用
            maasV2HttpUtil.getSmartMedicineStream("头疼吃什么药",
                message -> sseClient.sendMessage(userId, message))
                .subscribe();
            
            试用方式2：直接订阅流式数据
            maasV2HttpUtil.getSmartMedicineStream("头疼吃什么药", null)
                .subscribe(message -> {
                    // 处理每个消息片段
                    log.info("收到消息: {}", message);
        });*/
    }
    
    /**
     * 多轮对话非流式
     *
     * @param content 用户当前内容
     * @param historyChatList 历史聊天内容
     * @param dsFlag 是否是deepseek模型（是否开启深度思考）
     * @return {@link ModelServerResponseDTO }
     * @throws Exception 异常信息
     */
    public MaasV2ModelServerResponseDTO getMultiTurnDialogue(String content,
            List<ShcConversationRecordDetailPO> historyChatList, Boolean dsFlag) throws Exception {
        
        MultiValueMap<String, String> headers = buildHeaders(false);
        List<OpenAiCompletionMessageDTO> messages = new ArrayList<>();
        // 提示词
        messages.add(new OpenAiCompletionMessageDTO(Constants.ROLE_SYSTEM,
                Boolean.TRUE.equals(dsFlag) ? promptConfig.assistantPromptContent()
                        : promptConfig.assistantPromptDsContent()));
        
        for (ShcConversationRecordDetailPO chat : historyChatList) {
            messages.add(new OpenAiCompletionMessageDTO(Constants.ROLE_USER, chat.getHealthStatus()));
            messages.add(new OpenAiCompletionMessageDTO(Constants.ROLE_ASSISTANT, chat.getRecommendations()));
        }
        messages.add(new OpenAiCompletionMessageDTO(Constants.ROLE_USER, content));
        MaasV2ModelServerRequestDTO requestDto = new MaasV2ModelServerRequestDTO();
        requestDto.setModel(
                Boolean.TRUE.equals(dsFlag) ? maasV2Config.getThinkModelCode() : maasV2Config.getNoThinkModelCode());
        requestDto.setMessages(messages);
        
        String response = HttpClientUtil.doPostJson(maasV2Config.getModelServerUrl(), JsonUtil.toJsonString(requestDto),
                headers.toSingleValueMap());
        log.info("【maasV2.0模型服务接口响应结果】:\n{}\n", response);
        return JsonUtil.parseObject(response, MaasV2ModelServerResponseDTO.class);
    }
}

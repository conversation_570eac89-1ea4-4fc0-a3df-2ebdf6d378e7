package com.ctyk.ywzz.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.ctyk.common.core.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @project: ctyk-mia
 * @package: com.ctyk.mia.utils
 * @author: zwj
 * @date: 2024/11/21  09:19
 * @description: easyexcel工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class EasyExcelUtil {
    
    /**
     * 同步读取excel指定的sheet到list
     *
     * @param filePath 文件路径
     * @param clazz 数据模型类
     * @param sheetNo sheet序号，从0开始
     * @return {@link List }<{@link T }>
     */
    public static <T> List<T> syncReadModel(String filePath, Class<T> clazz, Integer sheetNo) {
        
        return EasyExcelFactory.read(filePath).sheet(sheetNo).head(clazz).doReadSync();
    }
    
    /**
     * 导出Excel到文件
     * @param fileName 文件名（包含路径）
     * @param sheetName sheet名称
     * @param clazz 数据模型类
     * @param data 数据列表
     */
    public static <T> void exportToFile(String fileName, String sheetName, Class<T> clazz, List<T> data) {
        
        EasyExcel.write(fileName, clazz).registerWriteHandler(getDefaultStyle()).sheet(sheetName).doWrite(data);
    }
    
    /**
     * 导出Excel到响应流
     * @param response HTTP响应
     * @param fileName 文件名
     * @param sheetName sheet名称
     * @param clazz 数据模型类
     * @param data 数据列表
     */
    public static <T> void exportToResponse(HttpServletResponse response, String fileName, String sheetName,
            Class<T> clazz, List<T> data) {
        
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String downloadFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + downloadFileName + ".xlsx");
            
            EasyExcel.write(response.getOutputStream(), clazz).registerWriteHandler(getDefaultStyle()).sheet(sheetName)
                    .doWrite(data);
        } catch (IOException e) {
            log.error("导出excel出现异常:{}", e.getMessage(), e);
            throw new ServiceException("导出Excel失败");
        }
    }
    
    /**
     * 获取默认的Excel样式
     */
    private static HorizontalCellStyleStrategy getDefaultStyle() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 自动换行
        contentWriteCellStyle.setWrapped(true);
        
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}

package com.ctyk.ywzz.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.util
 * @author: zwj
 * @date: 2025/07/10  09:11
 * @description: Hmac工具类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class HmacUtil {
    
    private HmacUtil() {
    }
    private static final String ALGORITHM = "HmacSHA256";
    
    private static final String DEFAULT_SECRET_KEY = "maas";
    
    /**
     * 生成HMACSHA256
     *
     * @param data 数据
     * @return {@link String }
     */
    public static String generateHMACSHA256(String data) {
        return generateHMACSHA256(DEFAULT_SECRET_KEY, data);
    }
    
    /**
     * 生成HMACSHA256
     *
     * @param secretKey 密钥
     * @param data 数据
     * @return {@link String }
     */
    public static String generateHMACSHA256(String secretKey, String data) {
        try {
            Mac mac = Mac.getInstance(ALGORITHM);
            
            // init key
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            mac.init(secretKeySpec);
            
            byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            
            return Base64Util.encodeBase64(hmacBytes);
        } catch (Exception e) {
            log.error("签名失败, error->", e);
            return null;
        }
    }
}

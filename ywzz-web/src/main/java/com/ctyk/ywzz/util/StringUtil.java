package com.ctyk.ywzz.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 字符串工具类
 * @date 2021/8/28 14:46
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class StringUtil {
    
    /** 下划线 **/
    private static final String UNDERLINE = "_";
    
    private static final String EMPTY = "";
    
    private static final int STRING_BUILDER_SIZE = 256;
    
    private static final String SYMBOLS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    
    private static final Random RANDOM = new SecureRandom();
    
    /**
     * 生成16位随机字符串（数字，大小写字母）
     */
    private static final char[] CODES = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F',
            'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a',
            'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
            'w', 'x', 'y', 'z'};
    
    public static String getRandomStr(int length) {
        if (length < 0) {
            throw new IllegalArgumentException("Length must be non-negative");
        }
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CODES[ThreadLocalRandom.current().nextInt(CODES.length)]);
        }
        return sb.toString();
    }
    
    /**
     * 获取32位UUID
     * @return
     */
    public static String getUuid() {
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }
    
    /**
     * 判断字符串是否在数组中
     * @return
     */
    public static boolean isStringInArray(String str, String[] array) {
        for (String val : array) {
            if (str.equals(val)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 生成几位随机数
     */
    public static int randomNum(int num) {
        
        int x = 1;
        for (int i = 1; i < num; i++) {
            x = x * 10;
        }
        // 生成一个 1 到 9 之间的数字，然后乘以 x
        return (RANDOM.nextInt(9) + 1) * x;
    }
    
    /**
     * 转换为String数组<br>
     *
     * @param str 被转换的值
     */
    public static String[] toStrArray(String str) {
        return toStrArray(",", str);
    }
    
    /**
     * 转换为String数组<br>
     *
     * @param split 分隔符
     */
    public static String[] toStrArray(String split, String str) {
        return str.split(split);
    }
    
    
    /**
     * 下划线转换为首字母大写驼峰
     * @param name
     * @return
     */
    public static String upToCamelCase(String name) {
        StringBuilder result = new StringBuilder();
        if (name == null || name.isEmpty()) {
            return "";
        } else if (!name.contains(UNDERLINE)) {
            // 不含下划线，仅将首字母大写
            return name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();
        }
        // 用下划线将原始字符串分割
        String[] camels = name.split(UNDERLINE);
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 首字母大写
            result.append(camel.substring(0, 1).toUpperCase());
            result.append(camel.substring(1).toLowerCase());
        }
        return result.toString();
    }
    
    /**
     * 下划线转换为首字母小写驼峰
     * @param name
     * @return
     */
    public static String lowToCamelCase(String name) {
        String result = upToCamelCase(name);
        return result.substring(0, 1).toLowerCase() + result.substring(1);
    }
    
    /**
     *  替换字符串
     * @param old
     * @param rep
     * @return
     */
    public static String format(String old, String rep) {
        return String.format(old, rep);
    }
    
    /**
     * 判断字符串是否为空
     * @param cs
     * @return
     */
    public static boolean isBlank(final CharSequence cs) {
        final int strLen = length(cs);
        if (strLen == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 判断字符串是否为空
     * @param cs
     * @return
     */
    public static boolean isNotBlank(final CharSequence cs) {
        return !isBlank(cs);
    }
    
    /**
     * 获取字符串长度
     * @param cs
     * @return
     */
    public static int length(final CharSequence cs) {
        return cs == null ? 0 : cs.length();
    }
    
    /**
     * list拼接为字符串
     * @param iterable
     * @param separator
     * @return
     */
    public static String join(final Iterable<?> iterable, final String separator) {
        if (iterable == null) {
            return null;
        }
        return join(iterable.iterator(), separator);
    }
    
    /**
     * 字符串拼接
     * @param iterator
     * @param separator
     * @return
     */
    public static String join(final Iterator<?> iterator, final String separator) {
        if (iterator == null) {
            return null;
        }
        if (!iterator.hasNext()) {
            return EMPTY;
        }
        final Object first = iterator.next();
        if (!iterator.hasNext()) {
            return Objects.toString(first, "");
        }
        
        // two or more elements
        final StringBuilder buf = new StringBuilder(STRING_BUILDER_SIZE);
        if (first != null) {
            buf.append(first);
        }
        
        while (iterator.hasNext()) {
            if (separator != null) {
                buf.append(separator);
            }
            final Object obj = iterator.next();
            if (obj != null) {
                buf.append(obj);
            }
        }
        return buf.toString();
    }
    
    /**
     * 获取随机字符串 Nonce Str
     *
     * @return String 随机字符串
     */
    public static String generateNonceStr() {
        char[] nonceChars = new char[32];
        for (int index = 0; index < nonceChars.length; ++index) {
            nonceChars[index] = SYMBOLS.charAt(RANDOM.nextInt(SYMBOLS.length()));
        }
        return new String(nonceChars);
    }
    
    /**
     * 分割列表
     * @param list
     * @param len
     * @param <Map>
     * @return
     */
    public static <Map> List<List<Map>> splitList(List<Map> list, int len) {
        if (list == null || list.isEmpty() || len < 1) {
            return Collections.emptyList();
        }
        List<List<Map>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<Map> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }
    
    /**
     * URL中的参数转为Map
     * @param urls
     * @return
     */
    public static Map<String, String> urlParamToMap(String urls) {
        String param = urls.substring(urls.indexOf("?") + 1, urls.length());
        Map<String, String> map = new HashMap<>(10);
        String[] params = param.split("&");
        for (String s : params) {
            String[] p = s.split("=");
            if (p.length == 2) {
                map.put(p[0], p[1]);
            }
        }
        return map;
    }
}

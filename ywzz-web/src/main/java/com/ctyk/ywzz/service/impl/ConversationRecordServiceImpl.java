package com.ctyk.ywzz.service.impl;

import com.ctyk.ywzz.mapper.ShcConversationRecordMapper;
import com.ctyk.ywzz.service.ConversationRecordService;
import com.ctyk.ywzz.util.UserUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: zwj
 * @date: 2025/01/07  18:24
 * @description: 对话记录业务实现层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Service
public class ConversationRecordServiceImpl implements ConversationRecordService {
    
    @Resource
    private ShcConversationRecordMapper shcConversationRecordMapper;
    
    /**
     * 获取某用户最后一条记录ID
     *
     * @param modelType 模型类型
     * @return {@link String }
     */
    @Override
    public String getLastRecordId(String modelType) {
        
        String userId = UserUtil.getUserIdOrDefault();
        return shcConversationRecordMapper.getLastRecordIdByUserIdAndModelType(userId, modelType);
    }
}

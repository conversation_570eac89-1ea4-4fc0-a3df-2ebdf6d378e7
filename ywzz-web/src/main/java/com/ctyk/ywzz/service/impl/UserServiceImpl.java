package com.ctyk.ywzz.service.impl;


import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.StpUtil;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.ywzz.config.EncryptProperty;
import com.ctyk.ywzz.config.ThirdSystemConfig;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.TokenInfoDTO;
import com.ctyk.ywzz.entity.vo.SecretInfoVO;
import com.ctyk.ywzz.service.UserService;
import com.ctyk.ywzz.util.Sm3Util;
import com.ctyk.ywzz.util.Sm4Util;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: <PERSON><PERSON>
 * @date: 2024/07/22 15:39
 * @description: 用户
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    
    @Resource
    private EncryptProperty encryptProperty;
    
    @Resource
    private RedisService redisService;
    
    @Resource
    private ThirdSystemConfig thirdSystemConfig;
    
    public static final long MAX_REQUEST_DIFFERENCE = 600;
    
    /**
     * 获取秘钥
     * @return String
     * <AUTHOR>
     * @date 2024/07/29 14:27
     */
    @Override
    public SecretInfoVO getSecret() throws Exception {
        if (!redisService.hasKey(Constants.SERVER_PRIVATE_KEY)
                || redisService.getExpire(Constants.SERVER_PRIVATE_KEY) <= 2) {
            log.info("秘钥过期，请重新获取！");
            encryptProperty.fresh();
        }
        // 自行加密
        String token = SaHolder.getRequest().getHeader(Constants.TOKEN);
        String userId = SaHolder.getRequest().getHeader(Constants.USER_ID);
        // 根据约定规则计算密钥
        String secretKey = Sm3Util.encrypt(String.join("", token, userId)).substring(2, 18);
        SecretInfoVO secretInfoVO = new SecretInfoVO();
        secretInfoVO.setPrivateKey(Sm4Util.encryptEcbBase64(Hex.toHexString(secretKey.getBytes()),
                encryptProperty.getSm2ClientPrivateKey(), "UTF-8"));
        secretInfoVO.setPublicKey(
                Sm4Util.encryptEcbBase64(Hex.toHexString(secretKey.getBytes()), encryptProperty.getSm2ServerPublicKey(),
                        "UTF-8"));
        // 返回给客户端时  有效期时间减去2s
        secretInfoVO.setExpireTime(redisService.getExpire(Constants.SERVER_PRIVATE_KEY) * 1000 - 2000);
        return secretInfoVO;
    }
    
    /**
     * 获取token
     * <AUTHOR>
     * @date 2024/07/17 11:34
     * @param req 请求实体
     * @return String
     */
    @Override
    public String getToken(TokenInfoDTO req) {
        
        log.info("获取token请求参数:{}", req);
        long timestamp = Long.parseLong(req.getTimestamp());
        long currentTime = System.currentTimeMillis() / 1000;
        long difference = Math.abs(currentTime - timestamp);
        if (difference > MAX_REQUEST_DIFFERENCE) {
            throw new ServiceException("请求时间超过限制!");
        }
        String[] arr = {req.getTimestamp(), req.getOpenId(),
                thirdSystemConfig.getAppCode() + thirdSystemConfig.getSecretKey()};
        Arrays.sort(arr);
        // 将三个参数字符串拼接成一个字符串
        StringBuilder sb = new StringBuilder();
        for (String temp : arr) {
            sb.append(temp);
        }
        // 这里利用了SM3的加密工具类
        String signature = Sm3Util.encrypt(sb.toString());
        if (!signature.equals(req.getSignature())) {
            throw new ServiceException("签名校验失败!");
        }
        StpUtil.login(req.getOpenId());
        return StpUtil.getTokenValue();
    }
}
package com.ctyk.ywzz.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: zwj
 * @date: 2025/05/12  14:05
 * @description: 语音文本互转业务层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface TtsService {
    
    /**
     * 语音转文本
     *
     * @param speechFile 语音文件
     * @return {@link String }
     */
    String speechToText(MultipartFile speechFile) throws Exception;
    
    /**
     * 文本转语音
     *
     * @param text 发短信
     * @return {@link byte[] }
     */
    byte[] textToSpeech(String text) throws Exception;
    
    /**
     * 长文本转语音
     *
     * @param text 文本
     * @return {@link byte[] }
     */
    byte[] longTextToSpeech(String text);
}

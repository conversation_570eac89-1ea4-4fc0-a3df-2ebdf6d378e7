package com.ctyk.ywzz.service;

import com.ctyk.ywzz.entity.qo.SaveConversationFeedbackQO;
import com.ctyk.ywzz.entity.qo.UpdateConversationFeedbackQO;
import com.ctyk.ywzz.entity.vo.QueryFeedbackOptionListVO;
import com.ctyk.ywzz.entity.vo.QueryFeedbackVO;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: zwj
 * @date: 2025/01/07  15:39
 * @description: 对话反馈业务层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface ConversationFeedbackService {
    
    /**
     * 保存对话反馈信息
     *
     * @param qo 实体
     * @return {@link String }
     */
    String saveFeedback(SaveConversationFeedbackQO qo);
    
    /**
     * 获取反馈选项信息
     *
     * @return {@link List }<{@link QueryFeedbackOptionListVO }>
     */
    List<QueryFeedbackOptionListVO> listOptions();
    
    /**
     * 更新对话反馈信息
     *
     * @param recordId 记录id
     * @param qo 请求实体
     * @return {@link String }
     */
    String updateFeedback(String recordId, UpdateConversationFeedbackQO qo);
    
    /**
     * 获取对话反馈
     *
     * @param recordId 记录 ID
     * @return {@link QueryFeedbackVO }
     */
    QueryFeedbackVO getFeedback(String recordId);
    
    /**
     * 删除反馈信息
     *
     * @param recordId 记录 ID
     * @return {@link Boolean }
     */
    Boolean deleteFeedback(String recordId);
}

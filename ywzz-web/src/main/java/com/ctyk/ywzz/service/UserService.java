package com.ctyk.ywzz.service;

import com.ctyk.ywzz.entity.dto.TokenInfoDTO;
import com.ctyk.ywzz.entity.vo.SecretInfoVO;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: <PERSON><PERSON>
 * @date: 2024/07/22 15:38
 * @description: 用户
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface UserService {
    
    /**
     * 获取token
     *
     * @param req 请求参数
     * @return String
     */
    String getToken(TokenInfoDTO req);
    
    /**
     * 获取秘钥
     *
     * @return String
     * @throws Exception 异常
     */
    SecretInfoVO getSecret() throws Exception;
}
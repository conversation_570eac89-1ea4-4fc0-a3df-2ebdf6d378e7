package com.ctyk.ywzz.service;

import com.ctyk.ywzz.entity.vo.VisitUserVO;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: Liyh
 * @date: 2024/07/22 15:52
 * @description: 就诊service
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface VisitService {
    
    /**
     * 查询就诊人列表
     *
     * @param openId openId
     * @return {@link List }<{@link VisitUserVO }>
     */
    List<VisitUserVO> getVisitUserList(String openId);

}

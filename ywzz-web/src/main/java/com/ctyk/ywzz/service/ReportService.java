package com.ctyk.ywzz.service;


import com.ctyk.ywzz.entity.dto.ReportDetailReqDTO;
import com.ctyk.ywzz.entity.dto.ReportListReqDTO;
import com.ctyk.ywzz.entity.dto.ReportParseResultDTO;
import com.ctyk.ywzz.entity.vo.InspectSuggestVO;
import com.ctyk.ywzz.entity.vo.ParsePdfReportVO;
import com.ctyk.ywzz.entity.vo.ReportBaseInfoVO;
import com.ctyk.ywzz.entity.vo.ReportDetailVo;
import com.ctyk.ywzz.entity.vo.ReportListVO;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: Liyh
 * @date: 2024/07/17 16:57
 * @description: 体检报告Service
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface ReportService {
    
    /**
     * 查询报告基本信息
     * <AUTHOR>
     * @date 2024/07/30 17:28
     * @param reportId 报告ID
     * @return ReportBaseInfoVO
     */
    ReportBaseInfoVO getReportBaseInfo(String reportId);
    
    /**
     * 获取报告列表
     *
     * @param reportListReqDTO 报告列表请求实体
     * @return {@link Map }<{@link String }, {@link List }<{@link ReportListVO }>>
     */
    Map<String, List<ReportListVO>> getReportList(ReportListReqDTO reportListReqDTO);
    
    /**
     * 查询报告总检建议
     * <AUTHOR>
     * @date 2024/07/30 17:28
     * @param reportId 报告ID
     * @return InspectSuggestVO
     */
    InspectSuggestVO getReportInspectSuggest(String reportId);
    
    /**
     * 获取报告详细信息
     *
     * @param reportDetailReq 报告详细信息 req
     * @return {@link ReportDetailVo }
     */
    ReportDetailVo getReportDetailInfo(ReportDetailReqDTO reportDetailReq);
    
    /**
     * 上传 PDF
     *
     * @param file 文件
     * @return {@link String }
     */
    String uploadPdf(MultipartFile file);
    
    /**
     * 获取报告解析结果
     *
     * @param parseId 解析 ID
     * @return {@link ReportParseResultDTO }
     */
    ReportParseResultDTO getReportParseResult(String parseId);
    
    /**
     * 上传PDF报告并解析
     *
     * @param file 文件
     * @return {@link Flux }<{@link ParsePdfReportVO }>
     */
    Flux<ParsePdfReportVO> uploadPdfAndParse(MultipartFile file);
    
}

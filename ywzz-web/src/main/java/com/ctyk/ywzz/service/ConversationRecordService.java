package com.ctyk.ywzz.service;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: zwj
 * @date: 2025/01/07  18:24
 * @description: 对话记录业务层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface ConversationRecordService {
    
    /**
     * 获取某用户最后一条记录ID
     *
     * @param modelType 型号类型
     * @return {@link String }
     */
    String getLastRecordId(String modelType);
}

package com.ctyk.ywzz.service.impl;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: REDIS通用方法
 * @date 2021/8/28 14:46
 */
@Component
@Slf4j
public class RedisService {
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 获取单条
     * @param key 键
     * @return 值
     */
    public String getString(final String key) {
        
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return Optional.ofNullable(redisTemplate.opsForValue().get(key)).map(Object::toString).orElse(null);
    }
    
    public boolean hasKey(final String key) {
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }
    
    /**
     * 存储单条
     * @param key 键
     * @param value 值
     * @param <T> 值类型
     */
    public <T> void set(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }
    
    /**
     * 限时存储单条
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param <T> 值类型
     */
    public <T> void setEx(final String key, final T value, final Long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }
    
    /**
     * 删除单条
     * @param key 键
     * @param <T> 类型
     */
    public <T> void del(final String key) {
        redisTemplate.delete(key);
    }
    
    /**
     * 获取存活时间
     * @param key 键
     * @return 存活时间
     */
    public Long getExpire(final String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }
    
    /**
     * 重置存活时间
     * @param key 键
     * @param timeout 过期时间
     */
    public void expire(final String key, final long timeout) {
        redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
    }
    
    /**
     * 左侧入队列
     * @param key 键
     * @param value 值
     * @param <T> 值类型
     */
    public <T> void lPush(final String key, final T value) {
        redisTemplate.opsForList().leftPush(key, value);
    }
    
    /**
     * 获取自增序列
     * @param key 键
     * @return 序列值
     */
    public Long atomicLong(final String key) {
        RedisAtomicLong counter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        return counter.incrementAndGet();
    }
    
    public <T> boolean setNxEx(final String key, final T value, final Long timeout) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, TimeUnit.SECONDS);
    }
    
    /**
     * 哈希 添加
     *
     * @param key     Redis键
     * @param hashKey 哈希键
     * @param value   哈希值
     */
    public void hmSet(String key, String hashKey, String value) {
        HashOperations<String, String, String> hash = redisTemplate.opsForHash();
        hash.put(key, hashKey, value);
    }
    
    /**
     * 哈希获取数据
     *
     * @param key     Redis键
     * @param hashKey 哈希键
     * @return 哈希值
     */
    public String hmGet(String key, String hashKey) {
        HashOperations<String, String, String> hash = redisTemplate.opsForHash();
        return hash.get(key, hashKey);
    }
    
    /**
     * 判断hash是否存在键
     *
     * @param key     Redis键
     * @param hashKey 哈希键
     * @return 是否存在
     */
    public boolean hmHasKey(String key, String hashKey) {
        HashOperations<String, String, String> hash = redisTemplate.opsForHash();
        return hash.hasKey(key, hashKey);
    }
    
    /**
     * 删除hash中一条或多条数据
     *
     * @param key      Redis键
     * @param hashKeys 哈希键名数组
     * @return 删除数量
     */
    public long hmRemove(String key, String... hashKeys) {
        HashOperations<String, String, String> hash = redisTemplate.opsForHash();
        return hash.delete(key, hashKeys);
    }
    
    /**
     * 获取所有哈希键值对
     *
     * @param key Redis键名
     * @return 哈希Map
     */
    public Map<String, String> hashMapGet(String key) {
        HashOperations<String, String, String> hash = redisTemplate.opsForHash();
        return hash.entries(key);
    }
    
    /**
     * 保存Map到哈希
     *
     * @param key Redis键名
     * @param map 哈希Map
     */
    public void hashMapSet(String key, Map<String, String> map) {
        HashOperations<String, String, String> hash = redisTemplate.opsForHash();
        hash.putAll(key, map);
    }
    
    /**
     * 获取锁
     *
     * @param key 锁键
     * @param value 锁值
     * @return 是否获取成功
     */
    public boolean setLock(String key, String value, long expire) {
        try {
            return redisTemplate.execute(
                    (RedisCallback<Boolean>) connection -> connection.set(key.getBytes(), value.getBytes(),
                            Expiration.seconds(expire), RedisStringCommands.SetOption.ifAbsent()));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return false;
    }
    
    /**
     * 释放锁操作
     * @param key 锁键
     * @param value 锁值
     * @return 是否释放成功
     */
    public boolean releaseLock(String key, String value) {
        // 封装参数
        List<String> keyList = new ArrayList<>();
        keyList.add(key);
        // 指定 lua 脚本，并且指定返回值类型
        DefaultRedisScript<Boolean> redisScript = new DefaultRedisScript<>();
        String lua = "if redis.call('get', KEYS[1]) == '" + value
                + "' then return redis.call('del', KEYS[1]) else return false end";
        // 设置脚本
        redisScript.setScriptText(lua);
        // 定义返回类型。注意如果没有这个定义，spring不会返回结果
        redisScript.setResultType(Boolean.class);
        return redisTemplate.execute(redisScript, keyList, value);
    }
    
}

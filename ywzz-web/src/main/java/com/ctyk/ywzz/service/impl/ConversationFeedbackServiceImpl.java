package com.ctyk.ywzz.service.impl;


import com.baidu.fsg.uid.UidGenerator;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.ywzz.entity.po.ShcConversationFeedbackPO;
import com.ctyk.ywzz.entity.po.ShcFeedbackOptionPO;
import com.ctyk.ywzz.entity.qo.SaveConversationFeedbackQO;
import com.ctyk.ywzz.entity.qo.UpdateConversationFeedbackQO;
import com.ctyk.ywzz.entity.vo.QueryFeedbackOptionListVO;
import com.ctyk.ywzz.entity.vo.QueryFeedbackVO;
import com.ctyk.ywzz.mapper.ShcConversationFeedbackMapper;
import com.ctyk.ywzz.service.ConversationFeedbackService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: zwj
 * @date: 2025/01/07  15:39
 * @description: 对话反馈业务实现层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Service
public class ConversationFeedbackServiceImpl implements ConversationFeedbackService {
    
    @Resource
    private ShcConversationFeedbackMapper shcConversationFeedbackMapper;
    
    @Resource
    private UidGenerator uidGenerator;
    
    /**
     * 保存对话反馈信息
     *
     * @param qo 实体
     * @return <{@link String }>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveFeedback(SaveConversationFeedbackQO qo) {
        
        // 检查该条对话的反馈是否存在
        checkFeedbackExists(qo);
        ShcConversationFeedbackPO po = new ShcConversationFeedbackPO();
        BeanUtils.copyProperties(qo, po);
        String feedbackId = String.valueOf(uidGenerator.getUID());
        po.setFeedbackId(feedbackId);
        po.setOperateTime(LocalDateTime.now());
        shcConversationFeedbackMapper.saveFeedback(po);
        return feedbackId;
    }
    
    /**
     * 检查反馈是否存在
     *
     * @param qo 请求实体
     */
    private void checkFeedbackExists(SaveConversationFeedbackQO qo) {
        
        String recordId = qo.getRecordId();
        int count = shcConversationFeedbackMapper.countFeedbackByRecordId(recordId);
        if (count > 0) {
            throw new ServiceException("该条对话的反馈已存在!");
        }
    }
    
    /**
     * 获取反馈选项信息
     *
     * @return <{@link List }<{@link QueryFeedbackOptionListVO }>>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<QueryFeedbackOptionListVO> listOptions() {
        
        List<ShcFeedbackOptionPO> list = shcConversationFeedbackMapper.listOptions();
        List<QueryFeedbackOptionListVO> voList = new ArrayList<>(list.size());
        list.forEach(po -> {
            QueryFeedbackOptionListVO vo = new QueryFeedbackOptionListVO();
            BeanUtils.copyProperties(po, vo);
            voList.add(vo);
        });
        return voList;
    }
    
    /**
     * 更新对话反馈信息
     *
     * @param recordId 记录id
     * @param qo 请求实体
     * @return <{@link String }>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateFeedback(String recordId, UpdateConversationFeedbackQO qo) {
        
        ShcConversationFeedbackPO po = new ShcConversationFeedbackPO();
        BeanUtils.copyProperties(qo, po);
        po.setRecordId(recordId);
        po.setOperateTime(LocalDateTime.now());
        shcConversationFeedbackMapper.updateFeedback(po);
        return recordId;
    }
    
    /**
     * 获取对话反馈
     *
     * @param recordId 记录 ID
     * @return {@link QueryFeedbackVO }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryFeedbackVO getFeedback(String recordId) {
        
        ShcConversationFeedbackPO po = shcConversationFeedbackMapper.getFeedback(recordId);
        if (po != null) {
            QueryFeedbackVO vo = new QueryFeedbackVO();
            BeanUtils.copyProperties(po, vo);
            return vo;
        }
        return null;
    }
    
    /**
     * 删除反馈信息
     *
     * @param recordId 记录 ID
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFeedback(String recordId) {
        
        shcConversationFeedbackMapper.deleteFeedback(recordId);
        return Boolean.TRUE;
    }
}

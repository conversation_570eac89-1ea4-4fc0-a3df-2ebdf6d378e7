package com.ctyk.ywzz.service.impl;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.ywzz.config.ReportInterpretationConfig;
import com.ctyk.ywzz.entity.dto.DeptInfoDTO;
import com.ctyk.ywzz.entity.vo.ConsultInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: <PERSON><PERSON>
 * @date: 2024/07/26 16:08
 * @description: 大模型客户端接口
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
public class LlmClientService {
    
    @Resource
    private ReportInterpretationConfig reportInterpretationConfig;

    /**
     * 结果字符串解析
     * <AUTHOR>
     * @date 2024/11/01 14:19
     * @param str 字符串
     * @return ConsultInfoVO
     */
    public ConsultInfoVO parseString(String str) {
        ConsultInfoVO consultInfoVO = new ConsultInfoVO();
        
        // Extract recommendations
        String recommendationsPattern = "健康建议：(.*?)(?=科室名称\\d*：|$)";
        // ds思考内容
        Pattern thinkPattern = Pattern.compile("(?:<think>)?(.*?)</think>", Pattern.DOTALL);
        Matcher thinkMatcher = thinkPattern.matcher(str);
        if (thinkMatcher.find()) {
            consultInfoVO.setThinkContent(thinkMatcher.group(1).trim());
        }
        
        Pattern pattern = Pattern.compile(recommendationsPattern, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            consultInfoVO.setRecommendations(matcher.group(1).trim());
        }
        
        // Extract department info
        List<DeptInfoDTO> departments = new ArrayList<>();
        String deptPattern = "科室名称(\\d*)：([^\\s]*)\\s*推荐概率值\\d*：([\\d.]+)";
        pattern = Pattern.compile(deptPattern);
        matcher = pattern.matcher(str);
        while (matcher.find()) {
            DeptInfoDTO deptInfoDTO = new DeptInfoDTO();
            deptInfoDTO.setDepartment(matcher.group(2).trim());
            deptInfoDTO.setConfidence(matcher.group(3).trim());
            departments.add(deptInfoDTO);
        }
        
        consultInfoVO.setDepartments(departments);
        consultInfoVO.setDeptFlag(CollectionUtils.isEmpty(departments));
        return consultInfoVO;
    }

    /**
     * 上传PDF体检报告
     *
     * @param file 文件
     * @param headers 头
     * @return {@link String }
     */
    public String uploadPdf(MultipartFile file, Map<String, String> headers) {
        
        try {
            // 发送请求并获取响应
            return uploadPdfContent(file.getBytes(), file.getOriginalFilename(), headers);
        } catch (Exception e) {
            log.error("上传PDF文件失败", e);
            throw new ServiceException("上传报告文件失败");
        }
    }
    
    public String uploadPdfContent(byte[] fileBytes, String fileName, Map<String, String> headers) {
        
        String boundary = "----" + System.currentTimeMillis();
        headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        headers.put("Content-Type", "multipart/form-data; boundary=" + boundary);
        
        // 创建POST请求并设置请求头
        HttpRequest request = HttpUtil.createPost(reportInterpretationConfig.getUploadUrl())
                .timeout(reportInterpretationConfig.getTimeout());
        headers.forEach(request::header);
        
        // 使用字节数组而不是MultipartFile
        request.form("file", fileBytes, fileName);
        request.form("stream", false);
        
        // 发送请求并获取响应
        return request.execute().body();
    }
}
package com.ctyk.ywzz.service;

import com.ctyk.ywzz.entity.dto.QueryQuestionnaireDTO;
import com.ctyk.ywzz.entity.qo.ConsultQO;
import com.ctyk.ywzz.entity.qo.MultiTurnDialogueQO;
import com.ctyk.ywzz.entity.vo.QuestionnaireVO;
import com.ctyk.ywzz.entity.vo.ShcShortcutKeyTemplateVO;
import com.ctyk.ywzz.entity.vo.SmartServiceInfoVO;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: <PERSON><PERSON>
 * @date: 2024/07/17 10:28
 * @description: 健康咨询
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
public interface ConsultService {

    /**
     * 获取问题示例
     * <AUTHOR>
     * @date 2024/07/17 17:19
     * @return List<QuestionnaireVO>
     */
    List<QuestionnaireVO> getQuestionnaireList(QueryQuestionnaireDTO request);

    /**
     * 获取就诊建议及科室
     * <AUTHOR>
     * @date 2024/07/17 17:40
     * @param req 请求实体
     * @return ConsultInfoVO
     */
    SmartServiceInfoVO getConsultInfo(ConsultQO req);

    /**
     * 查询所有的快捷键信息
     * <AUTHOR>
     * @date 2024/10/11 15:43
     * @return List<ShcShortcutKeyTemplatePO>
     */
    List<ShcShortcutKeyTemplateVO> getShortcutKeys();
    
    /**
     * 获取多轮对话记录ID
     *
     * @return {@link Long }
     */
    Long getRecordId();
    
    /**
     * 多轮对话
     *
     * @param req 要求
     * @return {@link SmartServiceInfoVO }
     */
    SmartServiceInfoVO multiTurnDialogue(MultiTurnDialogueQO req);
}

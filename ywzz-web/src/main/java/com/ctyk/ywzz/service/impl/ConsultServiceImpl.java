package com.ctyk.ywzz.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidu.fsg.uid.UidGenerator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.common.json.utils.JsonUtils;
import com.ctyk.ywzz.config.AccompanySymptomsConfig;
import com.ctyk.ywzz.config.HospitalConfigProperties;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.DeptInfoDTO;
import com.ctyk.ywzz.entity.dto.HistoryChatDTO;
import com.ctyk.ywzz.entity.dto.MaasV2AgentResponseDTO;
import com.ctyk.ywzz.entity.dto.MaasV2ModelServerResponseDTO;
import com.ctyk.ywzz.entity.dto.MultiTurnDialogueDataDTO;
import com.ctyk.ywzz.entity.dto.QueryOrganizationMapDTO;
import com.ctyk.ywzz.entity.dto.QueryQuestionnaireDTO;
import com.ctyk.ywzz.entity.dto.ScheduleReqDTO;
import com.ctyk.ywzz.entity.dto.ShcOrganizationMapDTO;
import com.ctyk.ywzz.entity.dto.SymptomResponseDTO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordDetailPO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordPO;
import com.ctyk.ywzz.entity.po.ShcHealthCareTemplatePO;
import com.ctyk.ywzz.entity.po.ShcShortcutKeyTemplatePO;
import com.ctyk.ywzz.entity.qo.ConsultQO;
import com.ctyk.ywzz.entity.qo.MultiTurnDialogueQO;
import com.ctyk.ywzz.entity.vo.ConsultInfoVO;
import com.ctyk.ywzz.entity.vo.KnowledgeBaseInfoVO;
import com.ctyk.ywzz.entity.vo.QuestionnaireVO;
import com.ctyk.ywzz.entity.vo.ScheduleItemVO;
import com.ctyk.ywzz.entity.vo.ScheduleReqVO;
import com.ctyk.ywzz.entity.vo.ShcShortcutKeyTemplateVO;
import com.ctyk.ywzz.entity.vo.SmartServiceInfoVO;
import com.ctyk.ywzz.enums.WebRequestCodeEnum;
import com.ctyk.ywzz.mapper.ShcConversationRecordDetailMapper;
import com.ctyk.ywzz.mapper.ShcConversationRecordMapper;
import com.ctyk.ywzz.mapper.ShcHealthCareTemplateMapper;
import com.ctyk.ywzz.mapper.ShcOrganizationMapMapper;
import com.ctyk.ywzz.mapper.ShcShortcutKeyTemplateMapper;
import com.ctyk.ywzz.model.BaseAppointmentSourceRequest;
import com.ctyk.ywzz.model.BaseAppointmentSourceResponse;
import com.ctyk.ywzz.service.AppointmentSourceService;
import com.ctyk.ywzz.service.ConsultService;
import com.ctyk.ywzz.util.HttpClientUtil;
import com.ctyk.ywzz.util.JsonUtil;
import com.ctyk.ywzz.util.MaasV2HttpUtil;
import com.ctyk.ywzz.util.UserUtil;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: Liuyl
 * @date: 2024/07/17 10:36
 * @description: 健康咨询
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Service
public class ConsultServiceImpl implements ConsultService {

    @Resource
    private ShcHealthCareTemplateMapper shcHealthCareTemplateMapper;

    @Resource
    private ShcConversationRecordMapper shcConversationRecordMapper;

    @Resource
    private ShcConversationRecordDetailMapper shcConversationRecordDetailMapper;

    @Resource
    private ShcShortcutKeyTemplateMapper shcShortcutKeyTemplateMapper;

    @Resource
    private ShcOrganizationMapMapper shcOrganizationMapMapper;

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private HospitalConfigProperties hospitalConfig;

    private final Pattern placeholderPattern = Pattern.compile("\\$\\{([^}]+)}");

    @Resource
    private AppointmentSourceService appointmentSourceService;

    @Resource
    private AccompanySymptomsConfig accompanySymptomsConfig;

    @Resource
    private MaasV2HttpUtil maasV2HttpUtil;

    Pattern thinkPattern = Pattern.compile("(?:<think>)?(.*?)</think>", Pattern.DOTALL);


    /**
     * 查询示例问题
     *
     * @param request 请求实体
     * @return {@link List }<{@link QuestionnaireVO }>
     */
    @Override
    public List<QuestionnaireVO> getQuestionnaireList(QueryQuestionnaireDTO request) {

        // 不传查询数量默认查询10条
        LambdaQueryWrapper<ShcHealthCareTemplatePO> qw = new LambdaQueryWrapper<>();
        qw.eq(!ObjectUtils.isEmpty(request) && !ObjectUtils.isEmpty(request.getPageName()),
                ShcHealthCareTemplatePO::getPageName, request.getPageName());
        qw.orderByAsc(ShcHealthCareTemplatePO::getSeqNo);
        IPage<ShcHealthCareTemplatePO> pageInfo = new Page<>();
        if (ObjectUtils.isEmpty(request) || ObjectUtils.isEmpty(request.getCount())) {
            pageInfo.setSize(10);
        } else {
            pageInfo.setSize(request.getCount());
        }
        pageInfo.setCurrent(1);
        List<ShcHealthCareTemplatePO> poList = shcHealthCareTemplateMapper.selectList(pageInfo, qw);
        List<QuestionnaireVO> voList = new ArrayList<>();
        if (ObjectUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        poList.forEach(po -> {
            QuestionnaireVO vo = new QuestionnaireVO();
            BeanUtils.copyProperties(po, vo);
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 获取就诊建议及科室
     *
     * @param req 请求实体
     * @return ConsultInfoVO
     * <AUTHOR>
     * @date 2024/07/17 17:40
     */
    @Override
    public SmartServiceInfoVO getConsultInfo(ConsultQO req) {

        String content = req.getContent();
        String intentionType = maasV2HttpUtil.getIntentRecognition(content);
        log.info("【MaasV2.0意图识别返回结果】:{}", intentionType);
        SmartServiceInfoVO smartServiceInfo = new SmartServiceInfoVO();
        String openId = UserUtil.getUserIdOrDefault();
        long recordId = uidGenerator.getUID();
        if (Constants.LLM.equals(intentionType)) {
            // 智能导诊
            long startTime = System.currentTimeMillis();
            ConsultInfoVO llmConsultInfo = getLlmConsultInfo(req, openId, recordId);
            long endTime = System.currentTimeMillis();
            if (llmConsultInfo != null && StringUtils.isNotBlank(llmConsultInfo.getRecommendations())) {
                smartServiceInfo.setType(Constants.LLM);
                smartServiceInfo.setConsultInfo(llmConsultInfo);
                smartServiceInfo.setRecordId(String.valueOf(recordId));
                smartServiceInfo.setThinkContent(llmConsultInfo.getThinkContent());
                smartServiceInfo.setThinkTime((endTime - startTime) / 1000);
                return smartServiceInfo;
            }
        }
        // 宣传中心 + 异常 + 其他
        boolean isArg = Constants.RAG.equals(intentionType);
        List<KnowledgeBaseInfoVO> largeModelResp = getLargeModelResp(isArg, content, openId, recordId);
        smartServiceInfo.setType(Constants.RAG);
        smartServiceInfo.setKnowledgeBaseList(largeModelResp);
        smartServiceInfo.setRecordId(String.valueOf(recordId));
        return smartServiceInfo;
    }

    /**
     * 查询所有的快捷键信息
     *
     * @return List<ShcShortcutKeyTemplatePO>
     * <AUTHOR>
     * @date 2024/10/11 15:43
     */
    @Override
    public List<ShcShortcutKeyTemplateVO> getShortcutKeys() {

        List<ShcShortcutKeyTemplatePO> poList = shcShortcutKeyTemplateMapper.getShcShortcutKeyTemplateList();
        List<ShcShortcutKeyTemplateVO> voList = new ArrayList<>(poList.size());
        poList.forEach(po -> {
            ShcShortcutKeyTemplateVO vo = new ShcShortcutKeyTemplateVO();
            BeanUtils.copyProperties(po, vo);
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 获取多轮对话记录ID
     *
     * @return {@link Long }
     */
    @Override
    public Long getRecordId() {

        long recordId = uidGenerator.getUID();
        ShcConversationRecordPO po = new ShcConversationRecordPO();
        po.setRecordId(recordId);
        po.setStartTime(LocalDateTime.now());
        String userId = UserUtil.getUserIdOrDefault();
        po.setUserId(userId);
        shcConversationRecordMapper.insert(po);
        return recordId;
    }

    /**
     * 多轮对话
     *
     * @param req 请求实体
     * @return {@link SmartServiceInfoVO }
     */
    @Override
    public SmartServiceInfoVO multiTurnDialogue(MultiTurnDialogueQO req) {

        // 预处理用户输入内容
        String processedContent = extractGenderAndAge(req.getContent());

        // 获取历史对话记录
        List<ShcConversationRecordDetailPO> historyRecords = getShcConversationRecordDetailList(req.getRecordId());

        // 检查是否已存在人体图
        boolean hasExistingBodyImage = checkExistingBodyImage(historyRecords);

        // 调用大模型获取响应
        MultiTurnDialogueResult modelResult = callModelForMultiTurnDialogue(processedContent, historyRecords,
                req.getDsFlag());

        // 构建并保存对话记录
        ShcConversationRecordDetailPO recordDetail = buildConversationRecord(processedContent, req.getRecordId(),
                modelResult.getDialogueData());
        saveRecordDetail(recordDetail);

        // 构建最终响应
        return buildMultiTurnDialogueResponse(req, modelResult, hasExistingBodyImage, historyRecords, processedContent);
    }

    /**
     * 检查历史对话中是否已存在人体图
     *
     * @param historyRecords 历史对话记录
     * @return 是否存在人体图
     */
    private boolean checkExistingBodyImage(List<ShcConversationRecordDetailPO> historyRecords) {
        return historyRecords
                .stream()
                .map(ShcConversationRecordDetailPO::getRecommendations)
                .filter(Objects::nonNull)
                .map(answer -> JsonUtil.parseObject(answer, MultiTurnDialogueDataDTO.class))
                .filter(Objects::nonNull)
                .anyMatch(data -> !ObjectUtils.isEmpty(data.getImageName()));
    }

    /**
     * 调用大模型进行多轮对话
     *
     * @param content        处理后的用户内容
     * @param historyRecords 历史记录
     * @param dsFlag         深度思考标志
     * @return 模型响应结果
     */
    private MultiTurnDialogueResult callModelForMultiTurnDialogue(String content,
                                                                  List<ShcConversationRecordDetailPO> historyRecords, Boolean dsFlag) {
        long startTime = System.currentTimeMillis();

        try {
            MaasV2ModelServerResponseDTO modelResponse = maasV2HttpUtil.getMultiTurnDialogue(content, historyRecords,
                    dsFlag);
            long endTime = System.currentTimeMillis();

            MaasV2AgentResponseDTO responseData = modelResponse.getData();
            String rawResponse = responseData.getMsg();

            // 处理响应数据
            ProcessedModelResponse processedResponse = processModelResponse(rawResponse);

            return new MultiTurnDialogueResult(processedResponse.getDialogueData(), processedResponse.getThinkContent(),
                    (endTime - startTime) / 1000);

        } catch (Exception e) {
            log.error("【多轮对话调用模型接口出现异常】content: {}, dsFlag: {}, error: {}", content, dsFlag,
                    e.getMessage(), e);
            throw new ServiceException(Constants.ERROR_MESSAGE);
        }
    }

    /**
     * 处理模型原始响应数据
     *
     * @param rawResponse 原始响应
     * @return 处理后的响应数据
     */
    private ProcessedModelResponse processModelResponse(String rawResponse) {
        // 提取思考内容
        String thinkContent = extractThinkContent(rawResponse);
        String cleanedResponse = removeThinkContent(rawResponse, thinkContent);

        // 清理markdown标记
        String finalResponse = cleanMarkdownTags(cleanedResponse);

        // 解析为DTO对象
        MultiTurnDialogueDataDTO dialogueData = JsonUtil.parseObject(finalResponse, MultiTurnDialogueDataDTO.class);
        if (dialogueData != null) {
            dialogueData.setThinkContent(thinkContent);
        }

        return new ProcessedModelResponse(dialogueData, thinkContent);
    }

    /**
     * 提取思考内容
     *
     * @param response 原始响应
     * @return 思考内容
     */
    private String extractThinkContent(String response) {
        if (!response.contains("</think>")) {
            return "";
        }

        Matcher thinkMatcher = thinkPattern.matcher(response);
        return thinkMatcher.find() ? thinkMatcher
                .group(1)
                .trim() : "";
    }

    /**
     * 移除响应中的思考内容
     *
     * @param response     原始响应
     * @param thinkContent 思考内容
     * @return 清理后的响应
     */
    private String removeThinkContent(String response, String thinkContent) {
        if (StringUtils.isEmpty(thinkContent)) {
            return response;
        }

        Matcher thinkMatcher = thinkPattern.matcher(response);
        return thinkMatcher.replaceFirst("");
    }

    /**
     * 清理markdown代码块标记
     *
     * @param response 响应内容
     * @return 清理后的内容
     */
    private String cleanMarkdownTags(String response) {
        return response
                .replaceAll("```json\\s*", "")
                .replaceAll("```\\s*$", "")
                .trim();
    }

    /**
     * 构建对话记录
     *
     * @param content      用户内容
     * @param recordId     记录ID
     * @param dialogueData 对话数据
     * @return 对话记录详情
     */
    private ShcConversationRecordDetailPO buildConversationRecord(String content, Long recordId,
                                                                  MultiTurnDialogueDataDTO dialogueData) {
        ShcConversationRecordDetailPO recordDetail = new ShcConversationRecordDetailPO();
        recordDetail.setHealthStatus(content);
        recordDetail.setRecordId(recordId);
        recordDetail.setType(Boolean.TRUE);

        if (dialogueData != null) {
            recordDetail.setRecommendations(JsonUtils.toJsonString(dialogueData));

            List<DeptInfoDTO> departments = dialogueData.getDepartments();
            if (!ObjectUtils.isEmpty(departments)) {
                recordDetail.setDepartments(JsonUtil.toJsonString(departments));
            }
        }

        return recordDetail;
    }

    /**
     * 构建多轮对话最终响应
     *
     * @param req                  原始请求
     * @param modelResult          模型结果
     * @param hasExistingBodyImage 是否已存在人体图
     * @param historyRecords       历史记录
     * @param processedContent     处理后的内容
     * @return 最终响应
     */
    private SmartServiceInfoVO buildMultiTurnDialogueResponse(MultiTurnDialogueQO req,
                                                              MultiTurnDialogueResult modelResult, boolean hasExistingBodyImage,
                                                              List<ShcConversationRecordDetailPO> historyRecords, String processedContent) {

        SmartServiceInfoVO response = new SmartServiceInfoVO();
        MultiTurnDialogueDataDTO dialogueData = modelResult.getDialogueData();

        // 处理异常情况
        if (dialogueData == null) {
            return buildErrorResponse(req.getRecordId());
        }

        // 构建咨询信息
        ConsultInfoVO consultInfo = buildConsultInfo(dialogueData, hasExistingBodyImage, historyRecords,
                processedContent);

        // 设置响应基本信息
        response.setRecordId(String.valueOf(req.getRecordId()));
        response.setConsultInfo(consultInfo);
        response.setType(Constants.LLM);
        response.setThinkTime(modelResult.getThinkTime());

        // 处理思考内容（根据dsFlag决定是否返回）
        if (Boolean.TRUE.equals(req.getDsFlag())) {
            response.setThinkContent(modelResult.getThinkContent());
        }

        return response;
    }

    /**
     * 构建咨询信息
     *
     * @param dialogueData         对话数据
     * @param hasExistingBodyImage 是否已存在人体图
     * @param historyRecords       历史记录
     * @param processedContent     处理后的内容
     * @return 咨询信息
     */
    private ConsultInfoVO buildConsultInfo(MultiTurnDialogueDataDTO dialogueData, boolean hasExistingBodyImage,
                                           List<ShcConversationRecordDetailPO> historyRecords, String processedContent) {

        ConsultInfoVO consultInfo = new ConsultInfoVO();
        consultInfo.setDeptFlag(Boolean.FALSE);
        consultInfo.setRecommendations(dialogueData.getResult());
        consultInfo.setRequirePatient(dialogueData.getRequirePatient());

        // 处理科室推荐
        List<DeptInfoDTO> departments = dialogueData.getDepartments();
        if (!ObjectUtils.isEmpty(departments)) {
            List<DeptInfoDTO> processedDepartments = getDeptList(departments);
            consultInfo.setDepartments(processedDepartments);
            consultInfo.setDeptFlag(Boolean.TRUE);
            // 有科室推荐时，不显示人体图
            consultInfo.setImageName(null);
        } else {
            // 处理人体图显示
            consultInfo.setImageName(hasExistingBodyImage ? null : dialogueData.getImageName());

            // 处理症状推荐
            handleSymptomRecommendation(consultInfo, dialogueData, hasExistingBodyImage, historyRecords,
                    processedContent);
        }

        return consultInfo;
    }

    /**
     * 处理症状推荐逻辑
     *
     * @param consultInfo          咨询信息
     * @param dialogueData         对话数据
     * @param hasExistingBodyImage 是否已存在人体图
     * @param historyRecords       历史记录
     * @param processedContent     处理后的内容
     */
    private void handleSymptomRecommendation(ConsultInfoVO consultInfo, MultiTurnDialogueDataDTO dialogueData,
                                             boolean hasExistingBodyImage, List<ShcConversationRecordDetailPO> historyRecords, String processedContent) {

        // 检查是否需要症状推荐
        boolean shouldRecommendSymptoms = ObjectUtils.isEmpty(dialogueData.getDepartments()) && Boolean.FALSE.equals(
                dialogueData.getRequirePatient()) && hasExistingBodyImage && Boolean.TRUE.equals(
                accompanySymptomsConfig.getEnable());

        if (shouldRecommendSymptoms) {
            String allUserContent = buildAllUserContent(historyRecords, processedContent);
            List<String> symptomList = getSymptoms(allUserContent);
            consultInfo.setSymptomList(symptomList);
        }
    }

    /**
     * 构建所有用户对话内容
     *
     * @param historyRecords 历史记录
     * @param currentContent 当前内容
     * @return 所有用户内容
     */
    private String buildAllUserContent(List<ShcConversationRecordDetailPO> historyRecords, String currentContent) {
        StringBuilder userContent = new StringBuilder();

        historyRecords.forEach(record -> {
            String healthStatus = record.getHealthStatus();
            if (StringUtils.isNotBlank(healthStatus)) {
                userContent.append(healthStatus);
            }
        });

        userContent.append(currentContent);
        return userContent.toString();
    }

    /**
     * 构建错误响应
     *
     * @param recordId 记录ID
     * @return 错误响应
     */
    private SmartServiceInfoVO buildErrorResponse(Long recordId) {
        SmartServiceInfoVO errorResponse = new SmartServiceInfoVO();
        ConsultInfoVO consultInfo = new ConsultInfoVO();
        consultInfo.setRecommendations(Constants.ERROR_MESSAGE);
        errorResponse.setConsultInfo(consultInfo);
        errorResponse.setRecordId(String.valueOf(recordId));
        return errorResponse;
    }

    /**
     * 多轮对话结果内部类
     */
    @Data
    private static class MultiTurnDialogueResult {

        private final MultiTurnDialogueDataDTO dialogueData;

        private final String thinkContent;

        private final Long thinkTime;

        public MultiTurnDialogueResult(MultiTurnDialogueDataDTO dialogueData, String thinkContent, Long thinkTime) {
            this.dialogueData = dialogueData;
            this.thinkContent = thinkContent;
            this.thinkTime = thinkTime;
        }
    }

    /**
     * 处理后的模型响应内部类
     */
    @Data
    private static class ProcessedModelResponse {

        private final MultiTurnDialogueDataDTO dialogueData;

        private final String thinkContent;

        public ProcessedModelResponse(MultiTurnDialogueDataDTO dialogueData, String thinkContent) {
            this.dialogueData = dialogueData;
            this.thinkContent = thinkContent;
        }

    }

    /**
     * 提取性别和年龄
     *
     * @param data 数据
     * @return {@link String }
     */
    private String extractGenderAndAge(String data) {

        // 更严格的正则表达式，确保匹配格式正确
        String regex = "^\\*?[^,，]+[,，]\\s*([男女])\\s*[,，]\\s*(\\d+岁?)$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(data);

        if (matcher.matches()) {
            String gender = matcher.group(1);
            String age = matcher.group(2);
            return gender + "，" + age;
        }

        return data;
    }

    /**
     * 组装多轮对话内容
     *
     * @param recordDetailList 记录详细信息列表
     * @return {@link List }<{@link HistoryChatDTO }>
     * @Description: massV1.5组装历史对话方法
     */
    private List<HistoryChatDTO> assembleMultiTurnData(List<ShcConversationRecordDetailPO> recordDetailList) {

        List<HistoryChatDTO> historyChatList = new ArrayList<>(recordDetailList.size());
        recordDetailList.forEach(recordDetail -> {
            HistoryChatDTO historyChat = new HistoryChatDTO();
            historyChat.setQuestion(recordDetail.getHealthStatus());
            historyChat.setAnswer(recordDetail.getRecommendations());
            historyChatList.add(historyChat);
        });
        return historyChatList;
    }

    /**
     * 保存记录详细信息
     *
     * @param saveData 保存数据
     */
    public void saveRecordDetail(ShcConversationRecordDetailPO saveData) {

        long recordDetailId = uidGenerator.getUID();
        saveData.setOperateTime(LocalDateTime.now());
        saveData.setRecordDetailId(recordDetailId);
        shcConversationRecordDetailMapper.insert(saveData);
    }


    /**
     * 根据对话记录id查询详细对话信息列表
     *
     * @param recordId 记录 ID
     * @return {@link List }<{@link ShcConversationRecordDetailPO }>
     */
    private List<ShcConversationRecordDetailPO> getShcConversationRecordDetailList(Long recordId) {

        LambdaQueryWrapper<ShcConversationRecordDetailPO> qw = new LambdaQueryWrapper<>();
        qw.eq(ShcConversationRecordDetailPO::getRecordId, recordId);
        qw.orderByAsc(ShcConversationRecordDetailPO::getOperateTime);
        return shcConversationRecordDetailMapper.selectList(qw);
    }

    /**
     * 特殊字符校验
     *
     * @param s 字符串
     * @return boolean
     * <AUTHOR>
     * @date 2024/11/27 14:01
     */
    public boolean containsSpecialCharacter(String s) {
        String[] split = hospitalConfig
                .getTip()
                .getSpecialChars()
                .split(",");
        for (String str : split) {
            if (s.contains(str)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 获取科室列表及号源信息
     *
     * @param departments 模型推荐的科室列表
     * @return List<DeptInfoDTO>
     * <AUTHOR>
     * @date 2024/09/06 10:38
     */
    private List<DeptInfoDTO> getDeptList(List<DeptInfoDTO> departments) {

        List<DeptInfoDTO> deptResultList = null;

        // 院区编码
        List<String> hospitalList = Arrays.asList(hospitalConfig
                .getApi()
                .getHospitalId()
                .split(","));

        // 组织机构科室映射
        CopyOnWriteArrayList<DeptInfoDTO> departmentList = new CopyOnWriteArrayList<>();
        Map<String, String> departmentMap = departments
                .stream()
                .collect(Collectors.toMap(DeptInfoDTO::getDepartment,
                        DeptInfoDTO::getConfidence));
        Map<String, Object> dataMap = Map.of("deptNameList", new ArrayList<>(departmentMap.keySet()), "hospitalList",
                hospitalList);
        QueryOrganizationMapDTO dto = new QueryOrganizationMapDTO();
        dto.setDeptNameList(departmentMap.keySet());
        dto.setHospitalList(hospitalList);
        List<ShcOrganizationMapDTO> shcOrganizationMapList = shcOrganizationMapMapper.getShcOrganizationMapList(
                dataMap);
        // 去重
        shcOrganizationMapList = shcOrganizationMapList
                .stream()
                .collect(collectingAndThen(toCollection(() -> new TreeSet<>(
                                comparing(ShcOrganizationMapDTO::getOrgCode))),
                        ArrayList::new));
        // 使用CompletableFuture进行并行处理
        List<CompletableFuture<DeptInfoDTO>> futures = new ArrayList<>();

        for (ShcOrganizationMapDTO shcOrganizationMap : shcOrganizationMapList) {
            // 急诊科室不需要跳转号源
            DeptInfoDTO deptInfoDTO = new DeptInfoDTO();
            deptInfoDTO.setDepartment(shcOrganizationMap.getOrgName());
            deptInfoDTO.setDeptCode(shcOrganizationMap.getOrgCode());
            deptInfoDTO.setHospitalId(shcOrganizationMap.getHospitalId());
            deptInfoDTO.setTitleName(shcOrganizationMap.getOrgName());
            deptInfoDTO.setCurDeptName(shcOrganizationMap.getGroupName());
            deptInfoDTO.setConfidence(departmentMap.get(shcOrganizationMap.getSource()));
            deptInfoDTO.setGroupCode(shcOrganizationMap.getGroupCode());
            // 急诊("0000")或体检门诊("0001")或sourceEnable为false时不需要跳转号源
            String orgCode = shcOrganizationMap.getOrgCode();

            // 开启查询号源功能
            String bookDeptCode = hospitalConfig
                    .getApi()
                    .getBookDeptCode();
            String offlineDeptCode = hospitalConfig
                    .getApi()
                    .getOfflineDeptCode();
            // 预约科室编码列表
            List<String> bookDeptCodes = ObjectUtils.isEmpty(bookDeptCode) ? Collections.emptyList()
                    : Arrays.asList(bookDeptCode.split(","));

            // 线下科室编码列表
            List<String> offlineDeptCodes = ObjectUtils.isEmpty(offlineDeptCode) ? Collections.emptyList()
                    : Arrays.asList(offlineDeptCode.split(","));

            // 只有在启用号源查询且科室不在排除列表时才执行查询线程
            if (Boolean.TRUE.equals(hospitalConfig
                    .getApi()
                    .getSourceEnable()) && !offlineDeptCodes.contains(
                    deptInfoDTO.getDeptCode()) && !bookDeptCodes.contains(deptInfoDTO.getDeptCode())) {
                // 异步查询号源
                CompletableFuture<DeptInfoDTO> future = CompletableFuture.supplyAsync(
                        () -> querySource(deptInfoDTO, shcOrganizationMap));
                futures.add(future);
            } else {
                deptInfoDTO.setScheduleFlag(false);
                departmentList.add(deptInfoDTO);
            }
        }
        // 等待所有异步任务完成并收集结果
        CompletableFuture
                .allOf(futures.toArray(new CompletableFuture[0]))
                .join();
        futures
                .stream()
                .map(CompletableFuture::join)
                .forEach(departmentList::add);

        // 去重
        List<DeptInfoDTO> distinctDeptList = departmentList
                .stream()
                .collect(Collectors.toMap(DeptInfoDTO::getDeptCode,
                        Function.identity(),
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .toList();
        // 根据置信度排序
        deptResultList = distinctDeptList
                .stream()
                .sorted((o1, o2) -> Double.compare(Double.parseDouble(o2.getConfidence()),
                        Double.parseDouble(o1.getConfidence())))
                .toList();
        // 挂号地址处理
        generateRegisterLink(deptResultList);

        return deptResultList;
    }

    /**
     * 查询号源
     *
     * @param deptInfoDto        部门信息实体
     * @param shcOrganizationMap 组织集合
     * @return {@link DeptInfoDTO }
     */
    private DeptInfoDTO querySource(DeptInfoDTO deptInfoDto, ShcOrganizationMapDTO shcOrganizationMap) {

        BaseAppointmentSourceRequest request = new BaseAppointmentSourceRequest();
        request.setHospitalId(String.valueOf(shcOrganizationMap.getOrganizationMapId()));
        request.setBranchCode(shcOrganizationMap.getHospitalId());
        request.setDeptId(shcOrganizationMap.getOrgCode());
        request.setStartTime(LocalDateTime
                .now()
                .format(DateTimeFormatter.ofPattern(Constants.NORM_DATETIME_PATTERN)));
        request.setEndTime(LocalDateTime
                .now()
                .plusDays(hospitalConfig
                        .getApi()
                        .getFutureDay())
                .format(DateTimeFormatter.ofPattern(Constants.NORM_DATETIME_PATTERN)));
        try {
            List<BaseAppointmentSourceResponse> list = appointmentSourceService.listAppointmentSourceInfo(request);
            log.info("号源查询结果:{}", list);
            if (!ObjectUtils.isEmpty(list)) {
                BaseAppointmentSourceResponse sourceResponse = list.get(0);
                String hosponline = sourceResponse.getHosponline();
                String availableLeftNum = sourceResponse.getAvailableLeftNum();
                deptInfoDto.setScheduleFlag(Integer.getInteger(availableLeftNum) > 0);
            }
            return deptInfoDto;
        } catch (Exception e) {
            log.error("查询号源出现异常:{}", e.getMessage(), e);
            return deptInfoDto;
        }

    }

    /**
     * 查询号源
     *
     * @param departmentList     科室列表
     * @param shcOrganizationMap 院区
     * @param departmentMap      科室
     * @param latch              线程执行计数器
     * <AUTHOR>
     * @date 2024/11/27 13:59
     */
    public void executeThread(List<DeptInfoDTO> departmentList, ShcOrganizationMapDTO shcOrganizationMap,
                              Map<String, String> departmentMap, CountDownLatch latch) {
        threadPoolTaskExecutor.execute(() -> {

            ScheduleReqDTO scheduleReqDTO = new ScheduleReqDTO();
            DeptInfoDTO deptInfoDTO = new DeptInfoDTO();
            boolean isScheduleFlag = false;
            try {
                scheduleReqDTO.setDepartmentCode(shcOrganizationMap.getOrgCode());
                scheduleReqDTO.setTradeCode("1004");
                scheduleReqDTO.setHospitalId(shcOrganizationMap.getHospitalId());
                scheduleReqDTO.setExtUserId("admin");
                scheduleReqDTO.setPatientId("");
                LocalDate currentDate = LocalDate.now();
                String startTime = currentDate.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                String endTime = currentDate
                        .plus(8, ChronoUnit.DAYS)
                        .format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                scheduleReqDTO.setStartDate(startTime);
                scheduleReqDTO.setEndDate(endTime);
                XmlMapper xmlMapper = new XmlMapper();
                log.info("请求参数：{}", xmlMapper.writeValueAsString(scheduleReqDTO));

                String result = HttpClientUtil.callServiceHc(hospitalConfig
                                .getApi()
                                .getScheduleInfoUrl(),
                        WebRequestCodeEnum.CONSULT_SCHEDULE.getCode(), xmlMapper.writeValueAsString(scheduleReqDTO));

                if (StringUtils.isBlank(result)) {
                    throw new ServiceException("接口返参为空!");
                }
                log.info("号源查询接口响应：{}", result);
                ScheduleReqVO scheduleReq = xmlMapper.readValue(result, ScheduleReqVO.class);
                log.info("对返参进行解析：{}", scheduleReq.toString());
                List<ScheduleItemVO> scheduleList = scheduleReq.getScheduleList();
                if (!CollectionUtils.isEmpty(scheduleList)) {
                    for (ScheduleItemVO item : scheduleList) {
                        if ("N".equals(item.getAllBookServise()) && "N".equals(item.getStopRegFlag())
                                && item.getAvailableLeftNum() > 0) {
                            isScheduleFlag = true;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("号源查询接口异常：{}", e.getMessage(), e);
            }
            deptInfoDTO.setDepartment(shcOrganizationMap.getOrgName());
            deptInfoDTO.setDeptCode(shcOrganizationMap.getHospitalId() + "_" + shcOrganizationMap.getGroupCode() + "_"
                    + shcOrganizationMap.getOrgCode());
            deptInfoDTO.setHospitalId(shcOrganizationMap.getHospitalId());
            deptInfoDTO.setTitleName(shcOrganizationMap.getOrgName());
            deptInfoDTO.setCurDeptName(shcOrganizationMap.getGroupName());
            deptInfoDTO.setConfidence(departmentMap.get(shcOrganizationMap.getSource()));
            deptInfoDTO.setScheduleFlag(isScheduleFlag);
            departmentList.add(deptInfoDTO);
            latch.countDown();
        });
    }

    /**
     * 保存对话记录
     *
     * @param consultInfo 对话记录响应信息
     * @param openId      openId
     * @param content     患者对话内容
     * <AUTHOR>
     * @date 2024/09/24 10:38
     */
    public void saveConsultInfo(ConsultInfoVO consultInfo, String openId, String content, String raResult,
                                Long recordId) {
        // 保存对话记录
        LocalDateTime createTime = LocalDateTime.now();
        List<DeptInfoDTO> infoList = consultInfo != null ? consultInfo.getDepartments() : null;
        ShcConversationRecordPO shcConversationRecordPo = new ShcConversationRecordPO();
        shcConversationRecordPo.setRecordId(recordId);
        shcConversationRecordPo.setUserId(openId == null ? Constants.DEFAULT_OPENID : openId);
        shcConversationRecordPo.setStartTime(createTime);
        shcConversationRecordPo.setDepartments(infoList == null ? "" : infoList.toString());
        shcConversationRecordMapper.insertShcConversationRecord(shcConversationRecordPo);
        // 保存对话详细记录
        long recordDetailId = uidGenerator.getUID();
        ShcConversationRecordDetailPO shcConversationRecordDetailPo = new ShcConversationRecordDetailPO();
        shcConversationRecordDetailPo.setRecordId(recordId);
        shcConversationRecordDetailPo.setRecordDetailId(recordDetailId);
        shcConversationRecordDetailPo.setHealthStatus(content);
        if (StringUtils.isNotBlank(raResult)) {
            shcConversationRecordDetailPo.setType(false);
            shcConversationRecordDetailPo.setRecommendations(raResult);
        } else {
            shcConversationRecordDetailPo.setType(true);
            if (!ObjectUtils.isEmpty(consultInfo)) {
                shcConversationRecordDetailPo.setRecommendations(consultInfo.getRecommendations());
                shcConversationRecordDetailPo.setDepartments(infoList == null ? "" : infoList.toString());
            }
        }
        shcConversationRecordDetailPo.setOperateTime(createTime);
        shcConversationRecordDetailMapper.insertShcConversationRecordDetail(shcConversationRecordDetailPo);
    }

    /**
     * 获取知识库返回结果处理
     *
     * @return List<Map < String, Object>>
     * <AUTHOR>
     * @date 2024/10/17 15:00
     */
    public List<KnowledgeBaseInfoVO> getLargeModelResp(boolean isArg, String content, String openId, Long recordId) {

        String knowledgeBase = isArg ? maasV2HttpUtil.getKnowledgeInfo(content, openId, recordId) : null;
        // 如果返参为空时，返给用户固定数据
        if (StringUtils.isBlank(knowledgeBase)) {
            knowledgeBase = hospitalConfig
                    .getTip()
                    .getKnowledgeMessage();
        }
        knowledgeBase = knowledgeBase.replace(")。", ")");
        String[] split = knowledgeBase.split("\n");
        String regex = "\\[(.*?)]\\((.*?)\\)";
        String phoneRegex = "##(.*?)##(.*)";
        List<KnowledgeBaseInfoVO> list = new ArrayList<>();
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        Pattern phonePattern = Pattern.compile(phoneRegex);
        Matcher matcher;
        Matcher phoneMatcher;
        for (String s : split) {
            boolean newLineFlag = knowledgeBase.indexOf(s) != 0;
            // 进行匹配
            matcher = pattern.matcher(s);
            phoneMatcher = phonePattern.matcher(s);
            if (matcher.find()) {
                // 提取URL
                String s1 = matcher.group(0);
                String replace = s.replace(s1, "");
                String title = matcher.group(1);
                String url = matcher.group(2);

                if (StringUtils.isNotEmpty(replace) && !" ".equals(replace)) {
                    addContentInfo(replace, null, "str", newLineFlag, list);
                    newLineFlag = false;
                }
                String type = "btn";
                if (StringUtils.isEmpty(title)) {
                    type = "url";
                    title = url;
                }
                addContentInfo(title, url, type, newLineFlag, list);
            } else if (phoneMatcher.find()) {
                String s1 = phoneMatcher.group(0);
                String phone = phoneMatcher.group(1);
                String strAf = phoneMatcher.group(2);
                String replace = s
                        .replace(s1, "")
                        .replace(strAf, "");

                if (StringUtils.isNotEmpty(replace) && !" ".equals(replace)) {
                    addContentInfo(replace, null, "str", newLineFlag, list);
                    newLineFlag = false;
                }
                addContentInfo(phone, null, "pho", newLineFlag, list);
                if (StringUtils.isNotEmpty(strAf) && !" ".equals(strAf)) {
                    addContentInfo(strAf, null, "str", false, list);
                }
            } else {
                addContentInfo(s, null, "str", newLineFlag, list);
            }
        }
        String finalKnowledgeBase = knowledgeBase;
        saveConsultInfo(null, openId, content, finalKnowledgeBase, recordId);
        return list;
    }

    private void addContentInfo(String content, String url, String type, boolean newLineFlag,
                                List<KnowledgeBaseInfoVO> list) {
        KnowledgeBaseInfoVO contentInfo = new KnowledgeBaseInfoVO();
        contentInfo.setType(type);
        contentInfo.setContent(content);
        contentInfo.setNewLineFlag(newLineFlag);
        contentInfo.setUrl(url);
        list.add(contentInfo);
    }

    /**
     * 获取健康咨询返回信息
     *
     * @param req 健康咨询请求实体
     * @return ConsultInfoVO
     * <AUTHOR>
     * @date 2024/10/17 16:25
     */
    public ConsultInfoVO getLlmConsultInfo(ConsultQO req, String openId, Long recordId) {

        validateRequest(req);
        ConsultInfoVO consultInfo = fetchConsultInfo(req);
        if (consultInfo == null || StringUtils.isBlank(consultInfo.getRecommendations())) {
            return consultInfo;
        }
        processDepartments(consultInfo);
        saveConsultInfoAsync(consultInfo, openId, req.getContent(), recordId);
        return consultInfo;
    }


    /**
     * 验证请求参数
     *
     * @param req 请求参数
     */
    private void validateRequest(ConsultQO req) {

        if (containsSpecialCharacter(req.getContent())) {
            throw new ServiceException("咨询内容不能包含特殊字符!");
        }
    }

    /**
     * 获取咨询信息
     *
     * @param req 要求
     * @return {@link ConsultInfoVO }
     */
    private ConsultInfoVO fetchConsultInfo(ConsultQO req) {
        try {
            Boolean dsFlag = req.getDsFlag();
            String content = req.getContent();
            if (Boolean.TRUE.equals(dsFlag)) {
                return maasV2HttpUtil.getDsTriage(content, null, null);
            } else {
                return maasV2HttpUtil.getTriage(content, null, null);
            }
        } catch (Exception e) {
            log.error("获取推荐结果异常:{} ", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理科室信息
     *
     * @param consultInfo 咨询信息
     */
    private void processDepartments(ConsultInfoVO consultInfo) {
        List<DeptInfoDTO> departments = consultInfo.getDepartments();
        if (CollectionUtils.isEmpty(departments)) {
            setDefaultDepartmentResponse(consultInfo);
            return;
        }
        List<DeptInfoDTO> deptList = getDeptList(departments);
        if (CollectionUtils.isEmpty(deptList)) {
            setDefaultDepartmentResponse(consultInfo);
        } else {
            consultInfo.setDepartments(deptList);
        }
    }

    /**
     * 设置默认科室信息
     *
     * @param consultInfo 咨询信息
     */
    private void setDefaultDepartmentResponse(ConsultInfoVO consultInfo) {
        consultInfo.setDepartments(null);
        consultInfo.setDeptFlag(false);
        consultInfo.setMessage(hospitalConfig
                .getTip()
                .getMessage());
    }

    /**
     * 异步保存咨询记录
     *
     * @param consultInfo 咨询信息
     * @param openId      openId
     * @param content     内容
     * @param recordId    记录 ID
     */
    private void saveConsultInfoAsync(ConsultInfoVO consultInfo, String openId, String content, Long recordId) {
        CompletableFuture.runAsync(() -> saveConsultInfo(consultInfo, openId, content, null, recordId));
    }

    /**
     * 根据医院科室生成挂号链接
     *
     * @param deptList 医院科室列表
     */
    public void generateRegisterLink(List<DeptInfoDTO> deptList) {

        deptList.forEach(dept -> {
            // 构建参数Map
            Map<String, String> params = new HashMap<>(8);

            BeanMap beanMap = BeanMap.create(dept);

            for (Object key : beanMap.keySet()) {
                Object value = beanMap.get(key);
                if (value != null) {
                    params.put(String.valueOf(key), java.lang.String.valueOf(value));
                } else {
                    params.put(String.valueOf(key), "");
                }
            }
            params.putAll(hospitalConfig
                    .getRegister()
                    .getDefaultParams());
            String registerUrl = buildUrl(params);
            dept.setRegisterUrl(registerUrl);
        });

    }


    /**
     * 根据参数和模板构建URL
     *
     * @param params 参数Map
     * @return 完整的URL
     */
    private String buildUrl(Map<String, String> params) {

        String urlTemplate = hospitalConfig
                .getRegister()
                .getUrlTemplate();
        String baseUrl = hospitalConfig
                .getRegister()
                .getBaseUrl();

        // 替换占位符
        Matcher matcher = placeholderPattern.matcher(urlTemplate);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            String paramName = matcher.group(1);
            String paramValue = params.getOrDefault(paramName, "");

            // 根据配置决定编码策略
            String encodingStrategy = hospitalConfig
                    .getRegister()
                    .getEncoding()
                    .getOrDefault(paramName, Constants.ENCODE_NONE);
            String encodedValue = encodeValue(paramValue, encodingStrategy);

            matcher.appendReplacement(sb, encodedValue);
        }
        matcher.appendTail(sb);
        String baseUrlEncode = hospitalConfig
                .getRegister()
                .getBaseUrlEncode();
        String urlTemplateEncode = hospitalConfig
                .getRegister()
                .getUrlTemplateEncode();
        String baseUrlEncodeValue = encodeValue(baseUrl, baseUrlEncode);
        String urlTemplateEncodeValue = encodeValue(sb.toString(), urlTemplateEncode);
        return baseUrlEncodeValue + urlTemplateEncodeValue;
    }

    /**
     * 根据编码策略对值进行编码
     *
     * @param value    原始值
     * @param strategy 编码策略
     * @return 编码后的值
     */
    private String encodeValue(String value, String strategy) {

        if (ObjectUtils.isEmpty(value)) {
            return "";
        }

        return switch (strategy) {
            // 全部编码
            case Constants.ENCODE_FULL -> URLEncodeUtil.encodeAll(value);
            // 对特殊字符不编码
            case Constants.ENCODE_PARTIAL -> URLEncodeUtil.encode(value);
            case Constants.ENCODE_FRAGMENT -> URLEncodeUtil.encodeFragment(value);
            // 不编码
            default -> value;
        };
    }

    /**
     * 获取伴随症状
     *
     * @param text 请求参数
     * @return {@link List }<{@link String }>
     */
    private List<String> getSymptoms(String text) {

        log.info("调用伴随症状接口");
        String url = accompanySymptomsConfig.getUrl();
        JSONObject requestJson = new JSONObject();
        text = text.replace("不能再推荐人体图", "");
        requestJson.put("text", text);
        String result = HttpUtil
                .createPost(url)
                .body(requestJson.toJSONString())
                .execute()
                .body();
        log.info("查询伴随症状请求参数：{}，响应结果:{}", text, result);
        if (ObjectUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        SymptomResponseDTO symptomResponseDto = JsonUtils.parseObject(result, SymptomResponseDTO.class);
        List<String> missingSymptomList = symptomResponseDto.getMissingSymptoms();
        if (missingSymptomList.size() > 6) {
            return missingSymptomList.subList(0, 5);
        }
        return missingSymptomList;
    }

}
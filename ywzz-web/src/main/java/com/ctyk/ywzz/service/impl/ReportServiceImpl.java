package com.ctyk.ywzz.service.impl;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.fsg.uid.UidGenerator;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.common.json.utils.JsonUtils;
import com.ctyk.ywzz.config.HospitalConfigProperties;
import com.ctyk.ywzz.config.ReportInterpretationConfig;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.core.ReportTest;
import com.ctyk.ywzz.entity.dto.ReportDetailReqDTO;
import com.ctyk.ywzz.entity.dto.ReportListDTO;
import com.ctyk.ywzz.entity.dto.ReportListReqDTO;
import com.ctyk.ywzz.entity.dto.ReportParseResultDTO;
import com.ctyk.ywzz.entity.dto.UploadPdfResultDTO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordDetailPO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordPO;
import com.ctyk.ywzz.entity.vo.InspectSuggestVO;
import com.ctyk.ywzz.entity.vo.ParsePdfReportVO;
import com.ctyk.ywzz.entity.vo.ReportBaseInfoVO;
import com.ctyk.ywzz.entity.vo.ReportDetailRespVO;
import com.ctyk.ywzz.entity.vo.ReportDetailVo;
import com.ctyk.ywzz.entity.vo.ReportListRespVO;
import com.ctyk.ywzz.entity.vo.ReportListVO;
import com.ctyk.ywzz.entity.vo.VisitUserVO;
import com.ctyk.ywzz.entity.vo.WebReportInfoVO;
import com.ctyk.ywzz.mapper.ShcConversationRecordDetailMapper;
import com.ctyk.ywzz.mapper.ShcConversationRecordMapper;
import com.ctyk.ywzz.service.ReportService;
import com.ctyk.ywzz.service.VisitService;
import com.ctyk.ywzz.util.JsonUtil;
import com.ctyk.ywzz.util.UserUtil;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: Liyh
 * @date: 2024/07/17 16:57
 * @description: 体检报告ServiceImpl
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Service
public class ReportServiceImpl implements ReportService {
    
    @Resource
    private VisitService visitService;
    
    private static List<ReportBaseInfoVO> reportBaseInfoList = new ArrayList<>(9);
    
    private static List<InspectSuggestVO> reportInspectSuggestList = new ArrayList<>();
    
    @Resource
    private HospitalConfigProperties hospitalConfig;
    
    @Resource
    private LlmClientService llmClientService;
    
    @Resource
    private HttpServletRequest request;
    
    @Resource
    private RedisService redisService;
    
    @Resource
    private UidGenerator uidGenerator;
    
    @Resource
    private ShcConversationRecordMapper recordMapper;
    
    @Resource
    private ShcConversationRecordDetailMapper recordDetailMapper;
    
    @Resource
    private ReportInterpretationConfig reportInterpretationConfig;
    
    @Resource
    private WebClient.Builder webClientBuilder;
    
    /**
     * 获取报告列表
     *
     * @param reportListReqDTO 报告列表请求实体
     * @return {@link Map }<{@link String }, {@link List }<{@link ReportListVO }>>
     */
    @Override
    public Map<String, List<ReportListVO>> getReportList(ReportListReqDTO reportListReqDTO) {
        Map<String, List<ReportListVO>> sortMap = null;
        try {
            XmlMapper xmlMapper = new XmlMapper();
            log.info("请求参数：{}", xmlMapper.writeValueAsString(reportListReqDTO));
            
            List<String> patientNoList = new ArrayList<>(10);
            if (StringUtils.isNotBlank(reportListReqDTO.getPatientNo())) {
                patientNoList.add(reportListReqDTO.getPatientNo());
            } else {
                patientNoList = visitService.getVisitUserList(reportListReqDTO.getOpenId())
                                            .stream()
                                            .map(VisitUserVO::getPatientNo)
                                            .toList();
            }
            List<ReportListVO> reportList = new ArrayList<>(10);
            ReportListDTO reportListReq = new ReportListDTO();
            reportListReq.setIdCard(reportListReqDTO.getIdCard());
            for (String patientNo : patientNoList) {
                reportListReq.setPatientNo(patientNo);
                //                String result = WebServiceClientUtil.callWebservice(reportUrl, WebRequestCodeEnum.REPORT_BASIC.getCode(), xmlMapper.writeValueAsString(reportListReqDTO));
                /*String result = HttpClientUtil.callServiceHc(hospitalConfig.getApi().getReportUrl(),
                        WebRequestCodeEnum.REPORT_BASIC.getCode(), xmlMapper.writeValueAsString(reportListReq));*/
                String result = """
                            <Response><ResultCode>0</ResultCode><ResultDesc>成功</ResultDesc><ResultList><PeRecord><name>方*玉</name><sex>男</sex><age>27</age><examDate>2024-01-17</examDate><company>北京大学第一医院</company><admId>7995142</admId><reportStatus>已取</reportStatus><peStatus>到达</peStatus><preDate>2024-01-17</preDate><orderName>tj01-本院44(男)</orderName><HPNo>PTAAAA0665</HPNo></PeRecord></ResultList></Response>
                        """;
                if (StringUtils.isBlank(result)) {
                    continue;
                }
                log.info("体检报告列表接口响应：{}", result);
                ReportListRespVO reportResponse = xmlMapper.readValue(result, ReportListRespVO.class);
                log.info("报告详情解析结果：{}", reportResponse);
                if (reportResponse.getResultCode() == 0) {
                    for (WebReportInfoVO webReport : reportResponse.getReportInfoList()) {
                        ReportListVO report = new ReportListVO();
                        BeanUtils.copyProperties(webReport, report);
                        reportList.add(report);
                    }
                } else {
                    // 失败
                    // throw new CustomException(reportResponse.getResultDesc());
                }
            }
            if (!reportList.isEmpty()) {
                // 根据体检日期排序分组
                Map<Date, List<ReportListVO>> treeMap = new TreeMap<>(Comparator.reverseOrder());
                treeMap.putAll(reportList.stream()
                                         .collect(Collectors.groupingBy(ReportListVO::getExamDate)));
                SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.NORM_DATE_PATTERN);
                sortMap = new LinkedHashMap<>(treeMap.size());
                for (Map.Entry<Date, List<ReportListVO>> entry : treeMap.entrySet()) {
                    sortMap.put(sdf.format(entry.getKey()), entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("查询报告列表发生异常：{}", e.getMessage(), e);
            throw new ServiceException("查询报告失败!");
        }
        return sortMap;
    }
    
    /**
     * 查询报告基本信息
     * <AUTHOR>
     * @date 2024/07/30 17:28
     * @param reportId 报告ID
     * @return ReportBaseInfoVO
     */
    @Override
    public ReportBaseInfoVO getReportBaseInfo(String reportId) {
        
        ReportBaseInfoVO reportBaseInfo = null;
        for (ReportBaseInfoVO item : reportBaseInfoList) {
            if (reportId.equals(item.getReportId())) {
                reportBaseInfo = item;
                break;
            }
        }
        return reportBaseInfo;
    }
    
    /**
     * 查询报告总检建议
     * <AUTHOR>
     * @date 2024/07/30 17:28
     * @param reportId 报告ID
     * @return InspectSuggestVO
     */
    @Override
    public InspectSuggestVO getReportInspectSuggest(String reportId) {
        
        InspectSuggestVO inspectSuggest = null;
        for (InspectSuggestVO item : reportInspectSuggestList) {
            if (reportId.equals(item.getReportId())) {
                inspectSuggest = item;
                break;
            }
        }
        return inspectSuggest;
    }
    
    /**
     * 获取报告详细信息
     *
     * @param reportDetailReq 报告详细信息
     * @return {@link ReportDetailVo }
     */
    @Override
    public ReportDetailVo getReportDetailInfo(ReportDetailReqDTO reportDetailReq) {
        
        ReportDetailVo reportDetail = new ReportDetailVo();
        try {
            XmlMapper xmlMapper = new XmlMapper();
            log.info("【查询报告详情】接口请求参数：{}", xmlMapper.writeValueAsString(reportDetailReq));
            //             String result = WebServiceClientUtil.callWebservice(reportUrl, WebRequestCodeEnum.REPORT_BASIC.getCode(), xmlMapper.writeValueAsString(reportDetailReq));
           /* String result = HttpClientUtil.callServiceHc(hospitalConfig.getApi().getReportUrl(),
                    WebRequestCodeEnum.REPORT_DETAIL.getCode(), xmlMapper.writeValueAsString(reportDetailReq));*/
            String result = ReportTest.reportDetailMap.get(reportDetailReq.getAdmId());
            //            Thread.sleep(700);
            if (StringUtils.isBlank(result)) {
                throw new ServiceException("接口返参为空!");
            }
            log.info("【查询报告详情】接口返参：{}", result);
            ReportDetailRespVO reportResponse = xmlMapper.readValue(result, ReportDetailRespVO.class);
            log.info("对返参进行解析：{}", reportResponse);
            if (reportResponse.getResultCode() == 0 && reportResponse.getReportDetail()
                                                                     .size() == 1) {
                // 成功
                BeanUtils.copyProperties(reportResponse.getReportDetail()
                                                       .get(0), reportDetail);
                reportDetail.setAbnormalIndex(reportDetail.getSummaryItemList()
                                                          .size());
            } else {
                // 失败
                throw new ServiceException(reportResponse.getResultDesc());
            }
        } catch (Exception e) {
            log.error("查询报告列表发生异常：{}", e.getMessage(), e);
            throw new ServiceException("查询报告失败！");
        }
        return reportDetail;
    }
    
    /**
     * 上传 PDF
     *
     * @param file 文件
     * @return {@link String }
     */
    @Override
    public String uploadPdf(MultipartFile file) {
        
        // 基础参数设置
        String redisKeyPrefix = "report_result:";
        checkPdfFile(file);
        
        // 获取用户信息和文件信息
        String originalFilename = file.getOriginalFilename();
        String openId = ObjectUtils.isEmpty(request.getHeader(Constants.USER_ID)) ? Constants.DEFAULT_OPENID
                : request.getHeader(Constants.USER_ID);
        
        // 生成Redis键
        String key = openId + originalFilename;
        String redisKey = redisKeyPrefix + DigestUtil.md5Hex(key);
        
        // 检查缓存，避免重复处理
        if (redisService.hasKey(redisKey)) {
            log.info("发现缓存结果，直接返回, openId: {}, fileName: {}", openId, originalFilename);
            return redisKey;
        }
        
        // 读取文件内容到内存
        byte[] fileBytes = readFileBytes(file);
        
        // 创建初始结果
        ReportParseResultDTO initialDto = new ReportParseResultDTO();
        initialDto.setRecordId(uidGenerator.getUID());
        initialDto.setParseResult(Constants.REPORT_READING);
        redisService.setEx(redisKey, JsonUtil.toJsonString(initialDto), reportInterpretationConfig.getCacheTime());
        
        // 定义异步任务
        Runnable processingTask = () -> processFileAsync(fileBytes, originalFilename, openId, redisKey,
                initialDto.getRecordId());
        
        // 执行异步任务并只处理5秒内的异常
        try {
            executeWithTimeWindowExceptionHandling(processingTask);
        } catch (Exception e) {
            // 5秒内出现的异常会被捕获并传递到这里
            log.error("PDF处理任务在5秒内失败: openId:{}, fileName:{}, error:{}", openId, originalFilename,
                    e.getMessage(), e);
            
            // 删除Redis中的处理中状态
            redisService.del(redisKey);
            // 向前端抛出异常
            throw new ServiceException(e.getMessage());
        }
        
        // 返回Redis键，前端可以使用此键查询处理状态
        return redisKey;
    }
    
    /**
     * 执行跟时间窗异常处理；辅助方法：仅捕获5秒内的异常
     * 这里处理逻辑主要是检查和大模型网络连通、文件类型问题
     *
     * @param task 任务
     */
    private void executeWithTimeWindowExceptionHandling(Runnable task) {
        
        CompletableFuture<Void> future = CompletableFuture.runAsync(task);
        
        try {
            // 只等待5秒，5秒内的异常会被捕获
            future.get(5, TimeUnit.SECONDS);
        } catch (TimeoutException | IORuntimeException e) {
            // 5秒后超时，我们不关心后续异常
            log.info("任务执行超过5秒，不再监控异常");
        } catch (ExecutionException e) {
            // 捕获5秒内的执行异常
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            } else {
                throw new ServiceException("服务执行异常: " + cause.getMessage());
            }
        } catch (InterruptedException e) {
            Thread.currentThread()
                  .interrupt();
            throw new ServiceException("服务被中断");
        }
    }
    
    /**
     * 读取文件字节，处理可能的IO异常
     *
     * @param file 文件
     * @return {@link byte[] }
     */
    private byte[] readFileBytes(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (IOException e) {
            log.error("读取上传文件内容失败: {}", e.getMessage(), e);
            throw new ServiceException("上传报告文件失败");
        }
    }
    
    /**
     * 异步处理文件的核心逻辑
     *
     * @param fileBytes 文件字节
     * @param fileName 文件名
     * @param openId openId
     * @param redisKey Redis 键
     * @param recordId 记录 ID
     */
    private void processFileAsync(byte[] fileBytes, String fileName, String openId, String redisKey, long recordId) {
        
        log.info("开始异步处理PDF报告: openId: {}, fileName: {}", openId, fileName);
        Map<String, String> headers = new HashMap<>();
        String uploadResult = "";
        
        try {
            // 上传文件到LLM服务
            uploadResult = llmClientService.uploadPdfContent(fileBytes, fileName, headers);
            log.info("上传PDF返回结果: openId: {}, fileName: {}, result: {}", openId, fileName, uploadResult);
            
            // 异常 {"detail":"400: 您上传的文件不是有效的体检报告"}
            // 处理大模型上传接口并发异常
            if (HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()
                                                .equals(uploadResult)) {
                log.warn("大模型服务返回内部错误: openId: {}, fileName: {}", openId, fileName);
                saveErrorToRedis(redisKey, "服务繁忙，请稍后重试");
                return;
            }
            
            // 解析返回结果
            UploadPdfResultDTO resultDto = JsonUtil.parseObject(uploadResult, UploadPdfResultDTO.class);
            // 先判断是否有错
            if (!ObjectUtils.isEmpty(resultDto) && !ObjectUtils.isEmpty(resultDto.getDetail())) {
                String errorMessage = resultDto.getDetail()
                                               .replace("400: ", "");
                saveErrorToRedis(redisKey, errorMessage);
                return;
            }
            String finalResult = resultDto.getFinalResult();
            
            // 构建并保存成功结果
            ReportParseResultDTO dto = new ReportParseResultDTO();
            dto.setRecordId(recordId);
            dto.setParseResult(finalResult);
            
            // 存储成功结果到Redis
            redisService.setEx(redisKey, JsonUtil.toJsonString(dto), reportInterpretationConfig.getCacheTime());
            log.info("PDF报告处理成功并保存到Redis: openId: {}, fileName: {}", openId, fileName);
            
        } catch (IORuntimeException e) {
            log.error("上传PDF文件失败: openId: {}, fileName: {}, error:{}", openId, fileName, e.getMessage(), e);
            throw new ServiceException(Constants.CALL_SERVICE_ERROR);
        } catch (Exception e) {
            log.error("上传PDF文件失败: openId: {}, fileName: {}, error:{}", openId, fileName, e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            saveRecord(openId, fileName, uploadResult, recordId);
        }
    }
    
    /**
     * 将错误保存到 Redis
     *
     * @param redisKey Redis 键
     * @param errorMessage 错误信息
     */
    private void saveErrorToRedis(String redisKey, String errorMessage) {
        ReportParseResultDTO errorDto = new ReportParseResultDTO();
        errorDto.setParseResult(errorMessage);
        redisService.setEx(redisKey, JsonUtil.toJsonString(errorDto), reportInterpretationConfig.getCacheTime());
    }
    
    /**
     * 获取报告解析结果
     *
     * @param parseId 解析 ID
     * @return {@link ReportParseResultDTO }
     */
    @Override
    public ReportParseResultDTO getReportParseResult(String parseId) {
        
        if (redisService.hasKey(parseId)) {
            String jsonValue = redisService.getString(parseId);
            ReportParseResultDTO parseResultDto = JSON.parseObject(jsonValue, ReportParseResultDTO.class);
            if (Constants.REPORT_READING.equals(parseResultDto.getParseResult())) {
                return null;
            }
            return parseResultDto;
        }
        return null;
    }
    
    /**
     * 上传PDF报告并解析
     *
     * @param file 文件
     * @return {@link Flux }<{@link ParsePdfReportVO }>
     */
    @Override
    public Flux<ParsePdfReportVO> uploadPdfAndParse(MultipartFile file) {
        
        log.info("接收到请求:{}", file.getOriginalFilename());
        long recordId = uidGenerator.getUID();
        String recordIdMessage = "recordId:" + recordId;
        String userId = UserUtil.getUserIdOrDefault();
        String originalFilename = file.getOriginalFilename();
        String key = userId + originalFilename;
        String redisKeyPrefix = "report_result:";
        String redisKey = redisKeyPrefix + DigestUtil.md5Hex(key);
        AtomicReference<Boolean> saveCacheFlag = new AtomicReference<>(Boolean.FALSE);
        // 缓存中获取
        if (redisService.hasKey(redisKey)) {
            String reportParseResult = redisService.getString(redisKey);
            log.debug("文件名：{} redis中获取的数据:{}", originalFilename, reportParseResult);
            return Flux.just(new ParsePdfReportVO(true, null, reportParseResult),
                    new ParsePdfReportVO(true, null, recordIdMessage),
                    new ParsePdfReportVO(true, null, Constants.SSE_END));
        }
        StringBuilder fullResponseForCache = new StringBuilder();
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        // 添加文件
        body.add("file", file.getResource());
        body.add("stream", true);
        
        // 实时获取
        return webClientBuilder.build()
                               .post()
                               .uri(reportInterpretationConfig.getUploadUrl())
                               .contentType(MediaType.MULTIPART_FORM_DATA)
                               .body(BodyInserters.fromMultipartData(body))
                               .retrieve()
                               .bodyToFlux(String.class) // 流式接收
                               .doOnNext(line -> {
                                   // 不拼接最后的
                                   if (!Constants.SSE_END.equals(line)) {
                                       fullResponseForCache.append(line);
                                   }
                               })
                               .flatMap(line -> {
                                   log.debug("响应结果: {}", line);
                                   // 空行直接透传
                                   if (ObjectUtils.isEmpty(line)) {
                                       return Flux.just(new ParsePdfReportVO(true, null, line));
                                   }
                                   // 处理返回的数据
                                   try {
                                       // json格式数据是模型接口返回的错误信息
                                       if (JSONUtil.isTypeJSON(line) && !Constants.SSE_END.equals(line)) {
                                           String detail = JsonUtils.parseMap(line)
                                                                    .get("detail")
                                                                    .toString();
                                           // 手动添加结束标识符，与原逻辑保持一致
                                           return Flux.just(new ParsePdfReportVO(false, detail, null),
                                                   new ParsePdfReportVO(true, null, Constants.SSE_END));
                                       } else {
                                           // 含有错误信息 格式 [ERROR] xxxx
                                           if (line.contains(Constants.REPORT_ERROR)) {
                                               int errorIndex = line.indexOf(Constants.REPORT_ERROR);
                                               String errorMessage = line.substring(
                                                       errorIndex + Constants.REPORT_ERROR.length());
                                               return Flux.just(new ParsePdfReportVO(false, errorMessage, null));
                                           }
                                           // 结束标识,结束前推送recodeId
                                           else if (line.contains(Constants.SSE_END)) {
                                               return Flux.just(new ParsePdfReportVO(true, null, recordIdMessage),
                                                       new ParsePdfReportVO(true, null, Constants.SSE_END));
                                           }
                                           // 正常的数据流
                                           else {
                                               saveCacheFlag.set(true);
                                               return Flux.just(
                                                       new ParsePdfReportVO(true, null, line.replace("data:", "")));
                                           }
                                       }
                                   } catch (Exception e) {
                                       log.error("解析JSON失败: {}", line, e);
                                       // 某一行的异常
                                       return Flux.error(new ServiceException("解读失败"));
                                   }
                               })
                               .doOnComplete(() -> {
                                   // 流处理成功结束后，缓存完整结果
                                   String finalResult = fullResponseForCache.toString();
                                   if (Boolean.TRUE.equals(saveCacheFlag.get())) {
                                       redisService.setEx(redisKey, finalResult,
                                               reportInterpretationConfig.getCacheTime());
                                       log.info("报告解读结果已缓存，Key: {}", redisKey);
                                   }
                               })
                               .onErrorResume(e -> {
                                   // 捕获流中的任何错误
                                   log.error("上传或解析PDF文件失败: {}", e.getMessage(), e);
                                   // 向前端发送一个表示失败的 VO 对象，并正常关闭流
                                   return Flux.just(new ParsePdfReportVO(false, "解读失败", null));
                               });
    }
    
    /**
     * 检查PDF文件
     *
     */
    private void checkPdfFile(MultipartFile file) {
        
        // 验证是否为空文件
        if (file.isEmpty()) {
            throw new ServiceException("文件不能为空!");
        }
        
        // 验证文件类型
        String contentType = file.getContentType();
        if (!Constants.CONTENT_TYPE_PDF.equals(contentType)) {
            throw new ServiceException("只允许上传PDF格式的文件!");
        }
    }
    
    /**
     * 保存记录
     *
     * @param openId openId
     * @param originalFilename 原始文件名
     * @param recommendations 建议
     * @param recordId 记录 ID
     */
    private void saveRecord(String openId, String originalFilename, String recommendations, Long recordId) {
        
        try {
            LocalDateTime dateTime = LocalDateTime.now();
            
            // 保存会话记录
            ShcConversationRecordPO recordInfo = new ShcConversationRecordPO();
            recordInfo.setRecordId(recordId);
            recordInfo.setStartTime(dateTime);
            recordInfo.setUserId(openId);
            recordMapper.insert(recordInfo);
            
            // 保存会话详情
            ShcConversationRecordDetailPO recordDetail = new ShcConversationRecordDetailPO();
            recordDetail.setRecordId(recordId);
            recordDetail.setRecordDetailId(uidGenerator.getUID());
            recordDetail.setType(false);
            recordDetail.setRecommendations(recommendations);
            recordDetail.setHealthStatus(originalFilename);
            recordDetail.setOperateTime(dateTime);
            recordDetailMapper.insert(recordDetail);
        } catch (Exception e) {
            log.error("保存记录失败", e);
        }
    }
}

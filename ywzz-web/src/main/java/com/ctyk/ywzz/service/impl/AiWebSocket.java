package com.ctyk.ywzz.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: zwj
 * @date: 2025/06/05  09:33
 * @description: AI公司语音合成WebSocket客户端
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
public class AiWebSocket extends WebSocketClient {
    
    // 用于收集音频数据的缓冲区
    private final ByteArrayOutputStream audioBuffer;
    
    private final String type;
    
    // 回调函数
    private Consumer<byte[]> ttsResultCallback;
    
    private Consumer<String> asrResultCallback;
    
    // 当前请求ID
    private String currentRequestId;
    
    // 文本内容（用于TTS）
    private String textContent;
    
    // PCM音频数据（用于ASR）
    private byte[] pcmAudioData;
    
    // ASR识别结果收集器
    private final StringBuilder asrTextCollector;
    
    // 是否已经收到res_status=4的消息
    private boolean receivedStatus4 = false;
    
    // 是否已经触发回调
    private boolean callbackTriggered = false;
    
    private String voice;
    
    private String format = "pcm";
    
    private int sampleRate = 16000;
    
    private int volume = 50;
    
    private float speechRate = 1.0f;
    
    private float pitch = 1.0f;
    
    public AiWebSocket(URI serverUri, Map<String, String> headers, String type, String voice) {
        super(serverUri, headers);
        this.audioBuffer = new ByteArrayOutputStream();
        this.type = type;
        this.currentRequestId = UUID.randomUUID().toString();
        this.asrTextCollector = new StringBuilder();
        this.voice = ObjectUtils.isEmpty(voice) ? "yixiaobei" : voice;
    }
    
    /**
     * 设置语音合成参数
     * @param voice 说话人
     * @param format 音频格式
     * @param sampleRate 采样率
     * @param volume 音量
     * @param speechRate 语速
     * @param pitch 音调
     */
    public void setParams(String voice, String format, int sampleRate, int volume, float speechRate, float pitch) {
        this.voice = voice;
        this.format = format;
        this.sampleRate = sampleRate;
        this.volume = volume;
        this.speechRate = speechRate;
        this.pitch = pitch;
    }
    
    /**
     * 设置TTS结果回调
     * @param callback 回调函数
     */
    public void setTtsResultCallback(Consumer<byte[]> callback) {
        this.ttsResultCallback = callback;
    }
    
    /**
     * 设置ASR结果回调
     * @param callback 回调函数
     */
    public void setAsrResultCallback(Consumer<String> callback) {
        this.asrResultCallback = callback;
    }
    
    /**
     * 发送TTS文本
     * @param text 要合成的文本
     */
    public void sendTtsText(String text) {
        this.textContent = text;
        log.info("设置TTS文本内容: {}", text);
        
        if (isOpen()) {
            log.info("WebSocket已连接，立即发送TTS请求");
            sendTtsRequest();
        } else {
            log.info("WebSocket未连接，等待连接建立后发送");
        }
    }
    
    /**
     * 发送ASR音频数据
     * @param pcmData PCM音频数据
     */
    public void sendAsrAudio(byte[] pcmData) {
        this.pcmAudioData = pcmData;
        // 清空之前的识别结果收集器
        asrTextCollector.setLength(0);
        if (isOpen()) {
            sendAsrAudioData();
        }
    }
    
    /**
     * 连接建立后发送初始化请求
     */
    @Override
    public void onOpen(ServerHandshake handshakedata) {
        log.info("WebSocket连接已建立，类型: {}", type);
        
        switch (type) {
            // case "2202057":
            case "2202094":
                log.info("TTS WebSocket连接就绪");
                sendTtsRequest();
                break;
            // 实时语音识别普通话版
            case "2202068":
                sendAsrRequest();
                break;
            default:
                break;
        }
        
    }
    
    private void sendTtsRequest() {
        // 发送消息
        try {
            // 根据接口文档构建请求参数
            JSONObject initSend = new JSONObject();
            // 必需参数
            initSend.put("req_id", currentRequestId);
            initSend.put("text", textContent);
            //  此参数有默认值，不添加
            // initSend.put("format", format);
            initSend.put("sample_rate", sampleRate);
            initSend.put("voice", voice);
            initSend.put("volume", volume);
            initSend.put("speech_rate", speechRate);
            initSend.put("pitch", pitch);
            
            log.info("发送语音合成请求: {}", initSend.toJSONString());
            log.debug("TTS文本内容: {}", textContent);
            
            send(initSend.toJSONString());
        } catch (Exception e) {
            log.error("发送TTS请求失败", e);
        }
    }
    
    private void sendAsrRequest() {
        try {
            // 初始化连接消息
            JSONObject initSend = new JSONObject();
            initSend.put("req_id", currentRequestId);
            initSend.put("rec_status", 0);
            JSONObject option = new JSONObject();
            option.put("sample_rate", 16000);
            option.put("enable_punctuation", true);
            option.put("enable_inverse_text_normalization", true);
            initSend.put("option", option);
            send(initSend.toJSONString());
            log.info("发送ASR初始化请求: {}", initSend.toJSONString());
        } catch (Exception e) {
            log.error("发送ASR初始化请求失败", e);
        }
    }
    
    /**
     * 发送ASR音频数据
     */
    private void sendAsrAudioData() {
        try {
            if (pcmAudioData == null || pcmAudioData.length == 0) {
                log.warn("PCM音频数据为空");
                return;
            }
            
            // 将音频数据分块发送， 每次发送1600字节
            List<byte[]> chunkedList = chunkByteArray(pcmAudioData, 1600);
            JSONObject audioSend = new JSONObject();
            
            log.info("开始发送语音片段，总大小: {} bytes, 分块数: {}", pcmAudioData.length, chunkedList.size());
            
            for (int i = 0; i < chunkedList.size(); i++) {
                byte[] chunked = chunkedList.get(i);
                String base64 = Base64.getEncoder().encodeToString(chunked);
                audioSend.put("rec_status", 1);
                audioSend.put("audio_stream", base64);
                audioSend.put("req_id", currentRequestId);
                send(audioSend.toJSONString());
                
                if (i % 10 == 0) { // 每10个分块打印一次日志
                    log.debug("发送进度: {}/{}", i + 1, chunkedList.size());
                }
                
                // 添加小延迟避免发送过快
                Thread.sleep(50);
            }
            
            // 发送结束消息
            JSONObject endSend = new JSONObject();
            endSend.put("rec_status", 2);
            endSend.put("req_id", currentRequestId);
            send(endSend.toJSONString());
            log.info("语音片段发送结束: {}", endSend.toJSONString());
            
        } catch (Exception e) {
            log.error("发送ASR音频数据失败", e);
        }
    }
    
    /**
     * 将byte数组按照指定长度截断并存放在list中
     *
     * @param originalArray 原始byte数组
     * @param chunkSize     每个截断部分的长度
     * @return 包含截断后byte数组的list
     */
    public static List<byte[]> chunkByteArray(byte[] originalArray, int chunkSize) {
        
        List<byte[]> chunkedList = new ArrayList<>();
        
        if (originalArray == null || chunkSize <= 0) {
            throw new IllegalArgumentException("Invalid input parameters");
        }
        
        for (int i = 0; i < originalArray.length; i += chunkSize) {
            int remainingLength = originalArray.length - i;
            int lengthToCopy = Math.min(chunkSize, remainingLength);
            
            byte[] chunk = new byte[lengthToCopy];
            System.arraycopy(originalArray, i, chunk, 0, lengthToCopy);
            
            chunkedList.add(chunk);
        }
        
        return chunkedList;
    }
    
    @Override
    public void onMessage(String message) {
        
        // log.debug("接收到消息：{}", message);
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            if (jsonObject.containsKey("result") && jsonObject.getJSONObject("result").containsKey("audio")) {
                handleTtsResponse(jsonObject);
            }
            
            // 处理ASR响应（语音识别）- 新格式
            if (jsonObject.containsKey("code") && jsonObject.containsKey("data") && jsonObject.containsKey(
                    "res_status")) {
                handleAsrResponse(jsonObject);
            }
        } catch (Exception e) {
            log.error("解析消息出错: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理TTS响应
     */
    private void handleTtsResponse(JSONObject jsonObject) {
        try {
            JSONObject result = jsonObject.getJSONObject("result");
            String audioBase64 = result.getString("audio");
            boolean isEnd = result.getBooleanValue("is_end");
            int audioLen = result.getIntValue("audio_len");
            
            // 解码Base64音频数据
            byte[] audioData = Base64.getDecoder().decode(audioBase64);
            
            // 将音频数据添加到缓冲区
            audioBuffer.write(audioData);
            
            log.debug("接收音频数据: 长度={}, 是否结束={}", audioLen, isEnd);
            
            // 如果是最后一段音频，触发回调
            if (isEnd) {
                byte[] completeAudio = audioBuffer.toByteArray();
                log.info("TTS完成，总音频大小: {} bytes", completeAudio.length);
                
                // 保存音频文件（调试用）
                // saveAudioData();
                
                // 触发回调
                if (ttsResultCallback != null) {
                    ttsResultCallback.accept(completeAudio);
                }
            }
            
        } catch (Exception e) {
            log.error("处理TTS响应失败", e);
        }
    }
    
    /**
     * 处理ASR响应（新格式）
     * 根据 res_status 值处理不同阶段的识别结果：
     * 0：识别就绪
     * 1：识别到有效语音开始
     * 2：中间识别结果（如果开启了返回中间结果）
     * 3：检测到一段有效语音结束，返回该段语音的识别结果
     * 4：处理完所有的音频数据，返回尚未返回的识别结果（如果有）
     * 完整的识别文本 = res_status=3 的所有文本 + res_status=4 的文本
     */
    private void handleAsrResponse(JSONObject jsonObject) {
        try {
            int code = jsonObject.getIntValue("code");
            int resStatus = jsonObject.getIntValue("res_status");
            String message = jsonObject.getString("message");
            String sid = jsonObject.getString("sid");
            
            log.debug("ASR响应 - code: {}, res_status: {}, message: {}, sid: {}", code, resStatus, message, sid);
            
            // 检查响应码
            if (code != 10000) {
                log.error("ASR识别失败，错误码: {}, 错误信息: {}", code, message);
                return;
            }
            
            // 获取数据部分
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                log.warn("ASR响应中没有data字段");
                return;
            }
            
            // 根据res_status处理不同状态
            switch (resStatus) {
                case 0:
                    log.info("ASR识别就绪");
                    // 清空之前的识别结果收集器，为新的识别会话做准备
                    asrTextCollector.setLength(0);
                    receivedStatus4 = false;
                    callbackTriggered = false;
                    break;
                case 1:
                    log.debug("ASR检测到有效语音开始");
                    break;
                case 2:
                    // 中间识别结果（临时结果，不加入最终文本）
                    handleAsrIntermediateResult(data);
                    break;
                case 3:
                    // 检测到一段有效语音结束，返回该段语音的识别结果（需要收集）
                    handleAsrSegmentResult(data);
                    
                    // 如果已经收到了res_status=4的消息，但还没触发回调，则在处理完这条消息后触发回调
                    if (receivedStatus4 && !callbackTriggered) {
                        triggerAsrCallback();
                    }
                    break;
                case 4:
                    // 标记已收到res_status=4消息
                    receivedStatus4 = true;
                    // 处理完所有音频数据，返回最终结果
                    handleAsrFinalResult(data);
                    break;
                default:
                    log.warn("未知的res_status值: {}", resStatus);
                    break;
            }
            
        } catch (Exception e) {
            log.error("处理ASR响应失败", e);
        }
    }
    
    /**
     * 处理中间识别结果
     * res_status=2 表示返回中间识别结果（如果开启了返回中间结果）
     * 这些是临时结果，仅用于显示，不加入最终文本收集器
     */
    private void handleAsrIntermediateResult(JSONObject data) {
        try {
            JSONArray results = data.getJSONArray("results");
            if (results != null && !results.isEmpty()) {
                for (int i = 0; i < results.size(); i++) {
                    JSONObject result = results.getJSONObject(i);
                    String text = result.getString("text");
                    int beginTime = result.getIntValue("begin_time");
                    int endTime = result.getIntValue("end_time");
                    
                    if (text != null && !text.trim().isEmpty()) {
                        log.debug("中间识别结果（res_status=2）: {} (时间: {}ms - {}ms)", text, beginTime, endTime);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理中间识别结果失败", e);
        }
    }
    
    /**
     * 处理语音段识别结果
     * res_status=3 表示检测到一段有效语音结束，返回该段语音的识别结果
     * 这些文本需要收集起来，与 res_status=4 的文本合并形成完整结果
     */
    private void handleAsrSegmentResult(JSONObject data) {
        try {
            JSONArray results = data.getJSONArray("results");
            if (results != null && !results.isEmpty()) {
                StringBuilder segmentText = new StringBuilder();
                
                for (int i = 0; i < results.size(); i++) {
                    JSONObject result = results.getJSONObject(i);
                    String text = result.getString("text");
                    int beginTime = result.getIntValue("begin_time");
                    int endTime = result.getIntValue("end_time");
                    
                    if (text != null && !text.trim().isEmpty()) {
                        log.debug("语音段识别结果（res_status=3）: {} (时间: {}ms - {}ms)", text, beginTime, endTime);
                        segmentText.append(text);
                    }
                }
                
                // 将语音段文本添加到总的收集器中，等待与 res_status=4 的文本合并
                String finalSegmentText = segmentText.toString().trim();
                if (!finalSegmentText.isEmpty()) {
                    log.debug("语音段完整文本（res_status=3）: {}", finalSegmentText);
                    // 添加到总的文本收集器中
                    if (!asrTextCollector.isEmpty()) {
                        // 添加空格分隔不同语音段
                        asrTextCollector.append(" ");
                    }
                    asrTextCollector.append(finalSegmentText);
                    log.info("当前累积文本（包含res_status=3）: {}", asrTextCollector.toString());
                }
            }
        } catch (Exception e) {
            log.error("处理语音段识别结果失败", e);
        }
    }
    
    /**
     * 处理最终识别结果
     * res_status=4 表示处理完所有音频数据，返回尚未返回的识别结果（如果有）
     * 需要将 res_status=3 和 res_status=4 的文本合并作为完整结果
     */
    private void handleAsrFinalResult(JSONObject data) {
        try {
            // 检查当前消息（res_status=4）中是否有新的识别结果
            JSONArray results = data.getJSONArray("results");
            StringBuilder currentMessageText = new StringBuilder();
            
            if (results != null && !results.isEmpty()) {
                for (int i = 0; i < results.size(); i++) {
                    JSONObject result = results.getJSONObject(i);
                    String text = result.getString("text");
                    int beginTime = result.getIntValue("begin_time");
                    int endTime = result.getIntValue("end_time");
                    
                    if (text != null && !text.trim().isEmpty()) {
                        log.info("最终消息（res_status=4）中的识别结果片段: {} (时间: {}ms - {}ms)", text, beginTime,
                                endTime);
                        currentMessageText.append(text);
                    }
                }
            }
            
            // 如果当前消息（res_status=4）中有新的文本，添加到收集器中
            String currentText = currentMessageText.toString().trim();
            if (!currentText.isEmpty()) {
                if (!asrTextCollector.isEmpty()) {
                    // 添加空格分隔不同语音段
                    asrTextCollector.append(" ");
                }
                asrTextCollector.append(currentText);
                log.info("res_status=4 消息添加文本: {}", currentText);
            }
            
            // 触发回调（如果不为空或者没有更多res_status=3消息）
            if (!asrTextCollector.isEmpty()) {
                triggerAsrCallback();
            }
            
        } catch (Exception e) {
            log.error("处理最终识别结果失败", e);
        }
    }
    
    /**
     * 触发ASR回调
     * 将收集的文本发送给回调函数，并标记回调已触发
     */
    private void triggerAsrCallback() {
        if (callbackTriggered) {
            return;
        }
        
        // 获取完整的识别结果
        String completeText = asrTextCollector.toString().trim();
        
        log.info("ASR识别完成，完整文本（res_status=3 + res_status=4）: {}", completeText);
        
        // 触发回调，返回最终识别结果
        if (asrResultCallback != null) {
            // 确保即使没有收集到文本也会触发回调，避免recognizedText为空
            if (completeText.isEmpty()) {
                log.warn("ASR识别完成但未收集到有效文本，将返回空字符串");
            }
            asrResultCallback.accept(completeText);
        } else {
            log.warn("ASR识别完成但回调函数为null，无法返回识别结果");
        }
        
        // 标记回调已触发
        callbackTriggered = true;
        
        // 清空收集器，为下次识别做准备
        asrTextCollector.setLength(0);
    }
    
    /**
     * 保存完整的音频数据到文件
     */
    private void saveAudioData() {
        try {
            byte[] completeAudio = audioBuffer.toByteArray();
            // 根据设置的format参数确定文件扩展名
            String fileExtension = "pcm";
            String fileName = "audio_" + System.currentTimeMillis() + "." + fileExtension;
            
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(completeAudio);
            }
            
            log.info("音频保存成功:{} ", fileName);
        } catch (IOException e) {
            log.error("保存音频数据失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("WebSocket connection closed:{} ", reason);
    }
    
    @Override
    public void onError(Exception ex) {
        log.error("websocket出现异常:{}", ex.getMessage(), ex);
    }
    
}

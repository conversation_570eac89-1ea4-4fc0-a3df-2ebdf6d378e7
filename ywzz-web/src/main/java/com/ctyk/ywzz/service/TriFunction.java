package com.ctyk.ywzz.service;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service
 * @author: zwj
 * @date: 2025/02/21  09:52
 * @description: 大模型参数函数接口
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@FunctionalInterface
public interface TriFunction<A, B, C, D, R> {
    
    /**
     * 处理api调用
     *
     * @param url 请求地址
     * @param path 路径
     * @param requestHeaders 请求标头
     * @param requestBody 请求正文
     * @return {@link R }
     */
    R invokeApi(A url, B path, C requestHeaders, D requestBody);
}

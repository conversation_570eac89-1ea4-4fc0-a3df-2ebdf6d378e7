package com.ctyk.ywzz.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.common.json.utils.JsonUtils;
import com.ctyk.ywzz.client.AiTtsClient;
import com.ctyk.ywzz.client.TtsClient;
import com.ctyk.ywzz.config.TtsConfig;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.TtsInitResponseDTO;
import com.ctyk.ywzz.entity.dto.TtsSpeechToTestResponseDTO;
import com.ctyk.ywzz.service.TtsService;
import com.ctyk.ywzz.util.AudioConvertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: zwj
 * @date: 2025/05/12  14:05
 * @description: 语音文本互转业务实现层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TtsServiceImpl implements TtsService {
    
    private final TtsClient ttsClient;
    
    private final AiTtsClient aiTtsClient;
    
    private final TtsConfig ttsConfig;
    
    @Value("${tts.enable}")
    private String type;
    
    /**
     * 语音转文本
     *
     * @param speechFile 语音文件
     * @return {@link String }
     */
    @Override
    public String speechToText(MultipartFile speechFile) throws Exception {
        
        String result = "";
        switch (type) {
            case Constants.TTS_AI -> result = aiSpeechToText(speechFile);
            case Constants.TTS_YZS -> result = yzsSpeechToText(speechFile);
            default -> result = "暂不支持语音转文本";
        }
        
        return result;
    }
    
    /**
     * 云之声语音转文本
     *
     * @param speechFile 语音文件
     * @return {@link String }
     * @throws Exception 异常
     */
    private String yzsSpeechToText(MultipartFile speechFile) throws Exception {
        
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("语音转文本开始");
        // 1. 文件检查和准备
        speechFileCheck(speechFile);
        String format = extractFileFormat(speechFile);
        
        // 2. 转写流程执行
        stopWatch.stop();
        stopWatch.start("初始化上传");
        String taskId = initializeUpload();
        String md5Hex = calculateMd5(speechFile);
        stopWatch.stop();
        stopWatch.start("上传语音文件");
        uploadSpeechFile(speechFile, taskId, format, md5Hex);
        stopWatch.stop();
        stopWatch.start("完成上传");
        finishUpload(taskId, format, md5Hex);
        stopWatch.stop();
        
        // 3. 获取转写结果
        stopWatch.start("获取转写结果");
        String result = pollForTranscriptionResult(taskId);
        stopWatch.stop();
        log.info("语音转文本耗时统计:{}", stopWatch.prettyPrint());
        return result;
    }
    
    /**
     * AI公司语音转文本
     *
     * @param speechFile 语音文件
     * @return {@link String }
     */
    private String aiSpeechToText(MultipartFile speechFile) {
        
        return aiTtsClient.speechToText(speechFile);
    }
    
    /**
     * 云之声-语音转文本-提取文件格式
     */
    private String extractFileFormat(MultipartFile speechFile) {
        String originalFilename = speechFile.getOriginalFilename();
        log.info("原始文件没:{}", originalFilename);
        if (originalFilename == null || !originalFilename.contains(".")) {
            // 如果文件名为空或没有扩展名，则默认为wav
            return "wav";
        }
        
        String[] parts = originalFilename.split("\\.");
        return parts[parts.length - 1].toLowerCase();
    }
    
    /**
     * 云之声-语音转文本-计算文件MD5
     */
    private String calculateMd5(MultipartFile file) throws IOException {
        try {
            return DigestUtil.md5Hex(file.getBytes());
        } catch (IOException e) {
            log.error("计算文件MD5失败: {}", e.getMessage());
            throw new ServiceException("处理语音文件失败: 无法计算MD5");
        }
    }
    
    /**
     * 云之声-语音转文本-初始化上传过程
     */
    private String initializeUpload() {
        try {
            String initResult = ttsClient.appendUploadInit();
            log.info("【语音转文本】初始化结果: {}", initResult);
            
            TtsInitResponseDTO response = parseAndValidateResponse(initResult, TtsInitResponseDTO.class,
                    "语音转文本初始化失败");
            
            return response.getTaskId();
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("【语音转文本】初始化异常: {}", e.getMessage(), e);
                throw new ServiceException("语音转文本初始化失败: " + e.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 云之声-语音转文本-上传语音文件
     */
    private void uploadSpeechFile(MultipartFile speechFile, String taskId, String format, String md5Hex) {
        try {
            String uploadResult = ttsClient.appendUpload(speechFile.getBytes(), taskId, format, md5Hex);
            log.info("【语音转文本】上传结果: {}", uploadResult);
            
            parseAndValidateResponse(uploadResult, TtsInitResponseDTO.class, "语音转文本上传失败");
        } catch (Exception e) {
            log.error("【语音转文本】上传异常: {}", e.getMessage(), e);
            throw new ServiceException("语音转文本上传失败: " + e.getMessage());
            
        }
    }
    
    /**
     * 云之声-语音转文本-完成上传过程
     */
    private void finishUpload(String taskId, String format, String md5Hex) {
        try {
            String finishResult = ttsClient.appendUploadFinish(taskId, format, Constants.TTS_DOMAIN_VALUE, md5Hex);
            log.info("【语音转文本】完成上传结果: {}", finishResult);
            
            parseAndValidateResponse(finishResult, TtsInitResponseDTO.class, "语音转文本完成上传失败");
        } catch (Exception e) {
            log.error("【语音转文本】完成上传异常: {}", e.getMessage(), e);
            throw new ServiceException("语音转文本完成上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析和验证API响应
     */
    private <T> T parseAndValidateResponse(String responseJson, Class<T> responseType, String errorMessage) {
        T response = JsonUtils.parseObject(responseJson, responseType);
        
        if (ObjectUtils.isEmpty(response)) {
            throw new ServiceException(errorMessage + ": 响应为空");
        }
        
        // 反射获取errorCode，适用于不同响应类型
        try {
            Method getErrorCodeMethod = responseType.getMethod("getErrorCode");
            Object errorCode = getErrorCodeMethod.invoke(response);
            
            if (errorCode != null && !errorCode.equals(0)) {
                Method getMessageMethod = responseType.getMethod("getMessage");
                String message = (String) getMessageMethod.invoke(response);
                throw new ServiceException(errorMessage + ": " + message);
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("解析响应时发生反射错误: {}", e.getMessage());
            throw new ServiceException(errorMessage + ": 无法验证响应状态");
        }
        
        return response;
    }
    
    /**
     * 轮询获取转写结果
     */
    private String pollForTranscriptionResult(String taskId) throws InterruptedException {
        
        StringBuilder textResult = new StringBuilder();
        long maxRetries = ttsConfig.getRetryTimes();
        long retryInterval = ttsConfig.getRetryInterval();
        
        for (int i = 0; i < maxRetries; i++) {
            try {
                String getResult = ttsClient.getText(taskId);
                log.info("【语音转文本】获取结果 (第{}次): {}", i + 1, getResult);
                
                TtsSpeechToTestResponseDTO response = JsonUtils.parseObject(getResult,
                        TtsSpeechToTestResponseDTO.class);
                if (ObjectUtils.isEmpty(response)) {
                    throw new ServiceException("语音转文本获取失败: 响应为空");
                }
                
                if (response.getErrorCode() != 0) {
                    throw new ServiceException("语音转文本获取失败: " + response.getMessage());
                }
                
                String status = response.getStatus();
                log.info("【语音转文本】状态: {}", status);
                
                if ("done".equals(status)) {
                    // 转写完成，提取文本
                    List<TtsSpeechToTestResponseDTO.ResultsDTO> results = response.getResults();
                    if (results != null) {
                        for (TtsSpeechToTestResponseDTO.ResultsDTO result : results) {
                            textResult.append(result.getText());
                        }
                    } else {
                        log.error("【语音转文本】获取结果异常: 结果为空");
                        textResult = new StringBuilder(ttsConfig.getEmptyText());
                    }
                    
                    log.info("【语音转文本】完成，文本内容: {}", textResult);
                    break;
                } else if ("running".equals(status) || "waiting".equals(status)) {
                    // 转写中或等待转写，等待后继续重试
                    log.info("【语音转文本】进度: {}%", response.getProgress());
                    try {
                        Thread.sleep(retryInterval);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw e;
                    }
                } else {
                    // 未知状态
                    log.error("【语音转文本】未知状态: {}", status);
                    throw new ServiceException("语音转文本获取异常: 未知状态 " + status);
                }
            } catch (ServiceException e) {
                // 直接抛出业务异常
                throw e;
            } catch (Exception e) {
                log.error("【语音转文本】获取结果异常: {}", e.getMessage(), e);
                throw new ServiceException("语音转文本获取失败: " + e.getMessage());
            }
        }
        
        return textResult.toString();
    }
    
    /**
     * 文本到语音
     * 返回WAV格式的音频数据
     *
     * @param text 发短信
     * @return {@link byte[] } WAV格式音频数据
     * @throws Exception 例外
     */
    @Override
    public byte[] textToSpeech(String text) throws Exception {
        
        byte[] result = null;
        switch (type) {
            case Constants.TTS_AI -> {
                // AI TTS 返回PCM格式，需要转换为WAV
                byte[] pcmData = aiTtsClient.textToSpeech(text);
                if (pcmData != null && pcmData.length > 0) {
                    log.info("AI TTS返回PCM数据，大小: {} bytes，开始转换为WAV", pcmData.length);
                    result = AudioConvertUtils.pcmToWav(pcmData);
                    log.info("PCM转WAV完成，WAV数据大小: {} bytes", result.length);
                } else {
                    log.warn("AI TTS返回空数据");
                    result = new byte[0];
                }
            }
            case Constants.TTS_YZS -> {
                byte[] pcmData = ttsClient.textToSpeech(text);
                if (pcmData != null && pcmData.length > 0) {
                    log.info("云之声 TTS返回PCM数据，大小: {} bytes，开始转换为WAV", pcmData.length);
                    result = AudioConvertUtils.pcmToWav(pcmData);
                    log.info("云之声 PCM转WAV完成，WAV数据大小: {} bytes", result.length);
                } else {
                    log.warn("云之声 TTS返回空数据");
                    result = new byte[0];
                }
            }
            default -> {
                log.error("不支持的TTS类型: {}", type);
                result = "暂不支持语音转文本".getBytes();
            }
        }
        return result;
    }
    
    /**
     * 长文本转语音
     *
     * @param text 文本
     * @return {@link byte[] }
     */
    @Override
    public byte[] longTextToSpeech(String text) {
        
        return ttsClient.longTextToSpeech(text);
    }
    
    /**
     * 语音文件检查
     *
     * @param speechFile 语音文件
     */
    private void speechFileCheck(MultipartFile speechFile) {
        
        if (ObjectUtils.isEmpty(speechFile)) {
            throw new ServiceException("语音文件不能为空");
        }
    }
}

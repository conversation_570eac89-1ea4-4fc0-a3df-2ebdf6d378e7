package com.ctyk.ywzz.service.impl;

import com.baidu.fsg.uid.UidGenerator;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.SmartMedicineDTO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordDetailPO;
import com.ctyk.ywzz.entity.po.ShcConversationRecordPO;
import com.ctyk.ywzz.entity.vo.MedicineAnswerKnowledgeBase;
import com.ctyk.ywzz.entity.vo.MedicineAnswerVO;
import com.ctyk.ywzz.mapper.ShcConversationRecordDetailMapper;
import com.ctyk.ywzz.mapper.ShcConversationRecordMapper;
import com.ctyk.ywzz.service.IntelligentMedInquiryService;
import com.ctyk.ywzz.util.MaasV2HttpUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: zwj
 * @date: 2025/03/11  15:22
 * @description: 智能问药业务实现层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Service
public class IntelligentMedInquiryServiceImpl implements IntelligentMedInquiryService {

    @Resource
    private UidGenerator uidGenerator;
    
    @Resource
    private ShcConversationRecordMapper recordMapper;
    
    @Resource
    private ShcConversationRecordDetailMapper recordDetailMapper;
    
    @Resource
    private HttpServletRequest request;
    
    @Resource
    private MaasV2HttpUtil maasV2HttpUtil;
    
    /**
     * 智能问药
     *
     * @param message 问题
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedicineAnswerVO answer(String message) {
        
        long recordId = uidGenerator.getUID();
        MedicineAnswerVO vo = new MedicineAnswerVO();
        List<MedicineAnswerKnowledgeBase> voList = new ArrayList<>();
        SmartMedicineDTO dto = maasV2HttpUtil.getSmartMedicine(message);
        String data = dto.getData();
        if (!ObjectUtils.isEmpty(data)) {
            String[] lines = data.split("\\n+");
            lines = Arrays.stream(lines)
                          .map(String::trim)
                          .filter(s -> !s.isEmpty())
                          .toArray(String[]::new);
            for (String line : lines) {
                MedicineAnswerKnowledgeBase knowledgeBase = new MedicineAnswerKnowledgeBase();
                Pattern boldPattern = Pattern.compile("\\*\\*(.*?)\\*\\*");
                Pattern titlePattern = Pattern.compile("^#{1,5}\\s*(.+)$");
                Matcher boldMatcher = boldPattern.matcher(line);
                Matcher titleMatcher = titlePattern.matcher(line);
                knowledgeBase.setNewLineFlag(Boolean.TRUE);
                if (titleMatcher.find()) {
                    knowledgeBase.setBoldFlag(Boolean.TRUE);
                } else {
                    knowledgeBase.setBoldFlag(Boolean.FALSE);
                }
                knowledgeBase.setContent(line.replace("*", "")
                                             .replace("#", "")
                                             .replace("-", "")
                                             .replace(" ", ""));
                voList.add(knowledgeBase);
            }
            vo.setKnowledgeBaseList(voList);
            vo.setRecordId(recordId);
            saveRecordData(message, data, recordId);
            return vo;
        }
        
        return vo;
    }
    
    /**
     * 保存对话记录数据
     *
     * @param message 用户提问信息
     * @param data 大模型回答内容
     * @param recordId 记录 ID
     */
    public void saveRecordData(String message, String data, Long recordId) {
        
        long recordDetailId = uidGenerator.getUID();
        String openId = request.getHeader(Constants.USER_ID);
        openId = ObjectUtils.isEmpty(openId) ? Constants.DEFAULT_USER_ID : openId;
        LocalDateTime now = LocalDateTime.now();
        ShcConversationRecordPO recordPo = new ShcConversationRecordPO();
        recordPo.setRecordId(recordId);
        recordPo.setStartTime(now);
        recordPo.setUserId(openId);
        recordMapper.insertShcConversationRecord(recordPo);
        
        ShcConversationRecordDetailPO recordDetailPo = new ShcConversationRecordDetailPO();
        recordDetailPo.setRecommendations(data);
        recordDetailPo.setHealthStatus(message);
        recordDetailPo.setType(Boolean.FALSE);
        recordDetailPo.setOperateTime(now);
        recordDetailPo.setRecordId(recordId);
        recordDetailPo.setRecordDetailId(recordDetailId);
        recordDetailMapper.insertShcConversationRecordDetail(recordDetailPo);
    }
}

package com.ctyk.ywzz.service.impl;


import cn.hutool.core.util.IdcardUtil;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.ywzz.config.HospitalConfigProperties;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.VisitListReqDTO;
import com.ctyk.ywzz.entity.vo.VisitLInfoReqVO;
import com.ctyk.ywzz.entity.vo.VisitListVO;
import com.ctyk.ywzz.entity.vo.VisitUserVO;
import com.ctyk.ywzz.service.VisitService;
import com.ctyk.ywzz.util.DataMaskingUtil;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.service.impl
 * @author: Liyh
 * @date: 2024/07/22 15:54
 * @description: 就诊人业务实现层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Service
@Slf4j
public class VisitServiceImpl implements VisitService {
    
    @Resource
    private HospitalConfigProperties hospitalConfig;
    
    protected static List<VisitUserVO> userList = new ArrayList<>(6);
    
    static {
        VisitUserVO userInfo = new VisitUserVO();
        userInfo.setUserId("000001");
        userInfo.setUserName("方兆玉");
        userInfo.setPatientNo("**********");
        userList.add(userInfo);
        VisitUserVO userInfo1 = new VisitUserVO();
        userInfo1.setUserId("000002");
        userInfo1.setUserName("王凯");
        userInfo1.setPatientNo("**********");
        userList.add(userInfo1);
        VisitUserVO userInfo2 = new VisitUserVO();
        userInfo2.setUserId("000003");
        userInfo2.setUserName("李正东");
        userInfo2.setPatientNo("**********8");
        userList.add(userInfo2);
        VisitUserVO userInfo3 = new VisitUserVO();
        userInfo3.setUserId("000004");
        userInfo3.setUserName("马云婷");
        userInfo3.setPatientNo("**********");
        userList.add(userInfo3);
        VisitUserVO userInfo4 = new VisitUserVO();
        userInfo4.setUserId("000005");
        userInfo4.setUserName("张京平");
        userInfo4.setPatientNo("**********");
        userList.add(userInfo4);
        VisitUserVO userInfo5 = new VisitUserVO();
        userInfo5.setUserId("000006");
        userInfo5.setUserName("韩鹏程");
        userInfo5.setPatientNo("**********");
        userList.add(userInfo5);
    }
    
    
    /**
     * 查询就诊人列表
     *
     * @param openId openId
     * @return {@link List }<{@link VisitUserVO }>
     */
    @Override
    public List<VisitUserVO> getVisitUserList(String openId) {
        
        List<VisitUserVO> users = new ArrayList<>(6);
        VisitListReqDTO visitListReq = new VisitListReqDTO();
        visitListReq.setOnlineId(openId);
        visitListReq.setTradeCode("910090");
        XmlMapper xmlMapper = new XmlMapper();
        try {
            log.info("查询就诊人请求参数：{}", xmlMapper.writeValueAsString(visitListReq));
            // TODO 上线时注释放开，调用接口
          /*  String result = HttpClientUtil.callServiceHc(hospitalConfig.getApi().getVisitInfoUrl(), WebRequestCodeEnum.VISIT_LIST.getCode(),
                    xmlMapper.writeValueAsString(visitListReq));*/
            String result = """
                    <Response>
                               <ResultCode>0</ResultCode>
                               <ResultMsg>成功</ResultMsg>
                               <OnlineId>o39LEjmxQqt4jPFIS33au4JKqaaw</OnlineId>
                               <PatInfoList>
                                    <PatInfo>
                                        <ID>2890724</ID>
                                        <PatOnLineName>方*玉</PatOnLineName>
                                        <HISPatNo>**********</HISPatNo>
                                        <HISPatName>方*玉</HISPatName>
                                        <HISPatCreadNo>610303199711033811</HISPatCreadNo>
                                        <HISPatCreadTypedr>居民身份证</HISPatCreadTypedr>
                                    </PatInfo>
                               </PatInfoList>
                    </Response>
                    """;
            
            if (StringUtils.isBlank(result)) {
                throw new ServiceException("接口返参为空!");
            }
            log.info("查询接口返参：{}", result);
            VisitLInfoReqVO visitInfo = xmlMapper.readValue(result, VisitLInfoReqVO.class);
            if (visitInfo != null && visitInfo.getResultCode() == 0 && !CollectionUtils.isEmpty(
                    visitInfo.getVisitList())) {
                // 根据姓名和patientNo去重
                List<VisitListVO> visitList = visitInfo.getVisitList().stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(item -> item.getHisPatName() + item.getHisPatNo()))),
                        ArrayList::new));
                
                visitList.forEach(item -> {
                    VisitUserVO userVO = new VisitUserVO();
                    userVO.setUserId(item.getId());
                    // 姓名中性脱敏
                    String hisPatName = item.getHisPatName();
                    
                    String hidePatientName = DataMaskingUtil.maskChineseName(hisPatName);
                    userVO.setUserName(hidePatientName);
                    userVO.setPatientNo(item.getHisPatNo());
                    String hisPatCreadTypedr = item.getHisPatCreadTypedr();
                    if (Constants.ID_CARD.equals(hisPatCreadTypedr)) {
                        // 从身份证中提取性别、年龄
                        extractUserBaseInfoFromIdCard(userVO, item);
                    }
                    users.add(userVO);
                });
            } else if (visitInfo != null && visitInfo.getResultCode() == 0) {
                throw new ServiceException("请退出系统，在服务号中添加就诊人!");
            } else {
                throw new ServiceException("请退出系统，重新授权!");
            }
            
        } catch (Exception e) {
            log.error("查询就诊人出现异常:{}", e.getMessage(), e);
            throw new ServiceException("查询就诊人失败!");
        }
        return users;
    }
    
    /**
     * 从身份证号中提取用户基本信息
     *
     * @param userVo 用户响应实体
     * @param item 项目
     */
    private void extractUserBaseInfoFromIdCard(VisitUserVO userVo, VisitListVO item) {
        
        try {
            String hisPatCreadNo = item.getHisPatCreadNo();
            int age = IdcardUtil.getAgeByIdCard(hisPatCreadNo);
            // (1 : 男 ， 0 : 女)
            int genderByIdCard = IdcardUtil.getGenderByIdCard(hisPatCreadNo);
            if (genderByIdCard == 1) {
                userVo.setGender(Constants.GENDER);
            } else {
                userVo.setGender(Constants.GENDER_WOMAN);
            }
            userVo.setAge(age);
        } catch (Exception e) {
            log.error("从身份证中提取性别、年龄出现异常,身份证号：{}，异常:{}", item.getHisPatCreadNo(), e.getMessage(),
                    e);
        }
    }
}

package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/05/12  15:33
 * @description: 语音转文本初始化阶段响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@NoArgsConstructor
@Data
public class TtsInitResponseDTO {
    
    @JsonProperty("task_id")
    private String taskId;
    
    @JsonProperty("error_code")
    private Integer errorCode;
    
    @JsonProperty("message")
    private String message;
}

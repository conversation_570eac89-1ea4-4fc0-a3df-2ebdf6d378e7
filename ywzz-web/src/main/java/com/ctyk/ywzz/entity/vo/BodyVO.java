package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/10/10 10:56
 * @description: 北大一接口响应体实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "Body")
public class BodyVO {
    @JacksonXmlProperty(localName = "HIPMessageServerResponse")
    private HIPMessageServerResponseVO hIPMessageServerResponse;
}
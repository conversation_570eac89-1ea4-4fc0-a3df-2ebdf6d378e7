package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.entity.dto
 * @author: zwj
 * @date: 2025/07/11  11:16
 * @description: maas v2.0知识库响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@NoArgsConstructor
@Data
public class MaasV2KnowledgeResponseDTO {
    
    @JsonProperty("code")
    private Integer code;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("data")
    private List<DataDTO> data;
    
    @JsonProperty("success")
    private Boolean success;
    
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        
        @JsonProperty("score")
        private BigDecimal score;
        
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("content")
        private String content;
    }
}

package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/08/21 09:47
 * @description: 号源信息明细
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "Schedule")
public class ScheduleItemVO {

    /**
     * 门诊排班项记录标识
     */
    @JacksonXmlProperty(localName = "ScheduleItemCode")
    private String scheduleItemCode;

    /**
     * 排班日期
     */
    @JacksonXmlProperty(localName = "ServiceDate")
    private String serviceDate;

    /**
     * 星期数
     */
    @JacksonXmlProperty(localName = "WeekDay")
    private String weekDay;

    /**
     * 排班时段代码
     */

    @JacksonXmlProperty(localName = "SessionCode")
    private String sessionCode;

    /**
     * 排班时段名称
     */
    @JacksonXmlProperty(localName = "SessionName")
    private String sessionName;

    /**
     * 开始时间
     */
    @JacksonXmlProperty(localName = "StartTime")
    private String startTime;

    /**
     * 结束时间
     */
    @JacksonXmlProperty(localName = "EndTime")
    private String endTime;

    /**
     * 科室代码
     */
    @JacksonXmlProperty(localName = "DepartmentCode")
    private String departmentCode;

    /**
     * 科室名称
     */
    @JacksonXmlProperty(localName = "DepartmentName")
    private String departmentName;

    /**
     * 诊室代码
     */
    @JacksonXmlProperty(localName = "ClinicRoomCode")
    private String clinicRoomCode;

    /**
     * 诊室名称
     */
    @JacksonXmlProperty(localName = "ClinicRoomName")
    private String clinicRoomName;

    /**
     * 医生代码
     */
    @JacksonXmlProperty(localName = "DoctorCode")
    private String doctorCode;

    /**
     * 医生名称
     */
    @JacksonXmlProperty(localName = "DoctorName")
    private String doctorName;

    /**
     * 医生工号
     */
    @JacksonXmlProperty(localName = "DoctorLogCode")
    private String doctorLogCode;

    /**
     * 医生职称代码
     */
    @JacksonXmlProperty(localName = "DoctorTitleCode")
    private String doctorTitleCode;

    /**
     * 医生职称
     */
    @JacksonXmlProperty(localName = "DoctorTitle")
    private String doctorTitle;

    /**
     * 医生专长
     */
    @JacksonXmlProperty(localName = "DoctorSpec")
    private String doctorSpec;

    /**
     * 出诊级别代码
     */
    @JacksonXmlProperty(localName = "DoctorSessTypeCode")
    private String doctorSessTypeCode;

    /**
     * 出诊级别
     */
    @JacksonXmlProperty(localName = "DoctorSessType")
    private String doctorSessType;

    /**
     * 专业代码
     */
    @JacksonXmlProperty(localName = "ServiceCode")
    private String serviceCode;

    /**
     * 专业名称
     */
    @JacksonXmlProperty(localName = "ServiceName")
    private String serviceName;

    /**
     * 预约挂号总费用
     */
    @JacksonXmlProperty(localName = "Fee")
    private String fee;

    /**
     * 挂号费
     */
    @JacksonXmlProperty(localName = "RegFee")
    private String regFee;

    /**
     * 诊查费
     */
    @JacksonXmlProperty(localName = "CheckupFee")
    private String checkupFee;

    /**
     * 服务费
     */
    @JacksonXmlProperty(localName = "ServiceFee")
    private String serviceFee;

    /**
     * 其他费
     */
    @JacksonXmlProperty(localName = "OtherFee")
    private String otherFee;

    @JacksonXmlProperty(localName = "AvailableNumStr")
    private String availableNumStr;

    /**
     * 就诊地址
     */
    @JacksonXmlProperty(localName = "AdmitAddress")
    private String admitAddress;

    /**
     * 就诊时间范围
     */
    @JacksonXmlProperty(localName = "AdmitTimeRange")
    private String admitTimeRange;

    /**
     * 备注
     */
    @JacksonXmlProperty(localName = "Note")
    private String note;

    /**
     * 是否有分时
     */
    @JacksonXmlProperty(localName = "TimeRangeFlag")
    private String timeRangeFlag;

    /**
     * 出诊状态
     */
    @JacksonXmlProperty(localName = "ScheduleStatus")
    private String scheduleStatus;

    @JacksonXmlProperty(localName = "ScheduleStatu")
    private String scheduleStatu;

    /**
     * 可挂号预约的总号源数
     */
    @JacksonXmlProperty(localName = "AvailableTotalNum")
    private int availableTotalNum;

    /**
     * 剩余号源数
     */
    @JacksonXmlProperty(localName = "AvailableLeftNum")
    private int availableLeftNum;

    /**
     * 全预约标志
     */
    @JacksonXmlProperty(localName = "AllBookServise")
    private String allBookServise;

    /**
     * 禁止挂号标志
     */
    @JacksonXmlProperty(localName = "StopRegFlag")
    private String stopRegFlag;

    /**
     * 称谓
     */
    @JacksonXmlProperty(localName = "DoctorCW")
    private String doctorCW;

    @JacksonXmlProperty(localName = "ServiceCodeStr")
    private String serviceCodeStr;

    @JacksonXmlProperty(localName = "ServiceNameStr")
    private String serviceNameStr;

    @JacksonXmlProperty(localName = "ClinicServiceCode")
    private String clinicServiceCode;

    @JacksonXmlProperty(localName = "ClinicServiceName")
    private String clinicServiceName;

    @JacksonXmlProperty(localName = "RegEndTime")
    private String regEndTime;

    @JacksonXmlProperty(localName = "AvailableLeftNumHLW")
    private String availableLeftNumHLW;

    @JacksonXmlProperty(localName = "HospitalName")
    private String hospitalName;

    @JacksonXmlProperty(localName = "HospitalCode")
    private String hospitalCode;

    @JacksonXmlProperty(localName = "PCLRowID")
    private String pclRowId;

    @JacksonXmlProperty(localName = "TimeRangInfo")
    private String timeRangInfo;

    @JacksonXmlProperty(localName = "TimeRangNoStr")
    private String timeRangNoStr;

}
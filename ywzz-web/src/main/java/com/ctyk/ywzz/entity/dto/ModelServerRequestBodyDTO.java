package com.ctyk.ywzz.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/02/14  09:09
 * @description: 模型服务入参实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class ModelServerRequestBodyDTO {
    
    /**
     * 模型编码
     */
    private String modelCode;
    
    /**
     * 问题
     */
    private String content;
    
    private Integer maxTokens = 4096;
    
    private BigDecimal temperature = new BigDecimal("0.2");
    
    /**
     * 系统限定提示
     */
    private String chatPrompt ;
    
    /**
     * 用户限定提示
     */
    private String assistantPrompt;
    
    /**
     *  历史会话列表长度不能超过5
     */
    private List<HistoryChatDTO> historyChatList;
}

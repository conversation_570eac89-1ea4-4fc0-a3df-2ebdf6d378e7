package com.ctyk.ywzz.entity.qo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.qo
 * @author: zwj
 * @date: 2025/01/08  10:10
 * @description: 健康咨询示例查询请求实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class QueryQuestionnaireQO {
    
    /**
     * 页面名称
     */
    @NotBlank(message = "页面名称不能为空")
    private String pageName;
    
    /**
     * 数量
     */
    @NotNull(message = "查询数量不能为空")
    private Integer count;
}

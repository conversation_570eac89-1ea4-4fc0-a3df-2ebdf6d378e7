package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/09/04 11:13
 * @description: 北大一就诊人查询响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "PatInfo")
public class VisitListVO {

    /**
     * His表ID
     */
    @JacksonXmlProperty(localName = "ID")
    private String id;

    /**
     * 第三方用户名
     */
    @JacksonXmlProperty(localName = "PatOnLineName")
    private String patOnLineName;

    /**
     * 登记号
     */
    @JacksonXmlProperty(localName = "HISPatNo")
    private String hisPatNo;

    /**
     * 姓名
     */
    @JacksonXmlProperty(localName = "HISPatName")
    private String hisPatName;

    /**
     * 证件号
     */
    @JacksonXmlProperty(localName = "HISPatCreadNo")
    private String hisPatCreadNo;

    /**
     * 证件类型
     */
    @JacksonXmlProperty(localName = "HISPatCreadTypedr")
    private String hisPatCreadTypedr;
}
package com.ctyk.ywzz.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: zwj
 * @date: 2025/01/07  15:40
 * @description: 对话反馈实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@TableName("shc_conversation_feedback")
public class ShcConversationFeedbackPO {
    
    /**
     * 主键
     */
    @TableId
    private String feedbackId;
    
    /**
     * 对话记录ID
     */
    private String recordId;
    
    /**
     * 反馈类型，0-点踩；1-点赞
     */
    private Integer optionType;
    
    /**
     * 对话场景，1-智慧分诊；0-信息咨询
     */
    private Integer recordScene;
    
    /**
     * 反馈选项ID
     */
    private String optionId;
    
    /**
     * 其他反馈内容
     */
    private String otherContent;
    
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
}

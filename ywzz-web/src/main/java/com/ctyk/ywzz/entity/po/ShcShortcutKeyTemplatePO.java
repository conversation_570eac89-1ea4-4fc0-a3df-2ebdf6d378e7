package com.ctyk.ywzz.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: Li<PERSON>h
 * @date: 2024/10/11 13:58
 * @description: 快捷键模板 PO
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@TableName("shc_shortcut_key_template")
public class ShcShortcutKeyTemplatePO {
    
    /**
     * 模板ID
     */
    @TableId
    private String templateId;
    
    /**
     * 名称;快捷按钮名称
     */
    private String name;
    
    /**
     * 图标;按钮图标
     */
    private String ico;
    
    /**
     * 类型;类型 1：本系统   2：第三方链接  3：问题
     */
    private String type;
    
    /**
     * url路径;url
     */
    private String url;
    
    /**
     * 是否有效;0：有效  1：无效
     */
    private String deleteFlag;
    
    /**
     * 序号
     */
    private String seqNo;
}

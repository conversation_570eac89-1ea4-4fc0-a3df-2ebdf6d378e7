package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/10/10 10:34
 * @description: 北大一接口响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "Envelope")
public class EnvelopeVO {

    @JacksonXmlProperty(localName = "Header")
    private String header;
    @JacksonXmlProperty(localName = "Body")
    private BodyVO body;
}
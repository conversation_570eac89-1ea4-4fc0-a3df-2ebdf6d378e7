package com.ctyk.ywzz.entity.vo;

import com.ctyk.ywzz.config.NoEscapeStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: zwj
 * @date: 2025/06/23  13:48
 * @description: 解析PDF报告响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@AllArgsConstructor
public class ParsePdfReportVO {
    
    private Boolean success;
    
    private String message;
    
    @JsonSerialize(using = NoEscapeStringSerializer.class)
    private String data;
}

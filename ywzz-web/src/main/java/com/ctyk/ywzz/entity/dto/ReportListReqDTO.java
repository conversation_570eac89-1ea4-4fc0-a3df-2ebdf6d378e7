package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.dto
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/08/19 10:22
 * @description: 报告列表请求DTO
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@JacksonXmlRootElement(localName = "Request")
@Setter
@Getter
public class ReportListReqDTO {

    /**
     * 登记号
     */
    private String patientNo;

    /**
     * 身份证号
     */
    private String idCard;


    /**
     * openId
     */
    private String openId;
}

package com.ctyk.ywzz.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: <PERSON><PERSON>
 * @date: 2024/08/01 09:01
 * @description: 对话详细记录
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@TableName("shc_conversation_record_detail")
public class ShcConversationRecordDetailPO {
    
    /**
     * 对话记录详细ID
     */
    @TableId
    private Long recordDetailId;
    
    /**
     * 对话记录ID
     */
    private Long recordId;
    
    /**
     * 健康状态;用户提问内容
     */
    private String healthStatus;
    
    /**
     * 健康建议;大模型回答内容
     */
    private String recommendations;
    
    /**
     * 推荐科室;大模型推荐科室列表
     */
    private String departments;
    
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
    
    /**
     * 问题类型
     */
    private Boolean type;
    
}
package com.ctyk.ywzz.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: <PERSON><PERSON>
 * @date: 2024/08/01 09:01
 * @description: 对话记录
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@TableName("shc_conversation_record")
public class ShcConversationRecordPO {
    
    /**
     * 对话记录ID
     */
    @TableId
    private Long recordId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 推荐科室;大模型推荐科室列表
     */
    private String departments;
    
    /**
     * 对话开始时间
     */
    private LocalDateTime startTime;
    
}
package com.ctyk.ywzz.entity.vo;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: Li<PERSON>h
 * @date: 2024/10/14 16:06
 * @description: 微信消息返参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Setter
@XmlRootElement(name = "xml")
public class WechatMessageVO {

    /**
     * 接收方帐号（收到的OpenID）
     */
    @XmlElement(name = "ToUserName")
    private String toUserName;
    /**
     * 开发者微信号
     */
    @XmlElement(name = "FromUserName")
    private String fromUserName;
    /**
     * 消息创建时间 （整型）
     */
    @XmlElement(name = "CreateTime")
    private long createTime;

    /**
     * 消息类型
     */
    @XmlElement(name = "MsgType")
    private String msgType;

    /**
     * 消息类型
     */
    @XmlElement(name = "Content")
    private String content;

}

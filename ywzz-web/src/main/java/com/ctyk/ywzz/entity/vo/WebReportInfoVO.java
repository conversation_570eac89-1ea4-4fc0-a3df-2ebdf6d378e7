package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: Liyh
 * @date: 2024/08/19 11:04
 * @description: 报告列表返参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "PeRecord")
public class WebReportInfoVO {
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 性别
     */
    private String sex;
    
    /**
     * 年龄
     */
    private String age;
    
    /**
     * 体检日期
     */
    private Date examDate;
    
    /**
     * 单位（北京大学第一医院）
     */
    private String company;
    
    /**
     * 就诊ID
     */
    private String admId;
    
    /**
     * 报告状态
     */
    private String reportStatus;
    
    /**
     * 体检状态
     */
    private String peStatus;
    
    /**
     * 预约日期
     */
    private Date preDate;
    
    /**
     * 套餐名称
     */
    private String orderName;
    
    /**
     *
     */
    @JacksonXmlProperty(localName = "HPNo")
    private String hpNo;

    /*<PeRecord>
<name>史翰维</name>
<sex>男</sex>
<age>26</age>
<examDate>2024-01-17</examDate>
<company>北京大学第一医院</company>
<admId>7995142</admId>
<reportStatus>已取</reportStatus>
<peStatus>到达</peStatus>
<preDate>2024-01-17</preDate>
<orderName>tj01-本院44(男)</orderName>
<HPNo>PTAAAA0665</HPNo>
</PeRecord>*/
}

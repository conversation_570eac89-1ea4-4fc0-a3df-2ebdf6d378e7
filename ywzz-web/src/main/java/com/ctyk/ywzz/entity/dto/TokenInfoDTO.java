package com.ctyk.ywzz.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: <PERSON><PERSON>
 * @date: 2024/07/18 14:59
 * @description: 获取token入参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class TokenInfoDTO {

    /**
     * 时间戳
     **/
    @NotBlank(message = "timestamp不能为空")
    @NotNull(message = "缺失参数:[timestamp]")
    @Pattern(regexp = "^\\d{10,13}$", message = "timestamp格式不正确")
    private String timestamp;

    /**
     * ID
     */
    @NotBlank(message = "openId不能为空")
    @NotNull(message = "缺失参数:[openId]")
    private String openId;

    /**
     * 加签
     */
    @NotBlank(message = "signature不能为空")
    @NotNull(message = "缺失参数:[signature]")
    private String signature;
}
package com.ctyk.ywzz.entity.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: Liyh
 * @date: 2024/07/17 16:09
 * @description: 内部接口就诊人查询响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class VisitUserVO {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 登记号
     */
    private String patientNo;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 年龄
     */
    private int age;
}

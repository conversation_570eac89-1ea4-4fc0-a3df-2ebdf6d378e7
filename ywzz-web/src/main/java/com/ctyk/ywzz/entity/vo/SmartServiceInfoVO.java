package com.ctyk.ywzz.entity.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/10/17 16:17
 * @description: 智慧服务接口返参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class SmartServiceInfoVO {

    /**
     * 服务类型  rag：知识库； llm：大模型
     */
    private String type;

    /**
     * 大模型健康咨询
     */
    private ConsultInfoVO consultInfo;

    /**
     * 知识库
     */
    private List<KnowledgeBaseInfoVO> knowledgeBaseList;
    
    /**
     * 记录id
     */
    private String recordId;
    
    /**
     * 思考内容
     */
    private String thinkContent;
    
    /**
     * 思考时间
     */
    private Long thinkTime;
}
package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/05/12  15:59
 * @description: 语音转文本结果实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@NoArgsConstructor
public class TtsSpeechToTestResponseDTO {
    
    @JsonProperty("duration")
    private Integer duration;
    
    @JsonProperty("start_time")
    private Long startTime;
    
    @JsonProperty("use_hot_data")
    private String useHotData;
    
    @JsonProperty("cost_time")
    private Integer costTime;
    
    @JsonProperty("progress")
    private Integer progress;
    
    @JsonProperty("error_code")
    private Integer errorCode;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("results")
    private List<ResultsDTO> results;
    
    @JsonProperty("status")
    private String status;
    
    @NoArgsConstructor
    @Data
    public static class ResultsDTO {
        
        @JsonProperty("start")
        private Integer start;
        
        @JsonProperty("index")
        private Integer index;
        
        @JsonProperty("end")
        private Integer end;
        
        @JsonProperty("speaker")
        private Integer speaker;
        
        @JsonProperty("word_info")
        private List<WordInfoDTO> wordInfo;
        
        @JsonProperty("text")
        private String text;
        
        @JsonProperty("text_length")
        private Integer textLength;
        
        @NoArgsConstructor
        @Data
        public static class WordInfoDTO {
            
            @JsonProperty("b")
            private Integer b;
            
            @JsonProperty("e")
            private Integer e;
            
            @JsonProperty("w")
            private String w;
        }
    }
}

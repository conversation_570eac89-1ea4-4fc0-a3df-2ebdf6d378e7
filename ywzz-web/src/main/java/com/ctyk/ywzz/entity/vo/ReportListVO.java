package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: Liyh
 * @date: 2024/07/17 18:10
 * @description: 体检报告列表返参信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class ReportListVO {

    /**
     * 就诊ID
     */
    private String admId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private String age;

    /**
     * 体检日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date examDate;

    /**
     * 单位（北京大学第一医院）
     */
    private String company;

}

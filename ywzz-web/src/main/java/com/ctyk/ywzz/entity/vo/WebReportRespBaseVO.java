package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: Liyh
 * @date: 2024/08/20 11:15
 * @description: webService接口返参公共类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "Response")
public class WebReportRespBaseVO {
    /**
     * 结果编码 0：成功
     */
    @JacksonXmlProperty(localName = "ResultCode")
    private int resultCode;

    /**
     * 结果明细
     */
    @JacksonXmlProperty(localName = "ResultDesc")
    private String resultDesc;
}

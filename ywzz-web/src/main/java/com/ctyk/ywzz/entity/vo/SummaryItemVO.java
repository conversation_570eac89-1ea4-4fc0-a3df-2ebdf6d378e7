package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: Liyh
 * @date: 2024/08/19 14:15
 * @description: 总检建议明细
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@JacksonXmlRootElement(localName = "SummaryItem")
public class SummaryItemVO {

    /**
     * 指标
     */
    private String item;

    /**
     * 指标明细
     */
    private String itemDesc;

    public void setItem(String item) {
        this.item = item.substring(item.indexOf("、") + 1, item.length());
    }

    public void setItemDesc(String itemDesc) {
        this.itemDesc = itemDesc.trim();
    }
}

package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: Liyh
 * @date: 2024/08/19 14:12
 * @description: 报告详细信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "ExaminationSummary")
public class WebReportDetailVO {

    /**
     * 总检医生
     */
    private String summaryDoctor;

    /**
     * 总检日期
     */
    private String summaryDate;

    /**
     * 审核医生
     */
    private String auditDoctor;

    /**
     * 审核时间
     */
    private String auditDate;

    /**
     * 总检明细
     */
    @JacksonXmlElementWrapper(localName = "summaryList")
    @JacksonXmlProperty(localName = "SummaryItem")
    private List<SummaryItemVO> summaryItemList;
}

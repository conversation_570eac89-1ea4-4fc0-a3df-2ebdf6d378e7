package com.ctyk.ywzz.entity.qo;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: <PERSON><PERSON>
 * @date: 2024/07/24 14:06
 * @description:
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class ConsultQO {

    @NotBlank(message = "咨询内容不能为空")
    @Length(min = 2,max = 100,message = "咨询内容必须在2到100个字符之间")
    private String content;

    private String templateId;
    
    private Boolean dsFlag;
}
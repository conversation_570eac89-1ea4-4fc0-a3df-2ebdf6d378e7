package com.ctyk.ywzz.entity.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: Liyh
 * @date: 2024/08/20 09:36
 * @description: 报告明细返参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Setter
@Getter
public class ReportDetailVo {

    /**
     * 总检医生
     */
    private String summaryDoctor;

    /**
     * 异常指标
     */
    private int abnormalIndex;

    /**
     * 总检明细
     */
    private List<SummaryItemVO> summaryItemList;

}

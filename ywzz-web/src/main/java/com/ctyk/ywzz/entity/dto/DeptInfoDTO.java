package com.ctyk.ywzz.entity.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: <PERSON><PERSON>
 * @date: 2024/07/17 17:36
 * @description: 科室信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class DeptInfoDTO {
    
    /**
     * 科室编码
     */
    private String deptCode;
    
    /**
     * 科室名称
     */
    private String department;
    
    /**
     * 匹配度
     */
    private String confidence;
    
    /**
     * 分院ID
     */
    private String hospitalId;
    
    /**
     * 标题
     */
    private String titleName;
    
    private String curDeptName;
    
    /**
     * 是否有号
     */
    private Boolean scheduleFlag;
    
    private String registerUrl;
    private String groupCode;
    
    @Override
    public String toString() {
        return "{" + "deptCode:'" + deptCode + '\'' + ", department:'" + department + '\'' + ", confidence:'"
                + confidence + '\'' + ", hospitalID:'" + hospitalId + '\'' + ", titleName:'" + titleName + '\''
                + ", curDeptName:'" + curDeptName + '\'' + ", scheduleFlag:'" + scheduleFlag + '\'' + '}';
    }
}
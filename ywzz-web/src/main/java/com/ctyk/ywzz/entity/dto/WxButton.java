package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: <PERSON>yh
 * @date: 2024/12/09 14:36
 * @description: 微信菜单按钮信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */

@Getter
@Setter
public class WxButton {

    /**
     * 按钮名称
     */
    private String name;

    /**
     * 按钮类型
     */
    private String type;

    /**
     * 按钮Key
     */
    private String key;

    /**
     * 按钮URL
     */
    private String url;

    /**
     * 小程序appId
     */
    @JsonProperty("appid")
    private String appId;

    /**
     * 小程序的页面路径
     */
    @JsonProperty("pagepath")
    private String pagePath;

    /**
     * 永久素材的合法media_id
     */
    @JsonProperty("media_id")
    private String mediaId;

    /**
     * 子按钮
     */
    @JsonProperty("sub_button")
    private WxButton[] subButton;
}

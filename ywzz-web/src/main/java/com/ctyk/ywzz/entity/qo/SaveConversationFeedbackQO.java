package com.ctyk.ywzz.entity.qo;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: zwj
 * @date: 2025/01/07  15:40
 * @description: 保存对话反馈实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class SaveConversationFeedbackQO {
    
    /**
     * 对话记录ID
     */
    @NotBlank(message = "对话记录ID不能为空")
    private String recordId;
    
    /**
     * 反馈类型，0-点踩；1-点赞
     */
    @NotNull(message = "反馈类型不能为空")
    @Min(value = 0, message = "反馈类型不正确")
    @Max(value = 1, message = "反馈类型不正确")
    private Integer optionType;
    
    /**
     * 对话场景，1-智慧分诊；2-信息咨询
     */
    @NotNull(message = "对话场景不能为空")
    @Min(value = 0, message = "对话场景不正确")
    @Max(value = 1, message = "对话场景不正确")
    private Integer recordScene;
    
    /**
     * 反馈选项ID
     */
    private String optionId;
    
    /**
     * 其他反馈内容
     */
    @Length(max = 500, message = "其他反馈内容长度不能超过500")
    private String otherContent;
}

package com.ctyk.ywzz.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/07/18  15:01
 * @description: maas v2.0模型服务请求实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@NoArgsConstructor
public class MaasV2ModelServerRequestDTO {
    
    /**
     * 模型编码，必填，如：Qwen-25-32B
     */
    private String model;
    
    /**
     * 最大token数，非必填，默认为4096
     */
    private Integer maxTokens = 4096;
    
    /**
     * 温度，非必填，默认为0.7
     */
    private BigDecimal temperature = new BigDecimal("0.7");
    
    /**
     * 是否流式调用，默认非流式false
     */
    private Boolean stream;
    
    
    /**
     * 历史对话信息，对象List，用于多轮对话，最大5轮，非必填
     */
    private List<OpenAiCompletionMessageDTO> messages;
    
}

package com.ctyk.ywzz.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/02/14  14:48
 * @description: 多轮对话大模型响应数据data实体类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class MultiTurnDialogueDataDTO {
    
    /**
     * 思考内容
     */
    private String thinkContent;
    
    /**
     * 健康建议或不适症描述
     */
    private String result;
    
    /**
     * 结束
     */
    private Boolean end;
    
    /**
     * 推荐的科室信息
     */
    private List<DeptInfoDTO> departments;
    
    /**
     * 需要患者信息
     */
    private Boolean requirePatient;
    
    private String imageName;
    
    private Boolean symptoms;
}

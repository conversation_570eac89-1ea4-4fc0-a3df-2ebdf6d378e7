package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: zwj
 * @date: 2025/01/08  13:37
 * @description: 查询反馈响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class QueryFeedbackVO {
    
    /**
     * 反馈id
     */
    private String feedbackId;
    
    /**
     * 对话记录ID
     */
    private String recordId;
    
    /**
     * 反馈类型，0-点踩；1-点赞
     */
    private Integer optionType;
    
    /**
     * 对话场景，1-智慧分诊；0-信息咨询
     */
    private Integer recordScene;
    
    /**
     * 反馈选项ID,多个使用,分隔
     */
    private String optionId;
    
    /**
     * 其他反馈内容
     */
    private String otherContent;
    
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;
}

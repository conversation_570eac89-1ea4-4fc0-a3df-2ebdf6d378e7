package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/09/04 11:08
 * @description: 就诊人信息反参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */

@Getter
@Setter
@JacksonXmlRootElement(localName = "Response")
public class VisitLInfoReqVO {

    /**
     * 结果标志（0成功，非0失败）
     */
    @JacksonXmlProperty(localName = "ResultCode")
    private int resultCode;

    /**
     * 结果描述
     */
    @JacksonXmlProperty(localName = "ResultMsg")
    private String resultMsg;

    /**
     * 第三方ID
     */
    @JacksonXmlProperty(localName = "OnlineId")
    private String onlineId;


    /**
     * 号源
     */
    @JacksonXmlElementWrapper(localName = "PatInfoList")
    @JacksonXmlProperty(localName = "PatInfo")
    private List<VisitListVO> visitList;
}
package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.dto
 * @author: <PERSON><PERSON>
 * @date: 2024/08/20 16:15
 * @description: 号源信息请求参数
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@JacksonXmlRootElement(localName = "Request")
@Setter
@Getter
public class ScheduleReqDTO {

    /**
     * 交易代码
     */
    @JacksonXmlProperty(localName = "TradeCode")
    private String tradeCode;

    /**
     * 预约机构
     */
    @JacksonXmlProperty(localName = "ExtOrgCode")
    private String extOrgCode;

    /**
     * 客户端类型
     */
    @JacksonXmlProperty(localName = "ClientType")
    private String clientType;

    /**
     * 医院唯一编号
     */
    @JacksonXmlProperty(localName = "HospitalId")
    private String hospitalId;

    /**
     * 操作员代码
     */
    @JacksonXmlProperty(localName = "ExtUserID")
    private String extUserId;

    /**
     * 开始日期
     */
    @JacksonXmlProperty(localName = "StartDate")
    private String startDate;

    /**
     * 结束日期
     */
    @JacksonXmlProperty(localName = "EndDate")
    private String  endDate;

    /**
     * 科室编码
     */
    @JacksonXmlProperty(localName = "DepartmentCode")
    private String departmentCode;

    /**
     * 专业代码
     */
    @JacksonXmlProperty(localName = "ServiceCode")
    private String serviceCode;

    /**
     * 医生编码
     */
    @JacksonXmlProperty(localName = "DoctorCode")
    private String doctorCode;

    /**
     * 出诊时段代码 S：上午  X：下午 Y：夜晚
     */
    @JacksonXmlProperty(localName = "RBASSessionCode ")
    private String rBASSessionCode ;

    /**
     * 查询排班的标记 N:正常的排班 S:停诊的排班
     */
    @JacksonXmlProperty(localName = "StopScheduleFlag")
    private String stopScheduleFlag;

    /**
     * 登记号
     */
    @JacksonXmlProperty(localName = "PatientID")
    private String patientId;

    /**
     * 就诊（结算）费别
     */
    @JacksonXmlProperty(localName = "BillTypeID")
    private String billTypeId;

    /**
     * 号别
     */
    @JacksonXmlProperty(localName = "SessType")
    private String sessType;

}
package com.ctyk.ywzz.entity.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: Liyh
 * @date: 2024/07/22 16:17
 * @description: 报告总检建议返回参数
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class InspectSuggestVO {

    /**
     * 报告ID
     */
    private String reportId;

    /**
     * 评审医师
     */
    private String reviewDoctor;

    /**
     * 总检建议
     */
    private List<InspectSuggestItemVO> inspectSuggestList;

    public InspectSuggestVO(String reportId, String reviewDoctor, List<InspectSuggestItemVO> inspectSuggestList) {
        this.reportId = reportId;
        this.inspectSuggestList = inspectSuggestList;
        this.reviewDoctor = reviewDoctor;
    }

}

package com.ctyk.ywzz.entity.qo;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: zwj
 * @date: 2025/01/07  15:40
 * @description: 修改对话反馈实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class UpdateConversationFeedbackQO {
    
    /**
     * 反馈类型，0-点踩；1-点赞
     */
    @NotNull(message = "反馈类型不能为空")
    @Min(value = 0, message = "反馈类型不正确")
    @Max(value = 1, message = "反馈类型不正确")
    private Integer optionType;
    
    /**
     * 反馈选项ID
     */
    private String optionId;
    
    /**
     * 其他反馈内容
     */
    @Length(max = 500, message = "其他反馈内容长度不能超过500")
    private String otherContent;
}

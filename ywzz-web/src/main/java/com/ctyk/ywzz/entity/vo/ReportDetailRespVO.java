package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: Liyh
 * @date: 2024/08/20 11:21
 * @description: 报告详细信息返参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class ReportDetailRespVO extends WebReportRespBaseVO{

    /**
     * 报告明细信息
     */
    @JacksonXmlElementWrapper(localName = "ResultList")
    @JacksonXmlProperty(localName = "ExaminationSummary")
    private List<WebReportDetailVO> reportDetail;
}

package com.ctyk.ywzz.entity.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: Liyh
 * @date: 2024/07/22 16:07
 * @description: 报告基本信息返回实体类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class ReportBaseInfoVO {

    /**
     * 报告ID
     */
    private String reportId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private String age;

    /**
     * 总指标
     */
    private String totalIndex;

    /**
     * 异常指标
     */
    private String errorIndex;

    /**
     * 加项结果
     */
    private String addItemResult;


    public ReportBaseInfoVO(String reportId, String userId, String userName, String sex, String age,
                                 String totalIndex, String errorIndex, String addItemResult) {
        this.reportId = reportId;
        this.userId = userId;
        this.userName = userName;
        this.sex = sex;
        this.age = age;
        this.totalIndex = totalIndex;
        this.errorIndex = errorIndex;
        this.addItemResult = addItemResult;
    }
}
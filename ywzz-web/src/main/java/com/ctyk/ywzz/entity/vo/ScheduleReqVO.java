package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/08/21 08:25
 * @description: 号源信息请求反参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Setter
@Getter
@JacksonXmlRootElement(localName = "Response")
public class ScheduleReqVO {

    /**
     * 结果编码 0：成功
     */
    @JacksonXmlProperty(localName = "ResultCode")
    private int resultCode;

    /**
     * 结果明细
     */
    @JacksonXmlProperty(localName = "ResultContent")
    private String resultContent;

    /**
     * 记录数量
     */
    @JacksonXmlProperty(localName = "RecordCount")
    private String recordCount;


    /**
     * 号源
     */
    @JacksonXmlElementWrapper(localName = "Schedules")
    @JacksonXmlProperty(localName = "Schedule")
    private List<ScheduleItemVO> scheduleList;
}
package com.ctyk.ywzz.entity.vo;

import com.ctyk.ywzz.entity.dto.DeptInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.vo
 * @author: <PERSON><PERSON>
 * @date: 2024/07/17 17:31
 * @description: 对话记录响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@ToString
public class ConsultInfoVO {
    
    /**
     * 分析结果
     */
    private String recommendations;
    
    /**
     * 推荐科室
     */
    private List<DeptInfoDTO> departments;
    
    /**
     * 是否存在推荐科室
     */
    private Boolean deptFlag;
    
    /**
     * 需要患者信息
     */
    private Boolean requirePatient;
    
    /**
     * 人体图名称
     */
    private String imageName;
    
    /**
     * 症状列表
     */
    private List<String> symptomList;
    
    /**
     * 提示信息
     */
    private String message;
    
    @JsonIgnore
    private String thinkContent;
    
    public ConsultInfoVO() {
        this.deptFlag = true;
    }
    
}
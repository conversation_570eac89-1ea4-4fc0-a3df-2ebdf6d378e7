package com.ctyk.ywzz.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/07/28  10:24
 * @description: OpenAiCompletionMessageDTO
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenAiCompletionMessageDTO {
    
    /**
     * 输入类型，可取值：system（系统提示词）、user（用户问题）、assistant(模型回答内容)
     */
    private String role;
    
    /**
     * 输入内容
     */
    private String content;
}

package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: zwj
 * @date: 2025/05/15  15:59
 * @description: 伴随症状响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class SymptomResponseDTO {
    
    /**
     * 症状提取
     */
    @JsonProperty("extracted_symptoms")
    private List<String> extractedSymptoms;
    
    /**
     * 待选症状
     */
    @JsonProperty("missing_symptoms")
    private List<String> missingSymptoms;
}

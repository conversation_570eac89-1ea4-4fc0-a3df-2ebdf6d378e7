package com.ctyk.ywzz.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.po
 * @author: <PERSON><PERSON>
 * @date: 2024/08/02 16:22
 * @description: 机器人账号信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@TableName("sys_robot_account")
public class SysRobotAccountPO {
    
    /**
     * 客户端主键
     */
    @TableId
    private Long accountId;
    
    /**
     * 系统ID
     */
    private String systemId;
    
    /**
     * 认证类型;01：ak/sk  02 :jwt
     */
    private String authType;
    
    /**
     * 客户端标识
     */
    private String accessKey;
    
    /**
     * 客户端密钥
     */
    private String secretKey;
    
    /**
     * IP白名单
     */
    private String ipList;
    
    /**
     * IP黑名单
     */
    private String ipBlacklist;
    
    /**
     * 有效期
     */
    private Date expiryDate;
    
    /**
     * 启用标识;1:启用  0： 停用
     */
    private int enableFlag;
    
    /**
     * 扩展字段
     */
    private String field1;
    
    /**
     * 扩展字段
     */
    private String field2;
    
    /**
     * 扩展字段
     */
    private String field3;
    
    /**
     * 更新次数
     */
    private Integer updateCount;
    
    /**
     * 删除标识
     */
    private int deleteFlag;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
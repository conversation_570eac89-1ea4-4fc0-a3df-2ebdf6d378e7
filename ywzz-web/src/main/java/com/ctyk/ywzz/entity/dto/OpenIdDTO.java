package com.ctyk.ywzz.entity.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.dto
 * @author: Liyh
 * @date: 2024/07/30 17:29
 * @description: openId入参
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class OpenIdDTO {

    /**
     * openId
     */
    @NotBlank(message = "openId不能为空")
    private String openId;
}

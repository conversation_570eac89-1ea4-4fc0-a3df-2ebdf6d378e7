package com.ctyk.ywzz.entity.qo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.entity.qo
 * @author: zwj
 * @date: 2025/02/13  18:50
 * @description: 多轮对话请求实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
public class MultiTurnDialogueQO {
    
    /**
     * 当前对话内容
     */
    @NotBlank(message = "对话内容不能为空")
    private String content;
    
    /**
     * 多轮对话记录ID
     */
    @NotNull(message = "多轮对话记录ID不能为空")
    private Long recordId;
    
    private Boolean dsFlag;
}

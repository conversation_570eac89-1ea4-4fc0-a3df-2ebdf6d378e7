package com.ctyk.ywzz.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.entity.dto
 * @author: zwj
 * @date: 2025/07/10  16:04
 * @description: maas v2.0智能体请求实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@NoArgsConstructor
public class MaasV2AgentRequestDTO {
    
    private String id;
    
    private String code;
    
    private String content;
    
    private List<?> fileUrlList = new ArrayList<>();
    
    private String customizeStr = "";
}

package com.ctyk.ywzz.entity.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.vo
 * @author: Liyh
 * @date: 2024/08/20 11:19
 * @description: 报告列表查询响应实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
public class ReportListRespVO extends WebReportRespBaseVO{

    /**
     * 报告列表信息
     */
    @JacksonXmlElementWrapper(localName = "ResultList")
    @JacksonXmlProperty(localName = "PeRecord")
    private List<WebReportInfoVO> reportInfoList;
}

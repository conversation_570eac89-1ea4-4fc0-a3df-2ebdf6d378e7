package com.ctyk.ywzz.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @project: ctyk-shc
 * @package: com.ctyk.shc.entity.dto
 * @author: zwj
 * @date: 2025/07/11  10:22
 * @description: maas v2.0知识库请求实体
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@NoArgsConstructor
public class MaasV2KnowledgeRequestDTO {
    
    /**
     * 知识库编码
     */
    private String code;
    
    /**
     * 查询内容
     */
    private String query;
    
    /**
     * 相似度阈值，默认0.2
     */
    private BigDecimal similarityThreshold = new BigDecimal("0.5");
    
    /**
     * 关键词相似度权重（混合知识库时可传），默认0.7
     */
    private BigDecimal keywordsSimilarityWeight = new BigDecimal("0.7");
    
    /**
     * 召回的知识条数，默认128
     */
    private Integer topK = 3;
}

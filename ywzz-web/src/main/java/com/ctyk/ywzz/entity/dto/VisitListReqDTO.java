package com.ctyk.ywzz.entity.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.entity.dto
 * @author: <PERSON><PERSON>
 * @date: 2024/09/04 11:03
 * @description: 就诊人列表信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@Setter
@JacksonXmlRootElement(localName = "Request")
public class VisitListReqDTO {

    /**
     * 交易代码
     */
    @JacksonXmlProperty(localName = "TradeCode")
    private String tradeCode;

    /**
     * 第三方ID
     */
    @JacksonXmlProperty(localName = "OnlineId")
    private String onlineId;
}
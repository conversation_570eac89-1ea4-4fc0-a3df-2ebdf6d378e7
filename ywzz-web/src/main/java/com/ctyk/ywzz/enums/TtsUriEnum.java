package com.ctyk.ywzz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.enums
 * @author: zwj
 * @date: 2025/05/12  14:30
 * @description: 语音文本互转uri枚举类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@AllArgsConstructor
public enum TtsUriEnum {
    
    INIT("/utservice/v2/trans/append_upload/init?", "POST", "初始化转写任务"),
    UPLOAD("/utservice/v2/trans/append_upload/upload?", "POST", "追加上传音频"),
    TRANSCRIBE("/utservice/v2/trans/transcribe?", "POST", "开始转写"),
    TEXT("/utservice/v2/trans/text?", "GET", "获取结果");
    
    private final String uri;
    
    private final String method;
    
    private final String description;
}

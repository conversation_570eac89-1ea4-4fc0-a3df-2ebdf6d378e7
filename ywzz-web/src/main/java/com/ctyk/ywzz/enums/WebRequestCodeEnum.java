package com.ctyk.ywzz.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.mediwayclient.enums
 * @author: Liyh
 * @date: 2024/08/19 08:33
 * @description: 北大一接口请求编码枚举
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
public enum WebRequestCodeEnum {

    REPORT_BASIC("MES0437", "HIPMessageServer"),

    REPORT_DETAIL("MES0438", "HIPMessageServer"),

    VISIT_LIST("MES0092", "GetPatInfoByWXId"),

    CONSULT_SCHEDULE("MES0108", "QueryAdmSchedule");
    
    /**
     * 编码
     */
    private String code;
    
    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 构造函数
     * @param code 业务编码
     * @param methodName 接口方法
     */
    WebRequestCodeEnum(String code, String methodName){
        this.code = code;
        this.methodName = methodName;
    }

    /**
     * 设置编码
     * <AUTHOR>
     * @date 2024/08/19 08:49
     * @param code 业务编码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 设置方法名
     * <AUTHOR>
     * @date 2024/08/19 08:49
     * @param methodName 接口方法
     */
    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public static WebRequestCodeEnum find(String code) {
        return Arrays.stream(WebRequestCodeEnum.values()).filter(f -> code.equalsIgnoreCase(f.getMethodName()))
                .findFirst().orElse(null);
    }
}

package com.ctyk.ywzz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpMethod;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.enums
 * @author: zwj
 * @date: 2025/06/05  15:25
 * @description: AI公司语音合成uri枚举类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Getter
@AllArgsConstructor
public enum AiUriEnum {
    
    TTS("/aipaas/voice/v1/stream/synthesis", HttpMethod.GET, "语音合成", "2202057", "wss://"),
    TTS_NEW("/aipaas/voice/v1/tts/stream", HttpMethod.GET, "语音合成", "2202094", "wss://"),
    ASR("/aipaas/lm/v1/stream/cnAsr", HttpMethod.GET, "语音识别", "2202068", "wss://");
    
    private final String uri;
    
    private final HttpMethod method;
    
    private final String description;
    
    private final String code;
    
    private final String protocol;
}

package com.ctyk.ywzz.listener;

import com.ctyk.common.core.utils.SpringUtils;
import com.ctyk.ywzz.event.ReportGenerationEvent;
import com.ctyk.ywzz.service.impl.ConsultServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.listener
 * @author: zwj
 * @date: 2025/07/14  11:04
 * @description: 报告生成事件监听器
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
public class ReportEventListener {
    
    @Async("threadPoolTaskExecutor")
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleReportGenerationEvent(ReportGenerationEvent event) {
        try {
            log.info("接收到报告生成任务，记录ID: {}", event.getRecordId());
            ConsultServiceImpl consultService = SpringUtils.getBean(ConsultServiceImpl.class);
            consultService.generatePreDiagnosisReport(event.getRecordId());
        } catch (Exception e) {
            log.error("异步生成预诊断报告失败，记录ID: {}，错误: {}", event.getRecordId(), e.getMessage(), e);
        }
    }
}

package com.ctyk.ywzz.controller;


import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.ReportDetailReqDTO;
import com.ctyk.ywzz.entity.dto.ReportIdDTO;
import com.ctyk.ywzz.entity.dto.ReportListReqDTO;
import com.ctyk.ywzz.entity.dto.ReportParseResultDTO;
import com.ctyk.ywzz.entity.vo.InspectSuggestVO;
import com.ctyk.ywzz.entity.vo.ParsePdfReportVO;
import com.ctyk.ywzz.entity.vo.ReportBaseInfoVO;
import com.ctyk.ywzz.entity.vo.ReportDetailVo;
import com.ctyk.ywzz.entity.vo.ReportListVO;
import com.ctyk.ywzz.service.ReportService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: Liyh
 * @date: 2024/07/17 16:06
 * @description: 体检报告 Controller
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/reports")
public class ReportController {
    
    @Resource
    private ReportService reportService;
    
    /**
     * 获取报告列表
     * <AUTHOR>
     * @date 2024/07/30 17:27
     * @param reportListReqDTO 请求参数
     * @return R<Map < String, List < ReportListVO>>>
     */
    @PostMapping(value = "")
    public R<Map<String, List<ReportListVO>>> getReportList(@RequestBody ReportListReqDTO reportListReqDTO) {
        return R.ok(reportService.getReportList(reportListReqDTO));
    }
    
    
    /**
     * 查询报告基本信息
     * <AUTHOR>
     * @date 2024/07/30 17:27
     * @param req 请求参数
     * @return R<ReportBaseInfoVO>
     */
    @PostMapping(value = "/base-report")
    public R<ReportBaseInfoVO> getReportBaseInfo(@RequestBody @Validated ReportIdDTO req) {
        return R.ok(reportService.getReportBaseInfo(req.getReportId()));
    }
    
    /**
     * 查询报告总检建议
     * <AUTHOR>
     * @date 2024/07/30 17:27
     * @param req 请求参数
     * @return R<InspectSuggestVO>
     */
    @PostMapping(value = "/inspect-suggest")
    public R<InspectSuggestVO> getReportInspectSuggest(@RequestBody @Validated ReportIdDTO req) {
        return R.ok(reportService.getReportInspectSuggest(req.getReportId()));
    }
    
    /**
     * 查询报告总检建议
     * <AUTHOR>
     * @date 2024/07/30 17:27
     * @param reportDetailReq 请求参数
     * @return R<InspectSuggestVO>
     */
    @PostMapping(value = "/report-detail")
    public R<ReportDetailVo> getReportDetailInfo(@RequestBody ReportDetailReqDTO reportDetailReq) {
        return R.ok(reportService.getReportDetailInfo(reportDetailReq));
    }
    
    /**
     * 上传PDF报告
     *
     * @param file PDF文件
     * @return {@link R }<{@link String }>
     */
    @PostMapping("upload-pdf")
    public R<String> uploadPdf(@RequestParam("file") MultipartFile file) {
        
        return R.ok(Constants.SUCCESS_MESSAGE,reportService.uploadPdf(file));
    }
    
    /**
     * 上传PDF报告并解析
     *
     * @param file 文件
     * @return {@link Flux }<{@link ParsePdfReportVO }>
     */
    @PostMapping(value = "upload-pdf-parse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ParsePdfReportVO> uploadPdfAndParse(@RequestParam("file") MultipartFile file) {
        
        return reportService.uploadPdfAndParse(file);
    }
    
    /**
     * 获取报告解析结果
     *
     * @param parseId 解析记录Id
     * @return {@link R }<{@link ReportParseResultDTO }>
     */
    @GetMapping("/parse-result")
    public R<ReportParseResultDTO> getReportParseResult(@RequestParam("parseId") String parseId) {
        
        return R.ok(reportService.getReportParseResult(parseId));
    }
}

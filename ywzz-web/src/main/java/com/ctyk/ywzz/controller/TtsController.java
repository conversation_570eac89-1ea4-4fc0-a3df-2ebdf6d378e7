package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.common.core.exception.ServiceException;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.qo.TextToSpeechRequestQO;
import com.ctyk.ywzz.service.TtsService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: zwj
 * @date: 2025/05/12  14:03
 * @description: 语音文本互转控制层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestController
@RequestMapping("/api/tts")
@RequiredArgsConstructor
public class TtsController {
    
    private final TtsService ttsService;
    
    /**
     * 语音转文本
     *
     * @param speechFile 语音文件
     * @return {@link R }<{@link String }>
     */
    @PostMapping("/speech-to-text")
    public R<String> speechToText(@RequestParam("file") MultipartFile speechFile) {
        
        String text = null;
        try {
            text = ttsService.speechToText(speechFile);
        } catch (Exception e) {
            log.error("语音转文本失败", e);
            throw new ServiceException("语音转文本失败");
        }
        return R.ok(Constants.SUCCESS_MESSAGE, text);
    }
    
    @PostMapping("/text-to-speech")
    public void textToSpeech(@RequestBody @Valid TextToSpeechRequestQO req, HttpServletResponse response) {
        
        try {
            // 获取音频数据（WAV格式）
            byte[] audioData = ttsService.textToSpeech(req.getText());
            // byte[] audioData = ttsService.longTextToSpeech(req.getText());

            if (audioData == null || audioData.length == 0) {
                log.error("文本转语音返回空数据");
                throw new ServiceException("文本转语音失败：返回空数据");
            }

            // 设置HTTP响应头（WAV格式）
            response.setContentType("audio/wav");
            response.setContentLength(audioData.length);

            // 设置文件下载头信息
            String filename = "speech_" + System.currentTimeMillis() + ".wav";
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");

            log.info("文本转语音成功，返回WAV文件，大小: {} bytes", audioData.length);

            // 直接写入响应输出流
            response.getOutputStream().write(audioData);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("文本转语音失败", e);
            throw new ServiceException("文本转语音失败");
        }
    }
}

package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.entity.vo.MedicineAnswerVO;
import com.ctyk.ywzz.service.IntelligentMedInquiryService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: zwj
 * @date: 2025/03/11  16:23
 * @description: 智能问药控制层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@RestController
@RequestMapping("/api/medicine")
public class IntelligentMedInquiryController {
    
    @Resource
    private IntelligentMedInquiryService intelligentMedInquiryService;
    
    /**
     * 智能问药
     *
     * @param message 问题
     * @return {@link R }<{@link Boolean }>
     */
    @GetMapping("/answer")
    public R<MedicineAnswerVO> answer(@RequestParam("message") String message) {
        
        return R.ok(intelligentMedInquiryService.answer(message));
    }
}

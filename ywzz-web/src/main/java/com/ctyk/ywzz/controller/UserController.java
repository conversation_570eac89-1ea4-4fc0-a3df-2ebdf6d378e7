package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.dto.TokenInfoDTO;
import com.ctyk.ywzz.entity.vo.SecretInfoVO;
import com.ctyk.ywzz.service.UserService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: <PERSON><PERSON>
 * @date: 2024/07/22 15:37
 * @description: 用户控制层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/users")
public class UserController {
    
    @Resource
    private UserService userService;
    
    /**
     * 获取token
     * <AUTHOR>
     * @date 2024/07/17 14:33
     * @param req 请求参数
     * @return R<String>
     */
    @PostMapping("/token")
    public R<String> getToken(@RequestBody @Valid TokenInfoDTO req) {
        return R.ok(Constants.SUCCESS_MESSAGE, userService.getToken(req));
    }
    
    /**
     * 获取秘钥
     * <AUTHOR>
     * @date 2024/07/29 14:25
     * @return R<SecretInfoVO>
     */
    @PostMapping("/secret")
    public R<SecretInfoVO> getSecret() throws Exception {
        return R.ok(Constants.SUCCESS_MESSAGE, userService.getSecret());
    }
}
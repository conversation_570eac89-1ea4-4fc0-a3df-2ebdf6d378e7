package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.entity.dto.OpenIdDTO;
import com.ctyk.ywzz.entity.vo.VisitUserVO;
import com.ctyk.ywzz.service.VisitService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: Liyh
 * @date: 2024/07/22 15:50
 * @description: 就诊信息Controller
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/visit")
public class VisitController {

    @Resource
    private VisitService visitService;
    
    /**
     * 查询就诊人列表
     *
     * @param rep 请求实体
     * @return {@link R }<{@link List }<{@link VisitUserVO }>>
     */
    @PostMapping(value = "/users")
    public R<List<VisitUserVO>> getVisitUserList(@RequestBody @Validated OpenIdDTO rep) {
        return R.ok(visitService.getVisitUserList(rep.getOpenId()));
    }
}

package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.entity.qo.SaveConversationFeedbackQO;
import com.ctyk.ywzz.entity.qo.UpdateConversationFeedbackQO;
import com.ctyk.ywzz.entity.vo.QueryFeedbackOptionListVO;
import com.ctyk.ywzz.entity.vo.QueryFeedbackVO;
import com.ctyk.ywzz.service.ConversationFeedbackService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: zwj
 * @date: 2025/01/07  15:20
 * @description: 对话反馈控制层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@RestController
@RequestMapping("/api/feedbacks")
public class ConversationFeedbackController {
    
    @Resource
    private ConversationFeedbackService conversationFeedbackService;
    
    /**
     * 保存对话反馈信息
     *
     * @param qo 实体
     * @return {@link R }<{@link String }>
     */
    @PostMapping("/feedback")
    public R<String> saveFeedback(@RequestBody @Validated SaveConversationFeedbackQO qo) {
        
        return R.ok(Constants.SUCCESS_MESSAGE,conversationFeedbackService.saveFeedback(qo));
    }
    
    /**
     * 更新对话反馈信息
     *
     * @param recordId 记录id
     * @param qo 请求实体
     * @return {@link R }<{@link String }>
     */
    @PutMapping("/feedback/{recordId}")
    public R<String> updateFeedback(@PathVariable("recordId") String recordId,
            @RequestBody @Validated UpdateConversationFeedbackQO qo) {
        
        return R.ok(Constants.SUCCESS_MESSAGE,conversationFeedbackService.updateFeedback(recordId, qo));
    }
    
    /**
     * 获取对话反馈信息
     *
     * @param recordId 记录 ID
     * @return {@link R }<{@link QueryFeedbackVO }>
     */
    @GetMapping("/feedback/{recordId}")
    public R<QueryFeedbackVO> getFeedback(@PathVariable("recordId") String recordId) {
        
        return R.ok(conversationFeedbackService.getFeedback(recordId));
    }
    
    /**
     * 删除反馈信息
     *
     * @param recordId 记录 ID
     * @return {@link R }<{@link Boolean }>
     */
    @DeleteMapping("/feedback/{recordId}")
    public R<Boolean> deleteFeedback(@PathVariable("recordId") String recordId) {
        
        return R.ok(conversationFeedbackService.deleteFeedback(recordId));
    }
    
    /**
     * 获取反馈选项信息
     *
     * @return {@link R }<{@link List }<{@link QueryFeedbackOptionListVO }>>
     */
    @GetMapping("/option")
    public R<List<QueryFeedbackOptionListVO>> listOptions() {
        
        return R.ok(conversationFeedbackService.listOptions());
    }
    
}

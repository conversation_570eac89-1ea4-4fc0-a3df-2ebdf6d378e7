package com.ctyk.ywzz.controller;


import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.entity.dto.QueryQuestionnaireDTO;
import com.ctyk.ywzz.entity.qo.ConsultQO;
import com.ctyk.ywzz.entity.qo.MultiTurnDialogueQO;
import com.ctyk.ywzz.entity.vo.QuestionnaireVO;
import com.ctyk.ywzz.entity.vo.ShcShortcutKeyTemplateVO;
import com.ctyk.ywzz.entity.vo.SmartServiceInfoVO;
import com.ctyk.ywzz.service.ConsultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: Liuyl
 * @date: 2024/07/17 13:59
 * @description: 健康咨询
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/consult")
public class ConsultController {
    
    @Resource
    private ConsultService consultService;
    
    /**
     * 获取问题示例列表
     * <AUTHOR>
     * @date 2024/07/17 17:28
     * @return R<List < QuestionnaireVO>>
     */
    @PostMapping("/question")
    public R<List<QuestionnaireVO>> getQuestionnaire(@RequestBody QueryQuestionnaireDTO request) {
        
        return R.ok(consultService.getQuestionnaireList(request));
    }
    
    /**
     * 获取问题建议及推荐科室
     * <AUTHOR>
     * @date 2024/07/17 17:53
     * @param req 请求实体
     * @return R<ConsultInfoVO>
     */
    @PostMapping("/answer")
    public R<SmartServiceInfoVO> getConsultInfo(@RequestBody @Validated ConsultQO req) {
        
        return R.ok(consultService.getConsultInfo(req));
    }
    
    /**
     * 获取快捷键信息
     * <AUTHOR>
     * @date 2024/10/11 15:31
     * @return R<ConsultInfoVO>
     */
    @PostMapping("/shortcut-keys")
    public R<List<ShcShortcutKeyTemplateVO>> getShortcutKeys() {
        return R.ok(consultService.getShortcutKeys());
    }
    
    /**
     * 获取多轮对话记录ID
     *
     * @return {@link R }<{@link Long }>
     */
    @GetMapping("/record-id")
    public R<Long> getRecordId() {
        
        return R.ok(consultService.getRecordId());
    }
    
    /**
     * 多轮对话
     *
     * @param req 要求
     * @return {@link R }<{@link SmartServiceInfoVO }>
     */
    @PostMapping("/multi-turn-dialogue")
    public R<SmartServiceInfoVO> multiTurnDialogue(@RequestBody @Validated MultiTurnDialogueQO req) {
        
        return R.ok(consultService.multiTurnDialogue(req));
    }
}
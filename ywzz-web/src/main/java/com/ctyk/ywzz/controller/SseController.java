package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.client.SseClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: zwj
 * @date: 2025/03/11  14:31
 * @description: Sse控制层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@RestController
@RequestMapping("/api/sse")
public class SseController {
    
    @Resource
    private SseClient sseClient;
    
    
    /**
     * 连接sse
     *
     * @param userId 用户 ID
     * @return {@link SseEmitter }
     */
    @GetMapping(value = "/connect/{userId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@PathVariable String userId) {
     
        return sseClient.connect(userId);
    }
    
    /**
     * 关闭sse连接
     *
     * @param userId 用户 ID
     * @return {@link R }<{@link Boolean }>
     */
    @GetMapping(value = "/close-connect/{userId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public R<Boolean> closeConnect(@PathVariable String userId) {
        
        sseClient.closeSseConnect(userId);
        return R.ok();
    }
    
}

package com.ctyk.ywzz.controller;

import com.ctyk.common.core.domain.R;
import com.ctyk.ywzz.core.Constants;
import com.ctyk.ywzz.service.ConversationRecordService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.controller
 * @author: zwj
 * @date: 2025/01/07  17:23
 * @description: 对话记录控制层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@RestController
@RequestMapping("/api/records")
public class ConversationRecordController {
    
    @Resource
    private ConversationRecordService conversationRecordService;
    
    /**
     * 获取最后一条记录id
     *
     * @param modelType 模型类型
     * @return {@link R }<{@link String }>
     */
    @GetMapping("recordId")
    public R<String> getLastRecordId(@RequestParam("modelType") String modelType) {
        
        return R.ok(Constants.SUCCESS_MESSAGE,conversationRecordService.getLastRecordId(modelType));
    }
}

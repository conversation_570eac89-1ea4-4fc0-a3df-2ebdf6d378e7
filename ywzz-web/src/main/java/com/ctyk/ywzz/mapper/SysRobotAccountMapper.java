package com.ctyk.ywzz.mapper;

import com.ctyk.ywzz.entity.po.SysRobotAccountPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @project: IG
 * @package: com.ctyk.igs.mapper
 * @author: <PERSON><PERSON>
 * @date: 2024/08/05 10:35
 * @description: 系统机器人账号持久层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface SysRobotAccountMapper {
    
    /**
     * 获取机器人帐户信息
     *
     * @param accessKey 访问密钥
     * @param secretKey 密钥
     * @return {@link SysRobotAccountPO }
     */
    SysRobotAccountPO getSysRobotAccountInfo(String accessKey, String secretKey);

}
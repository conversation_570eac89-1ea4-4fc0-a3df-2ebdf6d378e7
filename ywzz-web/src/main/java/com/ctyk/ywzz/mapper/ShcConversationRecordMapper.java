package com.ctyk.ywzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctyk.ywzz.entity.po.ShcConversationRecordPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @project: IG
 * @package: com.ctyk.igs.mapper
 * @author: Liyh
 * @date: 2024/07/31 16:01
 * @description: 对话记录持久层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface ShcConversationRecordMapper extends BaseMapper<ShcConversationRecordPO> {
    
    /**
     * 保存对话记录
     *
     * @param shcConversationRecordPo SHC谈话记录实体
     * @return int
     */
    int insertShcConversationRecord(ShcConversationRecordPO shcConversationRecordPo);
    
    
    /**
     * 按用户id和模型类型获取最后一条记录 ID
     *
     * @param userId 用户id
     * @param modelType 模块类型
     * @return {@link String }
     */
    String getLastRecordIdByUserIdAndModelType(@Param("userId") String userId, @Param("modelType") String modelType);
}

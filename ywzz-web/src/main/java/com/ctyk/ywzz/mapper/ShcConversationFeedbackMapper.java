package com.ctyk.ywzz.mapper;

import com.ctyk.ywzz.entity.po.ShcConversationFeedbackPO;
import com.ctyk.ywzz.entity.po.ShcFeedbackOptionPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @project: IG
 * @package: com.ctyk.ywzz.mapper
 * @author: zwj
 * @date: 2025/01/07  15:58
 * @description: 对话返回持久层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface ShcConversationFeedbackMapper {
    
    /**
     * 获取反馈选项信息
     *
     * @return {@link List }<{@link ShcFeedbackOptionPO }>
     */
    List<ShcFeedbackOptionPO> listOptions();
    
    /**
     * 保存对话反馈信息
     *
     * @param po 实体
     * @return int
     */
    int saveFeedback(@Param("po") ShcConversationFeedbackPO po);
    
    /**
     * 更新对话反馈信息
     *
     * @param po 实体
     * @return int
     */
    int updateFeedback(@Param("po") ShcConversationFeedbackPO po);
    
    /**
     * 根据记录id查询反馈数量
     *
     * @param recordId 记录 ID
     * @return int
     */
    int countFeedbackByRecordId(@Param("recordId") String recordId);
    
    /**
     * 获取对话反馈
     *
     * @param recordId 记录 ID
     * @return {@link ShcConversationFeedbackPO }
     */
    ShcConversationFeedbackPO getFeedback(@Param("recordId") String recordId);
    
    /**
     * 删除反馈信息
     *
     * @param recordId 记录 ID
     * @return int
     */
    int deleteFeedback(@Param("recordId") String recordId);
}

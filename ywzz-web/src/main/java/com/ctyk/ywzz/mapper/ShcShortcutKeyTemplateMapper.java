package com.ctyk.ywzz.mapper;

import com.ctyk.ywzz.entity.po.ShcShortcutKeyTemplatePO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @project: IG
 * @package: com.ctyk.ywzz.mapper
 * @author: Liyh
 * @date: 2024/10/11 14:08
 * @description: 快捷键模板数据库操作
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface ShcShortcutKeyTemplateMapper {
    
    /**
     * 获取功能按钮列表
     * 北大一项目中有，翼问智诊中前端写死的
     *
     * @return {@link List }<{@link ShcShortcutKeyTemplatePO }>
     */
    List<ShcShortcutKeyTemplatePO> getShcShortcutKeyTemplateList();
}

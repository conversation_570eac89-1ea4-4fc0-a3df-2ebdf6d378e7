package com.ctyk.ywzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctyk.ywzz.entity.po.ShcConversationRecordDetailPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @project: IG
 * @package: com.ctyk.igs.mapper
 * @author: <PERSON><PERSON>
 * @date: 2024/08/01 09:11
 * @description: 对话详细信息持久层
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface ShcConversationRecordDetailMapper extends BaseMapper<ShcConversationRecordDetailPO> {
    
    /**
     * 保存对话记录详细信息
     *
     * @param shcConversationRecordDetailPo SHC对话记录详情实体
     * @return int
     */
    int insertShcConversationRecordDetail(ShcConversationRecordDetailPO shcConversationRecordDetailPo);
}
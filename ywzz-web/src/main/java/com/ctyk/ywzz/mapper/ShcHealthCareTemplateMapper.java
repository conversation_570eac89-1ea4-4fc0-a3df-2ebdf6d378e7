package com.ctyk.ywzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctyk.ywzz.entity.dto.QueryQuestionnaireDTO;
import com.ctyk.ywzz.entity.po.ShcHealthCareTemplatePO;
import com.ctyk.ywzz.entity.vo.QuestionnaireVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @project: IG
 * @package: com.ctyk.igs.mapper
 * @author: <PERSON><PERSON>
 * @date: 2024/07/31 16:58
 * @description: 健康咨询模板
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface ShcHealthCareTemplateMapper extends BaseMapper<ShcHealthCareTemplatePO> {
    
    /**
     * 查询示例问题
     *
     * @param dto 实体
     * @return {@link List }<{@link QuestionnaireVO }>
     */
    List<QuestionnaireVO> getShcHealthCareTemplateList(@Param("dto") QueryQuestionnaireDTO dto);
}

package com.ctyk.ywzz.mapper;

import com.ctyk.ywzz.entity.dto.ShcOrganizationMapDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @project: IG
 * @package: com.ctyk.igs.mapper
 * @author: <PERSON><PERSON>
 * @date: 2024/08/05 16:29
 * @description: 组织机构映射信息
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Mapper
public interface ShcOrganizationMapMapper {
    
    /**
     * 根据科室名称、院区编码查询科室信息
     *
     * @param dataMap 数据映射
     * @return {@link List }<{@link ShcOrganizationMapDTO }>
     */
    List<ShcOrganizationMapDTO> getShcOrganizationMapList(Map<String, Object> dataMap);
    
    /**
     * 根据科室编码查询医院科室信息
     *
     * @param deptCodes 部门代码
     * @return {@link List }<{@link ShcOrganizationMapDTO }>
     */
    List<ShcOrganizationMapDTO> getShcOrganizationMapListByDeptCode(List<String> deptCodes);
}

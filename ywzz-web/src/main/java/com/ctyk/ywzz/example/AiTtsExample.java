package com.ctyk.ywzz.example;

import com.ctyk.ywzz.client.AiTtsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.example
 * @author: zwj
 * @date: 2025/06/05  17:00
 * @description: AI语音服务使用示例
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiTtsExample {
    
    private final AiTtsClient aiTtsClient;
    
    /**
     * 文本转语音示例
     */
    public void textToSpeechExample() {
        try {
            String text = "您好，欢迎使用AI语音合成服务。这是一个测试文本，用于验证语音合成功能是否正常工作。";
            
            log.info("开始文本转语音，文本内容: {}", text);
            
            // 调用文本转语音服务
            byte[] audioData = aiTtsClient.textToSpeech(text);
            
            if (audioData != null && audioData.length > 0) {
                // 保存音频文件
                String fileName = "tts_output_" + System.currentTimeMillis() + ".pcm";
                Files.write(Paths.get(fileName), audioData);
                
                log.info("文本转语音成功！音频文件已保存: {}, 大小: {} bytes", fileName, audioData.length);
            } else {
                log.warn("文本转语音返回空数据");
            }
            
        } catch (Exception e) {
            log.error("文本转语音示例执行失败", e);
        }
    }
    
    /**
     * 语音转文本示例
     */
    public void speechToTextExample() {
        try {
            // 尝试使用项目根目录下的PCM文件
            String[] possibleFiles = {
                "audio_1749092071881.pcm",
                "audio_1749092658869.pcm",
                "audio_1749094187043.pcm",
                "audio_1749104979705.pcm",
                "audio_1749105029189.pcm"
            };

            for (String fileName : possibleFiles) {
                if (Files.exists(Paths.get(fileName))) {
                    log.info("找到音频文件: {}", fileName);

                    // 读取PCM文件
                    byte[] pcmData = Files.readAllBytes(Paths.get(fileName));

                    // 创建MockMultipartFile（PCM文件）
                    MockMultipartFile pcmFile = new MockMultipartFile(
                            "audio",
                            "test_audio.pcm",
                            "audio/pcm",
                            pcmData
                    );

                    log.info("开始语音转文本，文件大小: {} bytes", pcmData.length);

                    // 调用语音转文本服务
                    String recognizedText = aiTtsClient.speechToText(pcmFile);

                    if (recognizedText != null && !recognizedText.trim().isEmpty()) {
                        log.info("语音转文本成功！识别结果: {}", recognizedText);
                    } else {
                        log.warn("语音转文本返回空结果");
                    }

                    break; // 只处理第一个找到的文件
                }
            }

            if (!Files.exists(Paths.get(possibleFiles[0]))) {
                log.info("未找到音频文件，跳过语音转文本示例");
            }

        } catch (Exception e) {
            log.error("语音转文本示例执行失败", e);
        }
    }
    
    /**
     * 创建测试用的音频文件示例
     */
    public void createTestAudioFile() {
        try {
            String testText = "这是一个测试音频文件，用于验证语音转文本功能。";

            // 先通过TTS生成音频
            byte[] audioData = aiTtsClient.textToSpeech(testText);

            if (audioData != null && audioData.length > 0) {
                // 保存为PCM文件
                String pcmFileName = "test_generated_" + System.currentTimeMillis() + ".pcm";
                Files.write(Paths.get(pcmFileName), audioData);

                log.info("测试音频文件已生成: {}", pcmFileName);

                // 然后可以用这个文件测试语音转文本
                MockMultipartFile testFile = new MockMultipartFile(
                        "audio",
                        "test.pcm",
                        "audio/pcm",
                        audioData
                );

                String recognizedText = aiTtsClient.speechToText(testFile);
                log.info("循环测试结果 - 原始文本: {}", testText);
                log.info("循环测试结果 - 识别文本: {}", recognizedText);
            }

        } catch (Exception e) {
            log.error("创建测试音频文件失败", e);
        }
    }
    
    /**
     * 完整的语音处理流程示例
     */
    public void completeWorkflowExample() {
        log.info("=== 开始完整的AI语音处理流程示例 ===");
        
        // 1. 文本转语音
        log.info("步骤1: 文本转语音");
        textToSpeechExample();
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 2. 语音转文本
        log.info("步骤2: 语音转文本");
        speechToTextExample();
        
        // 3. 循环测试
        log.info("步骤3: 循环测试（TTS -> ASR）");
        createTestAudioFile();
        
        log.info("=== 完整的AI语音处理流程示例结束 ===");
    }
    
    /**
     * 测试认证功能
     */
    public void testAuthentication() {
        try {
            log.info("测试认证功能...");
            
            // 测试生成授权头
            String authHeader = aiTtsClient.generateAuthorizationHeader(
                    "GET", 
                    "/aipaas/voice/v1/stream/synthesis", 
                    "", 
                    null, 
                    ""
            );
            
            log.info("生成的授权头: {}", authHeader);
            
            // 验证授权头格式
            String[] parts = authHeader.split("/");
            if (parts.length >= 6) {
                log.info("授权头格式正确，包含 {} 个部分", parts.length);
                log.info("认证前缀: {}", parts[0]);
                log.info("应用ID: {}", parts[1]);
                log.info("区域: {}", parts[2]);
                log.info("时间戳: {}", parts[3]);
                log.info("过期时间: {}", parts[4]);
            } else {
                log.warn("授权头格式可能不正确");
            }
            
        } catch (Exception e) {
            log.error("测试认证功能失败", e);
        }
    }
    
    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("开始运行所有AI语音服务示例...");
        
        // 1. 测试认证
        testAuthentication();
        
        // 2. 完整工作流程
        completeWorkflowExample();
        
        log.info("所有示例运行完成！");
    }
}

package com.ctyk.ywzz.mapper;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ctyk.ywzz.entity.po.ShcHealthCareTemplatePO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.mapper
 * @author: zwj
 * @date: 2025/02/19  09:24
 * @description: 健康咨询模板持久层测试类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@SpringBootTest
class ShcHealthCareTemplateMapperTest {
    
    @Resource
    private ShcHealthCareTemplateMapper shcHealthCareTemplateMapper;
    
    @Test
    void insertTemplate() {
        
        List<String> list = new ArrayList<>();
   /*     list.add("被猫抓了，伤口比较深，去哪个科室？");
        list.add("饭后尾部不适，胃胀痛，偶感恶心");
        list.add("我的喉咙持续有异物感，吞咽时有轻微疼痛，已经持续几周没有好转。");
        list.add("女，24岁持续头痛发烧3天，挂什么科？");*/
        List<ShcHealthCareTemplatePO> insertList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            
            ShcHealthCareTemplatePO po = new ShcHealthCareTemplatePO();
            po.setContent(list.get(i));
            po.setTemplateId(IdWorker.getId());
            po.setPageName("zhfz");
            po.setSeqNo(String.valueOf(i + 2));
            insertList.add(po);
        }
        shcHealthCareTemplateMapper.insert(insertList);
    }
    
}

package com.ctyk.ywzz.mapper;

import com.ctyk.ywzz.entity.po.ShcShortcutKeyTemplatePO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.mapper
 * @author: zwj
 * @date: 2025/01/21  15:29
 * @description: 快捷键模板数据库操作测试类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@SpringBootTest
@ExtendWith(MockitoExtension.class)
class ShcShortcutKeyTemplateMapperTest {
    
    @Mock
    private ShcShortcutKeyTemplateMapper shcShortcutKeyTemplateMapper;
    
    @Test
    void getShcShortcutKeyTemplateListTest() {
        // Arrange - 设置测试数据和期望行为
        List<ShcShortcutKeyTemplatePO> expectedList = Arrays.asList(
                new ShcShortcutKeyTemplatePO(),
                new ShcShortcutKeyTemplatePO()
        );
        when(shcShortcutKeyTemplateMapper.getShcShortcutKeyTemplateList()).thenReturn(expectedList);
        
        // Act - 执行被测试的方法
        List<ShcShortcutKeyTemplatePO> actualList = shcShortcutKeyTemplateMapper.getShcShortcutKeyTemplateList();
        
        // Assert - 添加断言验证结果
        assertNotNull(actualList, "The returned list should not be null");
        assertEquals(expectedList.size(), actualList.size(), "List size should match expected");
        verify(shcShortcutKeyTemplateMapper, times(1)).getShcShortcutKeyTemplateList();
    }
}
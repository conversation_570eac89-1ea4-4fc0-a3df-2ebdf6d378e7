package com.ctyk.ywzz.util;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.StringReader;

public class JacksonSoapParser {
    
    public static <T> T parseResponse(String soapResponse, Class<T> resultClass, String resultNodePath) {
        try {
            XmlMapper xmlMapper = new XmlMapper();
            
            // 创建XML解析器
            XMLInputFactory f = XMLInputFactory.newFactory();
            XMLStreamReader sr = f.createXMLStreamReader(new StringReader(soapResponse));
            
            // 跳到目标节点
            boolean found = false;
            String[] pathElements = resultNodePath.split("/");
            int pathIndex = 0;
            
            while (sr.hasNext() && !found) {
                int eventType = sr.next();
                if (eventType == XMLStreamReader.START_ELEMENT) {
                    String localName = sr.getLocalName();
                    if (localName.equals(pathElements[pathIndex])) {
                        pathIndex++;
                        if (pathIndex >= pathElements.length) {
                            found = true;
                        }
                    }
                }
            }
            
            if (found) {
                return xmlMapper.readValue(sr, resultClass);
            }
            
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
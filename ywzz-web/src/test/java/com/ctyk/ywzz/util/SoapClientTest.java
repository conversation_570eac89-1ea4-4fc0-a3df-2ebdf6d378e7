package com.ctyk.ywzz.util;

import cn.hutool.http.webservice.SoapClient;
import com.ctyk.ywzz.SoapEnvelope;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.StringReader;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz.util
 * @author: zwj
 * @date: 2025/03/14  14:53
 * @description: soap客户端测试
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@SpringBootTest
class SoapClientTest {
    
    @Test
    void test() throws JAXBException {
        
        SoapClient soapClient = new SoapClient(
                "http://webservices.oorsprong.org/websamples.countryinfo/CountryInfoService.wso");
        soapClient.setMethod("CapitalCity", "http://www.oorsprong.org/websamples.countryinfo");
        soapClient.setParam("sCountryISOCode", "CN");
        String msgStr = soapClient.getMsgStr(true);
        log.info("请求数据:{}", msgStr);
        String result = soapClient.send(true);
        log.info("响应数据:{}", result);
        
        // 解析方法1
        JAXBContext jaxbContext = JAXBContext.newInstance(SoapEnvelope.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        SoapEnvelope envelope = (SoapEnvelope) unmarshaller.unmarshal(new StringReader(result));
        
        String cityResult = envelope.getBody().getCapitalCityResponse().getCapitalCityResult();
        // 解析方法2
        CapitalCityResponse response = JacksonSoapParser.parseResponse(result, CapitalCityResponse.class,
                "Body/CapitalCityResponse");
        
        log.info("结果:{}", cityResult);
        log.info("结果:{}", response);
    }
}

package com.ctyk.ywzz.util;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "CapitalCityResponse")
public class CapitalCityResponse {
    
    @JacksonXmlProperty(localName = "CapitalCityResult")
    private String capitalCity;
    
}
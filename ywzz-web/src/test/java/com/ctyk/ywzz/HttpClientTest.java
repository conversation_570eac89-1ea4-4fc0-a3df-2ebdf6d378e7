package com.ctyk.ywzz;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.Map;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz
 * @author: zwj
 * @date: 2025/03/10  16:32
 * @description: http工具测试类
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Slf4j
@SpringBootTest
class HttpClientTest {
    
    @SneakyThrows
    @Test
    void doPostJsonTest() {
        
        String appId = "1014366783126769664";
        String appSecret = "D47FEE88F50D495E8D2AB35A179A1A50";
        Long timestamp = System.currentTimeMillis() / 1000;
        String content = appId + appSecret + timestamp;
        
        String sha256Hex = DigestUtil.sha256Hex(content);
        JSONObject body = new JSONObject();
        body.put("app_id", appId);
        body.put("sign", sha256Hex);
        body.put("timestamp", timestamp);
        body.put("file_link", timestamp);
        body.put("req_id", timestamp);
        body.put("enable_callback", Boolean.FALSE);
        Map<String, String> header = new HashMap<>();
        header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        HttpResponse httpResponse = HttpUtil.createPost("https://xhai.teleagi.cn/asr/v1/filetrans")
                .body(JSON.toJSONString(body)).addHeaders(header).execute();
        log.info("响应结果:{}", httpResponse);
        Assertions.assertNotNull(httpResponse);
        Assertions.assertTrue(httpResponse.isOk());
        
    }
    
}

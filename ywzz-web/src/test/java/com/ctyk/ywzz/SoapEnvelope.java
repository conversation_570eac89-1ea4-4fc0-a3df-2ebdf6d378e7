package com.ctyk.ywzz;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

/**
 * @project: ctyk-ywzz
 * @package: com.ctyk.ywzz
 * @author: zwj
 * @date: 2025/03/14  16:17
 * @description: TODO
 * @version: 1.0
 * @company: 中电信翼康科技有限公司
 */
@Data
@XmlRootElement(name = "Envelope", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
@XmlAccessorType(XmlAccessType.FIELD)
public class SoapEnvelope {
    
    @XmlElement(name = "Body", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
    private SoapBody body;
    
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class SoapBody {
        
        @XmlElement(name = "CapitalCityResponse", namespace = "http://www.oorsprong.org/websamples.countryinfo")
        private CapitalCityResponse capitalCityResponse;
        
        @Data
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class CapitalCityResponse {
            
            @XmlElement(name = "CapitalCityResult", namespace = "http://www.oorsprong.org/websamples.countryinfo")
            private String capitalCityResult;
        }
    }
}

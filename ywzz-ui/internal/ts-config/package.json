{"name": "@yk/ts-config", "version": "1.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/ts-config"}, "license": "MIT", "type": "module", "files": ["base.json", "node.json", "vue-app.json", "node-server.json"], "dependencies": {"@types/node": "^20.11.19", "vite": "^5.1.3"}}
// import purgeIcons from 'vite-plugin-purge-icons';
import uni from "@dcloudio/vite-plugin-uni";
import { type PluginOption } from "vite";
import vueJsx from '@vitejs/plugin-vue-jsx'

import { createAppConfigPlugin } from "./appConfig";
import { configCompressPlugin } from "./compress";
import { configHtmlPlugin } from "./html";
import { configMockPlugin } from "./mock";
import { configSvgIconsPlugin } from "./svgSprite";

interface Options {
  isBuild: boolean;
  root: string;
  compress: string;
  enableMock?: boolean;
}

async function createPlugins({
  isBuild,
  root,
  enableMock,
  compress,
}: Options) {
  const vitePlugins: (PluginOption | PluginOption[])[] = [uni() as any, vueJsx()];

  const appConfigPlugin = await createAppConfigPlugin({ root, isBuild });
  console.log(appConfigPlugin);
  
  vitePlugins.push(appConfigPlugin);

  // vite-plugin-html
  vitePlugins.push(configHtmlPlugin({ isBuild }));

  // vite-plugin-svg-icons
  vitePlugins.push(configSvgIconsPlugin({ isBuild }));

  // vite-plugin-purge-icons
  // vitePlugins.push(purgeIcons());

  // #ifdef H5
  if (isBuild) {
    // rollup-plugin-gzip
    vitePlugins.push(
      configCompressPlugin({
        compress,
      }),
    );
  }
  // #endif

  // vite-plugin-mock
  enableMock && vitePlugins.push(configMockPlugin({ isBuild }));

  return vitePlugins;
}

export { createPlugins };

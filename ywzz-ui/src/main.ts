import 'uno.css';
import 'virtual:uno.css'
import App from "./App.vue";
import {
	createSSRApp
} from 'vue'

import uvUI from '@climblee/uv-ui'
import { setupStore } from '@/store';
import { initRequest } from '@/utils/request/request';
import { setUVConfig } from '@/setting/uvSetting';
export function createApp() {
  const app = createSSRApp(App).use(uvUI);
  // 引入pinia
  setupStore(app);
  // 引入请求封装
  initRequest(app);
  // 全局配置UV-UI
  setUVConfig(app);

  return {
    app,
  };
}

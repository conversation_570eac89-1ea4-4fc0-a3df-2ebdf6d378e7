import { defineStore } from 'pinia';
import { store } from '@/store';

interface DeepSeekState {
	dsFlag?: Boolean;
}
export const useDSStore = defineStore({
	id: 'app-ds',
	state: (): DeepSeekState => ({
		dsFlag: false,//是否启用deepseek深度思考
	}),
	getters: {
		getDeepSeek(state) {
			return state.dsFlag
		},
	},
	actions: {
		setDeepSeek(flag) {
			this.dsFlag = flag;
		},
	}
})

// Need to be used outside the setup
export function useDeepSeekStore() {
	return useDSStore(store);
}
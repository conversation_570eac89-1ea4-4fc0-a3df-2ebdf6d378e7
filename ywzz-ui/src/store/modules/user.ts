import { defineStore } from 'pinia';
import { store } from '@/store';
import { getAuthCache, setAuthCache } from '@/utils/auth';
import {
	USER_INFO_KEY,
} from '@/enums/cacheEnum';
import type { UserInfo } from '#/store';
interface UserState {
	userInfo: Nullable<UserInfo>;
	token?: string;
	
}
export const useUserStore = defineStore({
	id: 'app-user',
	state: (): UserState => ({
		// user info
		userInfo: null
	}),
	getters: {
		getUserInfo(state): UserInfo {
			return state.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
		},
	},
	actions: {
		setUserInfo(info: UserInfo | null) {
			this.userInfo = info;
			this.lastUpdateTime = new Date().getTime();
			setAuthCache(USER_INFO_KEY, info);
		},
		confirmLoginOut() {
			//弹窗提示，确认退出登录
			uni.showModal({
				title: '提示',
				content: '是否确认退出登录？',
				success: async (res) => {
					if (res.confirm) {
						await this.logout(true);
					}
				}
			})
		},
	}
})

// Need to be used outside the setup
export function useUserStoreWithOut() {
	return useUserStore(store);
}
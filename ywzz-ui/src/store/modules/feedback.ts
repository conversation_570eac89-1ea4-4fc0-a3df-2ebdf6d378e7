import { defineStore } from 'pinia';
import { store } from '@/store';
import { gatFeedOptions } from '@/api/consult'

interface feedbackState {
	feedbackOPS?: Array<string>;
}
export const useFDStore = defineStore({
	id: 'app-feedback',
	state: (): feedbackState => ({
		feedbackOPS: [],//反馈选项
	}),
	getters: {
		getFeedback(state) {
			return state.feedbackOPS
		},
	},
	actions: {
		setFeedback() {
			gatFeedOptions().then(res => {
				this.feedbackOPS = res;
			})
		},
	}
})

// Need to be used outside the setup
export function useFeedBackState() {
	return useFDStore(store);
}
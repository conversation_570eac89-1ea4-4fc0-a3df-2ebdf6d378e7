import { get, post, del, put, upload, request } from '@/utils/request';

export function consult() {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			resolve('如果您的症状持续且严重，比如胸痛伴有非常严重的乏力，应立即前往急诊室 （Emergency Room）。这可能是心脏问题或其他紧急情况，他们有专业的设备和人员来及时处理。 如果是在非紧急情况下，您去内科 （General Practice）是合适的，因为胸痛可能是多种原因引起的，如心脏问题、肺部问题、消化系统问题、焦虑或肌肉骨骼问题等。但请记住，这只是一个建议，具体的科室应该由医生根据您的症状来判断。 在前往医院的路上，您可以尝试记录症状的详细情况，以便向医生提供更多信息。祝您早日恢复健康！')
		}, 1000)

	})

}

// 回答问题（包含意图识别）
export function sendConsult(data) {
	return post(
		{
			url: "/api/consult/answer",
			data,
		},
	);
}

// 问药
export function sendMessage(data) {
	return get(
		{
			url: `/api/medicine/answer`,
			data,
		},
	);
}
// 
export function gatShortcutKeys() {
	return post(
		{
			url: "/api/consult/shortcut-keys",
		},
	);
}
// 获取推荐问题
export function getQuestionnaire(data) {
	return post(
		{
			url: "/api/consult/question",
			data
		},
	);
}
// 获取公钥私钥
export function gatewayKey() {
	return post(
		{
			url: "/gateway/key",
		},
	);
}

// 获取反馈选项信息
export function gatFeedOptions() {
	return get(
		{
			url: "/api/feedbacks/option",
		},
	);
}

//提交点踩点赞信息
export function feedback(data) {
	return post(
		{
			url: "/api/feedbacks/feedback",
			data
		},
	);
}

//更新点踩点赞信息
export function updateFeedback(recordId, data) {
	return put(
		{
			url: `/api/feedbacks/feedback/${recordId}`,
			data
		},
	);
}

//删除
export function delFeedBack(recordId) {
	return del(
		{
			url: `/api/feedbacks/feedback/${recordId}`,
		},
	);
}


//获取多轮对话记录ID
export function gatRecordId() {
	return get(
		{
			url: "/api/consult/record-id",
		},
	);
}


//多轮对话
export function multiDialogue(data) {
	return post(
		{
			url: "/api/consult/multi-turn-dialogue",
			data
		},
	);
}


// 上传语音文件
export function uploadRecord(file) {
	// return post({
	return upload({
		url: `/api/tts/speech-to-text`,
		name: "file",
		file: file, // 文件路径
		filePath: URL.createObjectURL(file), // 本地文件临时路径
		// header: {
		// 	'Content-Type': 'multipart/form-data'
		// }
	}, {
		isTransformResponse: false,
	});
}


//获取语音音频
export function getAudio(data) {
	return post(
		{
			url: "/api/tts/text-to-speech",
			data,
			header: {
				'Content-Type': 'application/json',
				// 'Accept': 'audio/mpeg',
				'Content-Disposition': 'attachment; filename=demo.mp3'
			},
			responseType: 'arraybuffer'
		},
	);
}
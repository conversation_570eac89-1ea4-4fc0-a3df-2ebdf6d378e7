import { get, post } from '@/utils/request';
// import { sm2DoVerifySignature, sm4Encrypt, sm2DoSignature, sm4Decrypt } from './utils.ts'


// 获取密钥
export function gatewayKey() {
	return post(
		{
			url: "/gateway/key",
		}
	);
}
// 获取密钥
export function getSecret() {
	return post(
		{
			url: "/api/users/secret",
		}
	);
}

export function getEncryptConfig() {
	return get(
		{
			url: "/gateway/encryptConfig",
		}
	);
}
export function login(data) {
	return post({
		url: '/gateway/shc/api/tests/message',
		data
	})

}


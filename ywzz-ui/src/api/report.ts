import { get, post, upload } from '@/utils/request';

// 获取就诊人列表
export function getUserList(params) {
    return post({
        url: "/api/visit/users",
        params
    }, {
        isTransformResponse: false,
    });
}

// 查询报告列表
export function getReportList(params) {
    return post({
        url: `/api/reports`,
        params,
    }, {
        isTransformResponse: false,
    });
}

// 查询报告详情
export function getReportsDetail(params) {
    return post({
        url: `/api/reports/report-detail`,
        params
    }, {
        isTransformResponse: false,
    });
}
// 上传报告
export function uploadPdf(filePath) {
    // return post({
    return upload({
        url: `/api/reports/upload-pdf`,
        filePath: filePath, // 本地文件路径
        name: "file",
        // header: {
        //     'Content-Type': 'multipart/form-data'
        // }
    }, {
        isTransformResponse: false,
    });
}
// 报告解读
export function getReportsParse(params) {
    return get({
        url: `/api/reports/parse-result`,
        params
    }, {
        isTransformResponse: false,
    });
}
import { useUserStore } from '@/store/modules/user';
import { intersection, isArray } from 'lodash-es';
import { RoleEnum } from '@/enums/roleEnum';
// import { usePermissionStore } from '@/store/modules/permission';

/**
 * usePermission 由于小程序不支持指令，只能通过方法返回Boolean配合v-if使用
 * @returns true or false
 */
export function usePermission() {
    const userStore = useUserStore();
    // const permissionStore = usePermissionStore();

    /**
   * Determine whether there is permission
   */
    function hasPermission(value?: RoleEnum | RoleEnum[] | string | string[], def = true): boolean {
        // Visible by default
        if (!value) {
            return def;
        }
        const allCodeList = userStore.getPermCodeList as string[];
        if (!isArray(value)) {
            const splits = ['||', '&&'];
            const splitName = splits.find((item) => value.includes(item));
            if (splitName) {
                const splitCodes = value.split(splitName);
                return splitName === splits[0]
                    ? intersection(splitCodes, allCodeList).length > 0
                    : intersection(splitCodes, allCodeList).length === splitCodes.length;
            }
            return allCodeList.includes(value);
        }
        return (intersection(value, allCodeList) as string[]).length > 0;
    }

    return {
        hasPermission
    }
}
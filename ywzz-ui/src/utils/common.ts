
//隐藏部分字符串
export function hideStr(str: string, frontLen: number, endLen: number, zhanweifu: string = '*') {
	var len = str.length - frontLen - endLen;
	var xing = '';
	for (var i = 0; i < len; i++) {
		xing += zhanweifu;
	}
	return str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
}

export function toast(title: string) {
	uni.showToast({
		icon: 'none',
		title: title
	})
}

export function showConfirm(content : string) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			title: '提示',
			content: content,
			cancelText: '取消',
			confirmText: '确定',
			success: function (res) {
				resolve(res);
			}
		});
	});
}
export function showActionSheet(content : string[] = ['确定', '取消']) {
	return new Promise((resolve, reject) => {
		uni.showActionSheet({
			itemList: content,
			success: function (res) {
				resolve(res)
			},
		});
	})
}



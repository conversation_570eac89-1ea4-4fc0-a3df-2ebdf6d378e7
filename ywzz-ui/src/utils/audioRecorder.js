// utils/audioRecorder.js
import { uploadRecord } from '@/api/consult';
import * as wavEncoder from 'wav-encoder';

export class AudioRecorder {
    constructor(options = {}) {
        this.options = {
            mimeType: 'audio/wav', // 默认使用wav格式
            ...options
        };
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.encoder = null;
        this.audioContext = null;
        this.audioBuffer = null;
    }

    async start() {
        if (!navigator.mediaDevices || !window.MediaRecorder) {
            uni.showToast({
                title: '当前浏览器不支持录音功能',
                icon: 'none'
            })
        } else {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                this.mediaRecorder = new MediaRecorder(stream, {
                    mimeType: this.options.mimeType
                });
                this.mediaRecorder.ondataavailable = (e) => {
                    this.audioChunks.push(e.data);
                };

                this.mediaRecorder.start();
                return true;
            } catch (error) {
                const err = error;
                // 细化错误类型
                console.log('err.name', err.name);
                if (err.name === 'NotAllowedError') {
                    uni.showToast({
                        title: '请允许麦克风权限后重试',
                        icon: 'none'
                    })
                    throw new Error('请允许麦克风权限后重试');
                }
                if (err.name === 'NotFoundError') {
                    uni.showToast({
                        title: '未检测到可用麦克风设备',
                        icon: 'none'
                    })
                    throw new Error('未检测到可用麦克风设备');
                }
                uni.showToast({
                    title: `麦克风访问失败: ${err.message}`,
                    icon: 'none'
                })
                throw new Error(`麦克风访问失败: ${err.message}`);
            }
        }

    }

    // 生成文件（测试）
    async saveFile(file) {
        try {
            // 请求文件保存权限
            const handle = await window.showSaveFilePicker({
                suggestedName: file.name,
                types: [{
                    description: 'WAV Audio',
                    accept: { 'audio/wav': ['.wav'] }
                }]
            });
            // 创建可写流
            const writable = await handle.createWritable();
            // 写入文件内容
            await writable.write(file);
            // 关闭文件
            await writable.close();

            console.log('文件已保存到:', handle.name);
        } catch (err) {
            if (err.name !== 'AbortError') {
                console.error('保存失败:', err);
            }
        }
    }
    async stop() {
        return new Promise(async (resolve) => {
            this.mediaRecorder.onstop = async () => {
                const blob = new Blob(this.audioChunks, {
                    type: 'audio/wav'
                });
                this.audioChunks = [];
                const arrayBuffer = await blob.arrayBuffer();
                //使用第三方库输出
                // 修复 WAV 文件
                const audioBlob = await this.fixWavFile(blob);
                resolve(audioBlob);
            };
            this.mediaRecorder.stop();
        });
    }

    async stop1() {
        return new Promise((resolve) => {
            this.mediaRecorder.onstop = () => {
                const blob = new Blob(this.audioChunks, {
                    type: 'audio/wav'
                });
                this.audioChunks = [];
                resolve(blob);
            };
            this.mediaRecorder.stop();
        });
    }

    async upload(wavBlob) {
        try {

            const file = new File([wavBlob], `recording_${Date.now()}.wav`, { type: 'audio/wav' });

            console.log('录音 file', file);
            const url = URL.createObjectURL(file);
            // const url = 'blob:http://localhost:8080/3f22bc8f-a59f-43d5-bd8c-4da04f632a21'
            // console.log('录音 file.url', url);

            const formData = new FormData();
            formData.append('file', file);

            const result = await uploadRecord(file, formData);
            URL.revokeObjectURL(url)
            // console.log('result', result.data);

            return result.data;
        } catch (error) {
            throw new Error('上传失败: ' + error.message);
        }
    }

    //修复wav文件
    async fixWavFile(rawBlob) {
        try {
            // 将 Blob 转换为 ArrayBuffer
            const arrayBuffer = await rawBlob.arrayBuffer();

            // 解码音频数据
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            this.audioBuffer = audioBuffer;

            // 准备编码参数
            const audioData = {
                sampleRate: audioBuffer.sampleRate,
                channelData: []
            };

            // 提取声道数据
            for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
                audioData.channelData.push(audioBuffer.getChannelData(i));
            }

            // 编码为标准 WAV
            const wavBuffer = await wavEncoder.encode(audioData, {
                bitDepth: 16,      // 16位深度
                float: false,      // 使用整数格式
                symmetric: true    // 对称编码
            });

            // 返回修复后的 Blob
            return new Blob([wavBuffer], { type: 'audio/wav' });
        } catch (error) {
            console.error('修复 WAV 文件失败:', error);
            uni.showToast({
                title: '音频处理失败',
                icon: 'none'
            });
            return null;
        }
    }


}
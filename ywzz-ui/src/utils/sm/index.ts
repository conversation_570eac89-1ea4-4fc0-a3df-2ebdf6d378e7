import { sm3, sm2, sm4 } from 'sm-crypto-v2'
import SM4 from './sm4.js'
import { getToken, getUserId, getPrivateKey, getPublicKey } from '@/utils/auth';



// sm3加密
export const sm3HashData = (msg) => {
	return sm3(msg)
}

// sm4加密
export const sm4Encrypt = (data) => {
	const token = getToken();
	const userId = getUserId();
	const secret = sm3HashData(token + userId).substring(2, 18)
	const key = stringToHex(secret)
	let encryptData = sm4.encrypt(JSON.stringify(data), key, { output: 'array' }) // 加密，默认输出 16 进制字符串，默认使用 pkcs#7 填充（传 pkcs#5 也会走 pkcs#7 填充）
	return arrayBufferToBase64(encryptData);
}

// sm4解密
export const sm4Decrypt = (data) => {
	const token = getToken();
	const userId = getUserId();
	const secret = sm3HashData(token + userId).substring(2, 18)
	const sm4Util = new SM4({
		key: secret, //这里这个key值要与后端的一致，后端解密是根据这个key
		mode: "ecb",  // 加密的方式有两种，ecb和cbc两种，也是看后端如何定义的，不过要是cbc的话下面还要加一个iv的参数，ecb不用
		cipherType: "base64"
	});
	return sm4Util.decrypt(data, secret)
}

// sm2加签
export const sm2DoSignature = (data) => {
	return sm2.doSignature(
		sm3HashData(JSON.stringify(data)),
		getPrivateKey(),
		{
			hash: true,
			der: true,
		}
	) // 加密，默认输出 16 进制字符串，默认使用 pkcs#7 填充（传 pkcs#5 也会走 pkcs#7 填充）
}

// sm2验签
export const sm2DoVerifySignature = (data, sigValueHex) => {
	try {
		const sign = sm2.doVerifySignature(
			sm3HashData(data),
			sigValueHex,
			getPublicKey(),
			{
				hash: true,
				der: true,
			}
		) // 验签结果
		return sign

	} catch (error) {
		console.log(error);

	}

}

// arrayBuffer转换Base64
function arrayBufferToBase64(buffer) {
	let binary = "";
	const bytes = new Uint8Array(buffer);
	const len = bytes.byteLength;
	for (let i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	// window.btoa():
	// 将ascii字符串或二进制数据转换成一个base64编码过的字符串,该方法不能直接作用于Unicode字符串.
	return window.btoa(binary);
}

// 字符串转16进制hex
function stringToHex(str) {
	var val = "";
	for (var i = 0; i < str.length; i++) {
		if (val == "")
			val = str.charCodeAt(i).toString(16);
		else
			val += str.charCodeAt(i).toString(16);
	}
	return val;
}
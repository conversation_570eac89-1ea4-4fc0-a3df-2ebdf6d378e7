import { TOKEN_KEY, USER_ID_KEY , PRIVAYE_KEY, PUBLICKEY_KEY, AGREEMENT_KEY, EXPIRETIME_KEY } from '@/enums/cacheEnum';
import type { BasicKeys } from '@/utils/cache/persistent';

export function getToken() {
	return getAuthCache(TOKEN_KEY as BasicKeys);
}

export function getUserId() {
	return getAuthCache(USER_ID_KEY as BasicKeys);
}

export function getPrivateKey() {
	return getAuthCache(PRIVAYE_KEY as BasicKeys);
}
export function getPublicKey() {
	return getAuthCache(PUBLICKEY_KEY as BasicKeys);
}
export function getAgreementKey() {
	return getAuthCache(AGREEMENT_KEY as BasicKeys);
}
export function getExpireTimeKey() {
	return getAuthCache(EXPIRETIME_KEY as BasicKeys);
}

export function getAuthCache<T>(key : BasicKeys) {
	return uni.getStorageSync(key) as T;
}

export function setAuthCache(key : BasicKeys, value : any) {
	return uni.setStorageSync(key, value);
}

export function removeAuthCache(key : BasicKeys) {
	return key && uni.removeStorageSync(key);
}


export function clearAll() {
	return uni.clearStorageSync();
}
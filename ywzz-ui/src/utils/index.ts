/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl: string, obj: any): string {
  let parameters = '';
  for (const key in obj) {
    parameters += key + '=' + encodeURIComponent(obj[key]) + '&';
  }
  parameters = parameters.replace(/&$/, '');
  return /\?$/.test(baseUrl) ? baseUrl + parameters : baseUrl.replace(/\/?$/, '?') + parameters;
}


/**生成UUID */
export function uuid(len: number = 16, radix: number = 12): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  let uuid: string[] | number[] = [],
    i;
  radix = radix || chars.length;

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) {
      uuid[i] = chars[0 | (Math.random() * radix)];
    }
  } else {
    // rfc4122, version 4 form
    let r;

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }

  return uuid.join('');
}


export interface TreeReplaceKeys {
	id: string;
	parId: string; 
	children: string;
}
/*
 * 平铺的数据转树形结构
*/
export function toTree(
	list: any[] = [],
	{
		id,
		parId,
		children
	}: TreeReplaceKeys = {
		id: 'id',
		parId: 'parentId',
		children: 'children'
	},
	defaultId: string | number = 0,
) {
	if (list.length === 0) return [];
	const topParId = defaultId || list[0][parId];
	return list.filter(father => {
		const branchArr = list.filter(child => {
			return father[id] == child[parId];
		});

		if (branchArr.length > 0) {
			father[children] = branchArr;
		}
		return father[parId] == topParId;
	});
}

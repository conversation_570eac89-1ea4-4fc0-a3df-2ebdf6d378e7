import type { UserInfo } from '#/store.d.ts';
import {
	TOKEN_KEY,
	USER_ID_KEY,
	PRIVAYE_KEY,
	PUBLICKEY_KEY,
	USER_INFO_KEY,
	ROLES_KEY,
	AGREEMENT_KEY,
	EXPIRETIME_KEY
  } from '@/enums/cacheEnum';
interface BasicStore {
	[TOKEN_KEY]: string | number | null | undefined;
	[USER_ID_KEY]: string | number | null | undefined;
	[PRIVAYE_KEY]: string | number | null | undefined;
	[PUBLICKEY_KEY]: string | number | null | undefined;
	[AGREEMENT_KEY]: string | number | null | undefined;
	[EXPIRETIME_KEY]: string | number | null | undefined;
	[USER_INFO_KEY]: UserInfo;
	[ROLES_KEY]: string[];
}

export type BasicKeys = keyof BasicStore;
import type { RequestOptions, Result, RequestConfig } from '#/http';
// 请参照https://www.quanzhan.co/luch-request/guide/3.x/#request的配置
function get<T = any>(config: RequestConfig, options?: RequestOptions): Promise<T> {
  return request({ ...config, method: 'GET' }, options);
}

function post<T = any>(config: RequestConfig, options?: RequestOptions): Promise<T> {
  return request({ ...config, method: 'POST' }, options);
}
/**
 * 支付宝小程序不支持put
 */
function put<T = any>(config: RequestConfig, options?: RequestOptions): Promise<T> {
  return request({ ...config, method: 'PUT' }, options);
}

/**
 * 支付宝小程序和字节跳动小程序不支持
 */
function del<T = any>(config: RequestConfig, options?: RequestOptions): Promise<T> {
  return request({ ...config, method: 'DELETE' }, options);
}

function download<T = any>(config: RequestConfig, options?: RequestOptions): Promise<T> {
  return request({ ...config, method: 'DOWNLOAD' }, options);
}

function upload<T = any>(config: RequestConfig, options?: RequestOptions): Promise<T> {
  return request({ ...config, method: 'UPLOAD' }, options);
}

 function request<T = any>(config: RequestConfig, options?: RequestOptions):Promise<T> {
  return new Promise((resolve, reject) => {
    const { $uv } = uni as any;
    $uv.http
      .request({
        ...config,
        custom: options,
      })
      .then((res) => {
        resolve(res as T);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export {
  get,
  post,
  put,
  del,
  request,
  download,
  upload,
}
// utils/sse-util.js
export class SSEUtil {
    constructor(url, options = {}) {
        this.url = url
        this.options = {
            method: 'GET', // 默认 GET
            headers: {},
            body: null,    // POST 数据
            params: {},    // URL 参数
            ...options
        }
        this.controller = new AbortController()
        this.eventListeners = new Map()
        this.reconnectAttempts = 0
    }

    async connect() {
        try {
            const init = {
                method: this.options.method,
                headers: this.options.headers,
                body: this.options.method === 'POST' ? JSON.stringify(this.options.body) : null,
                signal: this.controller.signal
            }

            const url = this.buildURL()
            const response = await fetch(url, init)

            if (!response.ok) throw new Error(`SSE连接失败: ${response.status}`)

            this.dispatchEvent('open')

            const reader = response.body.getReader()
            const decoder = new TextDecoder()
            let buffer = ''

            while (true) {
                const { done, value } = await reader.read()
                if (done) break

                buffer += decoder.decode(value, { stream: true })
                buffer = this.processBuffer(buffer)
            }
        } catch (error) {
            this.dispatchEvent('error', error)
        } finally {
            this.dispatchEvent('close')
        }
    }

    buildURL() {
        const url = new URL(this.url)
        if (this.options.method === 'GET') {
            Object.entries(this.options.params).forEach(([key, value]) => {
                url.searchParams.append(key, value)
            })
        }
        return url
    }

    processBuffer(buffer) {
        while (true) {
            const eventEnd = buffer.indexOf('\n\n')
            if (eventEnd === -1) return buffer

            const eventData = buffer.slice(0, eventEnd)
            buffer = buffer.slice(eventEnd + 2)
            this.parseEvent(eventData)
        }
    }

    parseEvent(eventData) {
        const event = { data: '', id: '', event: 'message' }

        eventData.split('\n').forEach(line => {
            const [key, value] = line.split(/:(.*)/s)
            const field = key.trim()

            if (field === 'data') event.data += (value?.trimStart() || '') + '\n'
            if (field === 'id') event.id = value?.trim()
            if (field === 'event') event.event = value?.trim()
        })

        event.data = event.data.trimEnd()
        this.dispatchEvent(event.event, event)
    }

    addEventListener(type, listener) {
        if (!this.eventListeners.has(type)) {
            this.eventListeners.set(type, new Set())
        }
        this.eventListeners.get(type).add(listener)
    }

    removeEventListener(type, listener) {
        this.eventListeners.get(type)?.delete(listener)
    }

    dispatchEvent(type, detail) {
        this.eventListeners.get(type)?.forEach(cb => cb(detail))
    }

    close() {
        this.controller.abort()
        this.eventListeners.clear()
    }
}
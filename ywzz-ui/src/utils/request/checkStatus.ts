
// import router from '@/router';
// import { PageEnum } from '@/enums/pageEnum';
import { useUserStoreWithOut } from '@/store/modules/user';
import type { ErrorMessageMode } from '#/http';
import { ResultEnum } from '@/enums/httpEnum';


export function checkStatus(
  url: string,
  status: number,
  msg: string,
  errorMessageMode: ErrorMessageMode = 'message',
): void {
  const userStore = useUserStoreWithOut();
  let errMessage = '';
  const { $uv } = uni as any;

  switch (status) {
    case 400:
      errMessage = `${msg}`;
      break;
    case 401:
      // userStore.setToken(undefined);
      // userStore.logout(true);
      errMessage = msg || "用户没有权限（令牌、用户名、密码错误）!";
      break;
    case 403:
      errMessage = "用户得到授权，但是访问是被禁止的。!";
      break;
    // 404请求不存在
    case 404:
      errMessage = "网络请求错误,未找到该资源!";
      break;
    case 405:
      errMessage = "网络请求错误,请求方法未允许!";
      break;
    case 408:
      errMessage = "网络请求错误,请求超时!";
      break;
    case 500:
      errMessage = "服务器繁忙，请稍后再试!";
      break;
    case 501:
      errMessage = "网络未实现";
      break;
    case 502:
      errMessage = "网络错误!";
      break;
    case 503:
      errMessage = "服务不可用,服务器暂时过载或维护!";
      break;
    case 504:
      errMessage = "网络超时!";
      break;
    case 505:
      errMessage = "http版本不支持该请求!";
      break;
    default:
  }

  if (errMessage) {
    if (errorMessageMode === 'modal') {
      uni.showModal({
        title: '错误提示',
        content: errMessage,
        confirmText: '我知道了',
        showCancel: false,
        success: (res) => {
          // 如果未授权，点击直接跳转到登录页面
          // if (res.confirm && status === ResultEnum.UNAUTHORIZED) {
          //   userStore.setToken(undefined);
          //   userStore.logout(true);
          // }
        }
      });
    } else if (errorMessageMode === 'message') {
      $uv.toast(errMessage);
    }
  }
}

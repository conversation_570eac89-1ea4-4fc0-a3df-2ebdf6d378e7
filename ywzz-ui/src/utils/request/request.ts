import type { App } from "vue";
import type { RequestOptions, ResponseResult, RequestConfig } from '#/http';
import { isString, isArray, isEmpty, isNull } from 'lodash-es';
import { formatRequestDate, joinTimestamp } from './helper';
import { setObjToUrlParams } from '@/utils';
import { RequestEnum, ContentTypeEnum, ResultEnum } from '@/enums/httpEnum';
import { getToken, getUserId } from '@/utils/auth';
import { useUserStore } from "@/store/modules/user";
import { checkStatus } from './checkStatus';
import { useAppInject } from '@/hooks/web/useAppInject';
import projectSetting from '@/setting/projectSetting';
import { getSecret } from '@/api/login.ts'
import {
  EXPIRETIME_KEY,
  PRIVAYE_KEY, PUBLICKEY_KEY
} from '@/enums/cacheEnum';
import { setAuthCache, getExpireTimeKey } from '@/utils/auth';
import { sm2DoVerifySignature, sm4Encrypt, sm2DoSignature, sm4Decrypt } from '../sm/index'
const encryptUrlList = [
  // "/shc/api/consult/answer"
]

const decryptUrlList = [
  // "/shc/api/consult/answer"
]
const requestOptions: RequestOptions = {
  // 默认将prefix 添加到url
  joinPrefix: true,
  // 是否返回原生响应头 比如：需要获取响应头时使用该属性
  isReturnNativeResponse: false,
  // 需要对返回数据进行处理
  isTransformResponse: true,
  // post请求的时候添加参数到url
  joinParamsToUrl: false,
  // 格式化提交参数时间
  formatDate: true,
  // 消息提示类型
  errorMessageMode: 'message',
  // 接口拼接地址
  urlPrefix: '',
  //  是否加入时间戳
  joinTime: true,
  // 是否携带token
  withToken: true,
  // auth scheme e.g: 'Bearer'
  authenticationScheme: ''
}

const getBaseUrl = () => {
  const { uniPlatform } = useAppInject();
  return projectSetting.baseURL;
}
const prefix = () => {
  return projectSetting.prefix.slice(0, -1);
}
// 此vm参数为页面的实例，可以通过它引用vuex中的变量
const initRequest = (vm: App<Element>) => {
  const { $uv } = uni as any;
  // 初始化请求配置
  $uv.http.setConfig((config: RequestConfig & {
    custom: RequestOptions;
  }) => {
    /* config 为默认全局配置*/
    config.baseURL = getBaseUrl();
    config.timeout = 50 * 1000,
      config.header = {
        'Content-Type': ContentTypeEnum.JSON
      }
    // 如果是form-data格式
    // config.header = { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
    config.custom = { ...requestOptions }
    return config;
  });

  // 请求拦截
  $uv.http.interceptors.request.use(
    async (config: RequestConfig & {
      custom: RequestOptions;
    }) => {
      const { apiUrl, joinPrefix, joinParamsToUrl, formatDate, joinTime = true, urlPrefix, withToken, authenticationScheme } = config.custom || {};
      if (joinPrefix) {
        config.url = `${urlPrefix}${config.url}`;
      }
      if (!config.url?.startsWith('/gateway')) {
        config.url = `${prefix()}${config.url}`;
      }

      if (apiUrl && isString(apiUrl)) {
        config.baseURL = apiUrl;
      }
      // 根据custom参数中配置的是否需要token，添加对应的请求头
      const token = getToken();
      const userId = getUserId();

      if (token && withToken !== false) {
        config.header.Authorization = authenticationScheme
          ? `${authenticationScheme} ${token}`
          : token;
        config.header.token = token
        config.header.openId = userId
      }

      const params = config.params || {};
      const data = config.data || false;
      formatDate && data && !isString(data) && formatRequestDate(data);
      if (
        config.method?.toUpperCase() === RequestEnum.GET ||
        config.method?.toUpperCase() === RequestEnum.DELETE
      ) {
        if (!isString(params)) {
          // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
          config.params = Object.assign(params || {}, joinTimestamp(joinTime, false));
        } else {
          // 兼容restful风格
          config.url = config.url + params + `${joinTimestamp(joinTime, true)}`;
          config.params = undefined;
        }
      } else {
        if (!isString(params)) {
          formatDate && formatRequestDate(params);
          if (
            Reflect.has(config, 'data') &&
            config.data &&
            (Object.keys(config.data).length > 0 || config.data instanceof FormData)
          ) {
            console.log(config.url, encryptUrlList);

            if (encryptUrlList.includes(config.url as string)) {
              const encryptData = {
                ...data,
                "timestamp": Date.now()
              }
              const request = sm4Encrypt(encryptData)
              const sign = sm2DoSignature(encryptData)
              config.sourceData = data
              config.data = {
                request,
                sign
              };
            } else {
              config.data = data;
            }
            config.params = params;
          } else {
            // 非GET请求如果没有提供data，则将params视为data
            config.data = params;
            config.params = undefined;
          }
          if (joinParamsToUrl) {
            config.url = setObjToUrlParams(
              config.url as string,
              Object.assign({}, config.params, config.data),
            );
          }
        } else {
          // 兼容restful风格
          config.url = config.url + params;
          config.params = undefined;
        }
      }
      return config;
    },
    (config) => {
      return Promise.reject(config);
    },
  );

  // 响应拦截
  $uv.http.interceptors.response.use(
    async (response: ResponseResult & {
      config: RequestConfig & {
        custom: RequestOptions;
      };
    }) => {
      // if (isDecrypt(response.config.url)) {
      if (decryptUrlList.includes(response.config.url as string)) {
        const checkFail = async () => {
          if (response.config.header.errCount > 2) {
            throw new Error("请求出错，请稍候重试");
          } else if (response.config.header.errCount) {
            response.config.header.errCount++
          } else {
            response.config.header.errCount = 1
          }
          const res = await getSecret()
          setAuthCache(PRIVAYE_KEY, sm4Decrypt(res.privateKey))
          setAuthCache(PUBLICKEY_KEY, sm4Decrypt(res.publicKey))
          setAuthCache(EXPIRETIME_KEY, res.expireTime + new Date().getTime())
          response.config.data = response.config.sourceData
          return $uv.http.request(response.config)
        }
        const expireTime = getExpireTimeKey();
        if (response?.data?.code === ResultEnum.VERIFY_SIGNATURE_FAIL || new Date().getTime() >= expireTime) {
          checkFail()
        }
        const decryptData = sm4Decrypt(response.data.response)
        const verifyResult = sm2DoVerifySignature(decryptData, response.data.sign)
        if (verifyResult && decryptData) {
          const { data, code, message } = JSON.parse(decryptData)
          if (code === ResultEnum.SUCCESS) {
            return data
          }
          uni.showModal({
            title: '错误提示',
            content: message,
            confirmText: '确认',
            showCancel: false,
          });
        } else {
          checkFail()
        }
      }
      //暂时注释
      const { data } = response;



      // 自定义参数
      const { successMessageMode, isTransformResponse, isReturnNativeResponse, errorMessageMode } = response.config.custom || {};

      // 二进制数据则直接返回
      if (response.config.responseType === 'blob' || response.config.responseType === 'arraybuffer')
        return response.data;

      // 是否返回原生响应头 比如：需要获取响应头时使用该属性
      if (isReturnNativeResponse) return response;

      // 不进行任何处理，直接返回
      // 用于页面代码可能需要直接获取code，data，msg这些信息时开启
      if (!isTransformResponse) return response.data;

      //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
      const { code, data: result, msg } = data;
      // 这里逻辑可以根据项目进行修改
      const hasSuccess = data && Reflect.has(data, 'code') && code.toString() === ResultEnum.SUCCESS;

      if (hasSuccess) {
        let successMsg = msg;
        if (successMsg == null || successMsg === undefined || successMsg === '' || isEmpty(successMsg)) {
          successMsg = "操作成功";
        }

        if (successMessageMode === 'modal') {
          uni.showModal({
            title: '错误提示',
            content: successMsg,
            confirmText: '我知道了',
            showCancel: false,
          });

        } else if (successMessageMode === 'message') {
          $uv.toast(successMsg);
        }
        return result;
      }

      if (isEmpty(data)) {
        // return '[HTTP] Request has no return value';
        throw new Error("请求出错，请稍候重试");
      } else if (data.code === 500) {
        data.msg && $uv.toast(data.msg);
      }

      // 在此处根据自己项目的实际情况对不同的code执行不同的操作
      // 如果不希望中断当前请求，请return数据，否则直接抛出异常即可
      let timeoutMsg = '';
      switch (code) {
        case ResultEnum.UNAUTHORIZED:
          timeoutMsg = '登录超时,请重新登录!';
          break;
        default:
          if (msg) timeoutMsg = msg;
      }
      if (errorMessageMode === 'modal') {
        uni.showModal({
          title: '错误提示',
          content: timeoutMsg,
          confirmText: '我知道了',
          showCancel: false,
        });
      } else if (errorMessageMode === 'message') {
        $uv.toast(timeoutMsg);
      }
      throw new Error('请求出错，请稍候重试');
      // return new Promise(() => { });
    },
    (response) => responseInterceptorsCatch(response)
  );

  function responseInterceptorsCatch(res: ResponseResult & {
    config: RequestConfig & {
      custom: RequestOptions;
    };
  }): Promise<any> {
    const { data, statusCode, config } = res || {};
    const errorMessageMode = config?.custom?.errorMessageMode || 'none';
    const msg: string = data?.msg ?? '';
    checkStatus(config.url || '', statusCode, msg, errorMessageMode);
    return Promise.reject(data);
  }
};

function isEncrypt(url) {
  if (!CONFIG) return false;
  if (CONFIG.enable) {
    if (CONFIG.checkModel === "WHITE_LIST") {
      return !CONFIG.checkModelList.includes(url)
    } else if (CONFIG.checkModel === "BLACK_LIST") {
      return CONFIG.checkModelList.includes(url)
    }
  } else if (CONFIG.requestDecrypt) {
    if (CONFIG.requestDecryptCheckModel === "WHITE_LIST") {
      return !CONFIG.requestDecryptCheckModelList.includes(url)
    } else if (CONFIG.requestDecryptCheckModel === "BLACK_LIST") {
      return CONFIG.requestDecryptCheckModelList.includes(url)
    }
  }
  return false
}
function isDecrypt(url) {
  if (!CONFIG) return false;
  if (CONFIG.enable) {
    if (CONFIG.checkModel === "WHITE_LIST") {
      return !CONFIG.checkModelList.includes(url)
    } else if (CONFIG.checkModel === "BLACK_LIST") {
      return CONFIG.checkModelList.includes(url)
    }
  } else if (CONFIG.responseEncrypt) {
    if (CONFIG.responseEncryptCheckModel === "WHITE_LIST") {
      return !CONFIG.responseEncryptCheckModelList.includes(url)
    } else if (CONFIG.responseEncryptCheckModel === "BLACK_LIST") {
      return CONFIG.responseEncryptCheckModelList.includes(url)
    }
  }
  return false
}
export { initRequest };

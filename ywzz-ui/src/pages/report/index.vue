<template>
	<view class="report">
		<!-- <view class="head">
			<image class="head-logo" src="@/static/images/title-zhfx.png"></image>
			<view class="head-btn" @click="checkReport">立即查看</view>
		</view> -->
		<image src="@/static/images/zhfx-bg1.png" class="head-bg"></image>
		<view class="member-header">
			<view class="title">
				<image src="@/static/title-icon.png" class="title-icon"></image>
				<span>我的家庭</span>
			</view>
			<view class="member-list">
				<!-- <uv-tabs :list="memberList" keyName="userName" :activeStyle="memberActiveStyle" lineHeight="0"
					:inactiveStyle="memberInactiveStyle" :itemStyle="memberItemStyle" @click="memberChange"></uv-tabs> -->
				<view class="member-item" v-for="item in memberList" :key="item.userId" @click="memberChange(item)">
					<view style="position: relative;">
						<img :src="item.ava"
							style="width: 144rpx;height: 144rpx; top: -18rpx; left: -21rpx; position: absolute;">
						</img>
						<view :class="item.checked ? 'seltouxiang' : 'touxiang'"></view>
					</view>
					<div :class="item.checked ? 'selName' : 'name'">{{ item.userName }}</div>
				</view>
			</view>
		</view>
		<view class="main">
			<!-- <view class="main-tab">
				<uv-tabs :list="tabList" @click="tabChange" :activeStyle="tabActiveStyle"
					:inactiveStyle="tabInactiveStyle" :itemStyle="tabItemStyle"></uv-tabs>
			</view> -->

			<view class="main-list-content">
				<view class="title">
					<image src="@/static/title-icon.png" class="title-icon"></image>
					<span>体检报告</span>
				</view>
				<!-- <view v-if="!isDataEmpty" class="list-item" v-for="(keyitem, keyindex) in Object.keys(reportList)"
					:key="keyindex"> -->
				<view v-if="!isDataEmpty" class="list-item" v-for="(keyitem, keyindex) in objectKeyObjs"
					:key="keyindex">
					<view class="item-date">
						<!-- <image class="icon-date" src="@/static/images/icon-date.png"></image> -->
						<!-- <uv-icon name="clock-fill" color="rgb(34 140 255)" size="16"
							style="margin-right: 10rpx;"></uv-icon> -->
						<view class="date-year">{{ keyitem.year }}</view>
						<view class="date-mon">{{ keyitem.mon }}</view>
					</view>
					<view class="list-info-box" v-for="(item, index) in reportList[keyitem.item]" :key="index">
						<view class="list-info" @click="checkReportDetail(item)"
							:class="item.reportType == 1 ? '' : 'list-info-label2'">
							<view class="list-label"><text>体检报告</text></view>

							<view class="list-detail">
								<view class="detail-name">
									<view class="name">{{ item.name }}</view>
									<view class="sex">{{ item.sex }}</view>
								</view>
								<view class="card">
									<image src="@/static/card-icon.png" class="card-icon"></image>
									<text>卡号：{{ item.admId }}</text>
								</view>
							</view>
							<!-- <view class="detail-hospital"> -->
							<view class="hospital">{{ item.company }}</view>
							<!-- </view> -->
							<view class="examDate">{{ item.examDate }}</view>

						</view>
					</view>
				</view>
				<CTEmpty v-else text="暂无数据" type="list"></CTEmpty>
			</view>
			<!-- <view class="upload-btn" @click="uploadFile">
				<image class="icon-upload" src="@/static/images/icon-upload.png"></image>
				上传文件
			</view> -->
		</view>
	</view>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import CTEmpty from '@/components/ct-empty/index.vue'
import { getReportList, getUserList } from '@/api/report';
import { ResultEnum } from '@/enums/httpEnum'
import { getUserId } from '@/utils/auth';
import man from '@/static/man.png';
import women from '@/static/women.png';
import girl from '@/static/girl.png';
import boy from '@/static/boy.png';

const tabActiveStyle = {
	color: '#333333',
	fontWeight: '700',
	fontSize: '32rpx'
}
const tabInactiveStyle = {
	color: '#333333',
	fontSize: '32rpx',
	fontWeight: '400',
}
const tabItemStyle = {
	height: '80rpx'
}
const memberActiveStyle = {
	width: '156rpx',
	height: '64rpx',
	lineHeight: '64rpx',
	textAlign: 'center',
	background: 'rgb(61 187 255 / 12%)',
	fontWeight: '500',
	fontSize: '27rpx',
	borderRadius: '32rpx',
	border: '2rpx solid rgb(0 165 255)',
}
const memberInactiveStyle = {
	width: '156rpx',
	height: '64rpx',
	lineHeight: '64rpx',
	textAlign: 'center',
	background: 'rgb(228 241 255 / 71%)',
	fontWeight: '500',
	fontSize: '27rpx',
	border: '2rpx solid rgb(58 181 215 / 12%)',
	borderRadius: '32rpx',
}
const memberItemStyle = {
	height: '80rpx'
}
const tabList = ref([
	{
		name: '全部报告',
		id: ''
	},
	{
		name: '体检报告',
		id: '0'
	},
	// {
	// 	name: '检查检验单',
	// 	id: '1'
	// },
])
type memberListType = {
	userName: string,
	userId: string,
	patientNo: string,
	age: number,
	gender: string,
	checked: boolean,
	ava: string
}
const memberList = ref<memberListType[]>([])

const reportList = ref({})
const params = ref({
	patientNo: '',
	idCard: '',
	openId: getUserId()
})
// 立即查看
function checkReport() {
	uni.navigateTo({
		url: '/pages/consult/index'
	})
}
// 报告
function tabChange(item: any) {
	// params.value.reportType = item.id
	getReportInfo()
}
let currMember = ref({})
// 人员
function memberChange(selitem: any) {
	currMember.value = selitem
	memberList.value.map(item => {
		if (selitem.patientNo == item.patientNo) {
			item.checked = true
		} else {
			item.checked = false
		}

	})
	params.value.patientNo = selitem.patientNo
	getReportInfo()
}
// 人员列表
function getMemberInfo() {
	uni.showLoading({
		title: '加载中'
	});
	getUserList({ openId: getUserId() }).then((result: any) => {
		if (result.code == ResultEnum.SUCCESS) {
			memberList.value = [...memberList.value, ...result.data]
			memberList.value.map(item => {
				item.checked = false
				if (item.gender == '男') {
					if (item.age <= 14) {
						item.ava = boy
						// "../../static/boy.png"
					} else {
						item.ava = man
						// '../../static/man.png'
					}
				} else if (item.gender == '女') {
					if (item.age <= 14) {
						item.ava = girl
						// '../../static/girl.png'
					} else {
						item.ava = women
						// '../../static/women.png'
					}
				}
			})
			uni.hideLoading();
		} else {
			uni.hideLoading();
		}
		memberChange(memberList.value[0])
	}).catch(() => {
		uni.hideLoading();
	})
}
const objectKeys = ref()
const objectKeyObjs = ref()
const isDataEmpty = ref(false)
// 报告列表
function getReportInfo() {
	uni.showLoading({
		title: '加载中'
	});
	getReportList({ ...params.value }).then((result: any) => {
		if (result.code == ResultEnum.SUCCESS) {
			if (result.data) {
				reportList.value = result.data
				// reportList.value.map(item => {

				// })
				objectKeys.value = Object.keys(reportList.value)
				objectKeyObjs.value = objectKeys.value.map(item => {
					const obj = {
						year: item.split('-')[0],
						mon: item.split('-')[1],
						item: item
					}
					return obj
				})
				isDataEmpty.value = false
			} else {
				isDataEmpty.value = true
			}
			uni.hideLoading();
		} else {
			uni.hideLoading();
		}
	}).catch(() => {
		isDataEmpty.value = true
		uni.hideLoading();
	})
}
// 报告解读详情
function checkReportDetail(item) {
	let userInfo = {
		name: item.name,
		sex: item.sex,
		age: item.age,
		ava: item.ava,
	}
	uni.navigateTo({
		// url: `/pages/report-info/index?admId=${item.admId}&name=${item.name}&sex=${item.sex}&age=${item.age}`
		url: `/pages/report-info/index?userInfo=${encodeURI(JSON.stringify(userInfo))}&admId=${item.admId}`
	})
}
// 上传文件
function uploadFile() {
	console.log('上传文件')
}
onMounted(async () => {
	getMemberInfo()
	getReportInfo()
})
</script>

<style lang="scss" scoped>
.report {
	padding: 20rpx 0;
	box-sizing: border-box;
	width: 100%;
	// #ifdef H5
	height: calc(100vh - var(--window-top) - var(--window-bottom));
	// #endif
	// #ifndef H5
	height: 100vh;
	// #endif

	// .head {
	// 	background: url('@/static/images/report_bg.png') no-repeat center center;
	// 	width: 100%;
	// 	height: 332rpx;
	// 	background-size: 100% 100%;
	// 	position: relative;
	// 	padding-left: 32rpx;
	// 	box-sizing: border-box;

	// 	&-logo {
	// 		width: 340rpx;
	// 		height: 70rpx;
	// 		margin-top: 90rpx;
	// 	}

	// 	&-btn {
	// 		width: 200rpx;
	// 		height: 64rpx;
	// 		background: rgba(173, 227, 214, 0.01);
	// 		box-shadow: inset 2rpx 2rpx 12rpx 10rpx rgba(243, 255, 255, 0.58);
	// 		border-radius: 32rpx 32rpx 32rpx 32rpx;
	// 		border: 2rpx solid rgba(255, 255, 255, 0.4);
	// 		font-family: Source Han Sans, Source Han Sans;
	// 		font-weight: 500;
	// 		font-size: 28rpx;
	// 		color: #238084;
	// 		line-height: 64rpx;
	// 		text-align: center;
	// 		margin-top: 26rpx;
	// 	}
	// }
	.head-bg {
		width: 100%;
		height: 280rpx;
		margin-left: 8rpx;
	}

	.title {
		display: flex;
		padding: 15rpx 0 0 15rpx;

		span {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 700;
			font-size: 32rpx;
			color: #333333;
			line-height: 56rpx;
			text-align: left;
			margin-left: 15rpx;
		}

		.title-icon {
			width: 38rpx;
			height: 26rpx;
			margin-right: 15rpx;
			position: relative;
			left: 15rpx;
			top: 15rpx;
		}
	}

	.member-header {
		width: 100%;
		background: rgba(255, 255, 255, 0.61);
		box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.05), inset 2rpx 2rpx 2rpx 0rpx #FFFFFF;
		border-radius: 15rpx 15rpx 15rpx 15rpx;
		margin-top: 16rpx;
		// padding: 10rpx 0;



		.member-list {
			// height: 80rpx;
			padding: 10rpx 20rpx;
			display: flex;

			.member-item {
				width: 100rpx;
				margin: 0 20rpx;
				height: 170rpx;
				display: block;

				.selName {
					margin-top: 120rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					font-size: 28rpx;
					color: #557AF2;
					line-height: 40rpx;
					text-align: center;
				}

				.name {
					margin-top: 120rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
					text-align: center;
				}


				.touxiang {
					width: 96rpx;
					height: 96rpx;
					position: absolute;
					top: 0;
					border: 2px solid #FFFFFF;
					border-radius: 50%;
				}

				.seltouxiang {
					position: absolute;
					width: 96rpx;
					height: 96rpx;
					border: 3px solid #55aef2;
					top: 0;
					border-radius: 50%;
				}
			}


		}
	}

	.main {
		width: 100%;
		// height: calc(100% - 320rpx);
		background: rgba(255, 255, 255, 0.61);
		box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.05), inset 2rpx 2rpx 2rpx 0rpx #FFFFFF;
		border-radius: 15rpx 15rpx 15rpx 15rpx;
		margin-top: 16rpx;

		.main-tab {
			height: 80rpx;
			padding: 0 20rpx;

			.active {
				font-weight: 700;
				position: relative;
			}

			.active::after {
				content: '';
				display: block;
				width: 30rpx;
				height: 4rpx;
				background: #237484;
				position: absolute;
				bottom: -10rpx;
				left: 50%;
				transform: translateX(-50%);
			}

			:deep(.uv-tabs__wrapper__nav__line) {
				background: linear-gradient(to right, #dcf7ff, #72bdef) !important;
				height: 10rpx !important;
				border-radius: 5rpx !important;
			}
		}

		.main-list {
			height: 80rpx;
			padding: 0 20rpx;
		}


	}
}

.main-list-content {
	// height: calc(100vh - 530rpx);
	overflow-y: scroll;
	//padding: 20rpx;

	.list-item {
		padding: 20rpx;

		.item-date {
			display: block;
			align-items: center;

			.icon-date {
				width: 30rpx;
				height: 30rpx;
				margin-right: 10rpx;
			}

			.date-year {
				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				line-height: 46rpx;
			}

			.date-mon {
				font-size: 24rpx;
				color: #666666;
				line-height: 34rpx;
			}
		}

		.list-info-box {
			margin-left: 15rpx;
			padding: 20rpx 0 0 15rpx;
			border-left: 2rpx dashed #D8D8D8;
			position: relative;

			.list-info {
				height: 192rpx;
				// background: linear-gradient(180deg, #18b1e5 0%, #97B6FF 100%);
				box-shadow: 0rpx 8rpx 19rpx 0rpx rgba(79, 145, 203, 0.33);
				border-radius: 0rpx 15rpx 15rpx 15rpx;
				// display: flex;
				// padding: 2rpx;
				box-sizing: border-box;
				width: 580rpx;
				position: relative;
				margin-left: 35rpx;

				.hospital {
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
					position: absolute;
					bottom: 10rpx;
					left: 36rpx;
				}

				.examDate {
					position: absolute;
					bottom: 10rpx;
					right: 12rpx;
					font-size: 24rpx;
					color: #666666;
				}
			}


			.list-detail {
				// background: linear-gradient(180deg, #E9F3FF 0%, #FFFFFF 100%);
				border-radius: 15rpx 31rpx 0rpx 15rpx;
				border-image: linear-gradient(180deg, rgb(24, 174, 229), rgba(151.2776193022728, 182.39434719085693, 255, 1)) 2 2;
				display: block;
				// align-items: center;
				padding: 26rpx 0;
				flex: 1;

				.detail-name {
					display: flex;
					text-align: center;
					// border-right: 2rpx dashed#E2E2E2;
					margin-bottom: 15rpx;
					padding: 0 36rpx;

					.name {
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
						line-height: 40rpx;
					}

					.sex {
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
						line-height: 40rpx;
						margin-left: 20rpx;
					}
				}

				.card {
					font-weight: 400;
					font-size: 24rpx;
					height: 52rpx;
					color: #666666;
					background: linear-gradient(90deg, #EEF3FF 4%, rgba(239, 244, 255, 0) 100%);
					display: flex;
					text-align: center;
					align-items: center;

					.card-icon {
						height: 32rpx;
						width: 32rpx;
						padding-left: 36rpx;
					}

					// margin-top: 20rpx;
					text {
						margin-left: 5rpx;
					}
				}

			}


			.list-label {
				font-weight: 500;
				font-size: 28rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				text-align: center;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 176rpx;
				height: 48rpx;
				background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
				border-radius: 0rpx 15rpx 0rpx 15rpx;
				padding-top: 0;
				padding-right: 0;
				float: right;

				text {
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 32rpx;
				}
			}


		}

		.list-info-box:last-child {
			padding-bottom: 20rpx;
		}
	}

	.list-item:last-child {
		.list-info-box {
			border-left: none;
		}
	}
}

.upload-btn {
	width: 200rpx;
	height: 68rpx;
	background: linear-gradient(90deg, #02C3C3 0%, #37D6AE 100%);
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 28rpx;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 20rpx auto;

	.icon-upload {
		width: 32rpx;
		height: 32rpx;
		margin-right: 4rpx;
	}
}
</style>
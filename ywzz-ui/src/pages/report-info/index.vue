<template>
	<view class="report-info">
		<view class="user-detail">
			<!-- <view class="tabs-group">
				<view :class="checkbtn == '异常结果' ? 'check-btn' : 'btn-right'" @click="changeBtn('异常结果')">
					异常结果
					<image class="sel-sign" v-if="checkbtn == '异常结果'" src="../../static/sel-sign.png"></image>
				</view>
				<view :class="checkbtn == '完整报告' ? 'check-btn-left' : 'btn'" @click="changeBtn('完整报告')">
					完整报告
					<image class="sel-sign" v-if="checkbtn == '完整报告'" src="../../static/sel-sign.png"></image>
				</view>
			</view> -->
			<view class="title">
				<image src="@/static/title-icon.png" class="title-icon"></image>
				<span>异常结果</span>
			</view>
			<view class="user-info">
				<!-- <image class="user-avatar" src="../../static/women.png"></image> -->
				<img :src="ava" class="user-avatar">
				</img>
				<view class="user">
					<view class="user-name">{{ userInfo.name }}</view>
					<view class="user-other">
						<!-- <text>本人</text> -->
						<!-- <text>{{ userInfo.sex }}</text> -->
						<image class="sex-icon" v-if="userInfo.sex == '女'" src="../../static/女.png"></image>
						<image class="sex-icon" v-else src="../../static/男.png"></image>
						<text>{{ userInfo.age }}岁</text>
					</view>
				</view>
			</view>
			<view class="user-detail-info">
				<view class="info-item">
					<image style="width: 100rpx;
			height: 100rpx;" src="../../static/images/all-result.png"></image>
					<text class="detail-name">总指标</text>
					<view class="detail-count"><text>{{ reportDetailList.totalIndex || 0 }}</text>项</view>
				</view>
				<view class="info-group">
					<view class="info-item">
						<image class="item-icon" src="../../static/images/warn-result.png"></image>
						<text class="detail-name">异常结果</text>
						<view class="detail-count"><text class="text-error">{{ reportDetailList.abnormalIndex || 0
						}}</text>项
						</view>
					</view>
					<view class="info-item">
						<image class="item-icon" src="../../static/images/warn-result.png"></image>
						<text class="detail-name">加项结果</text>
						<view class="detail-count"><text>{{ reportDetailList.addItemResult || 0 }}</text>项</view>
					</view>
				</view>

			</view>
		</view>
		<view class="report-main" v-if="checkbtn == '异常结果'">
			<view class="title">
				<image src="@/static/title-icon.png" class="title-icon"></image>
				<span>总检建议</span>
			</view>
			<view class="report-doctor">
				<image class="icon-doctor" src="../../static/images/icon-doctor1.png"></image>
				<text class="doctor-name">评审医师：{{ reportDetailList.summaryDoctor }}</text>
			</view>
			<view class="report-content-item" v-for="(item, index) in inspectSuggestInfo" :key="index">
				<view class="item-title">{{ item.item }}</view>
				<view class="item-info">{{ item.itemDesc }}</view>
			</view>
		</view>
		<view class="report-main" v-else>
			<view class="title">
				<image src="@/static/title-icon.png" class="title-icon"></image>
				<span>完整报告</span>
			</view>

			<view class="report-content-item" v-for="(item, index) in inspectSuggestInfo" :key="index">
				<view class="item-title">{{ item.item }}</view>
				<view class="item-info">{{ item.itemDesc }}</view>
			</view>
		</view>
		<view class="result">
			<!-- <view class="time-bg">7x24h</view> -->
			<!-- <image class="icon-robot" src="../../static/robot.png"></image> -->
			<view class="result-detail">
				<view class="result-title">异常结果解读<text class="text-error">{{ reportDetailList.abnormalIndex }}</text>项
				</view>
				<view class="result-content">
					人工智能综合报告内检项给出的建议与指导
				</view>
				<view class="result-btn" @click="resultBtn">AI体检报告解读
					<div class="right-arrow">
						<uv-icon name="arrow-right" color="#557AF2" size="12" style="padding: 3rpx 0 0 3rpx;"></uv-icon>
					</div>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { getReportsDetail } from '@/api/report';
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue'
import man from '@/static/man.png';
import women from '@/static/women.png';
import girl from '@/static/girl.png';
import boy from '@/static/boy.png';


//当前选择的tab类型
const checkbtn = ref('异常结果')
const changeBtn = (name) => {
	checkbtn.value = name
}

// 报告详情
type reportDetailListType = {
	abnormalIndex: number,
	summaryDoctor: string

}
const reportDetailList = ref<reportDetailListType>({
	abnormalIndex: 0,
	summaryDoctor: ''
})
type inspectSuggestInfoType = {
	item: string,
	itemDesc: string
}
const inspectSuggestInfo = ref<inspectSuggestInfoType[]>([])
// AI报告解读
function resultBtn() {
	uni.navigateTo({
		url: '/pages/consult/report?reportId=' + admId.value
	})
}

const admId = ref('')
const userInfo = ref({
	name: '',
	age: 0,
	sex: '',
})
let ava: any = ref()
onLoad((options) => {
	userInfo.value = JSON.parse(options?.userInfo)
	const item = userInfo.value
	if (item.sex == '男') {
		if (Number(item.age) <= 14) {
			ava = boy
			// "../../static/boy.png"
		} else {
			ava = man
			// '../../static/man.png'
		}
	} else if (item.sex == '女') {
		if (Number(item.age) <= 14) {
			// ava = '../../static/girl.png'
			ava = girl
		} else {
			// ava = '../../static/women.png'
			ava = women
		}
	}
	admId.value = options?.admId
	getReportsDetail({ admId: admId.value }).then((res) => {
		reportDetailList.value = res.data
		inspectSuggestInfo.value = res.data.summaryItemList
	})
})
</script>

<style lang="scss" scoped>
.report-info {
	box-sizing: border-box;
	width: 100%;
	// #ifdef H5
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	// #endif
	// #ifndef H5
	min-height: 100vh;
	// #endif
	padding: 40rpx 0 20rpx;
}

.user-detail {
	// padding: 20rpx;
	// background: linear-gradient(180deg, rgb(231 243 255 / 56%) 0%, #FFFFFF 100%);
	background: rgba(255, 255, 255, 0.71);
	box-shadow: inset 2rpx 2rpx 21rpx 0rpx rgba(177, 228, 255, 0.36);
	border-radius: 15rpx 15rpx 15rpx 15rpx;
	border-top: 2rpx solid #FFFFFF;

	.title {
		display: flex;
		padding: 15rpx 0 0 15rpx;

		span {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 700;
			font-size: 32rpx;
			color: #333333;
			line-height: 56rpx;
			text-align: left;
			margin-left: 15rpx;
		}

		.title-icon {
			width: 38rpx;
			height: 26rpx;
			margin-right: 15rpx;
			position: relative;
			left: 15rpx;
			top: 15rpx;
		}
	}

	.tabs-group {
		position: absolute;
		display: flex;
		justify-content: space-around;
		width: 100%;
		height: 100rpx;
		border-radius: 15rpx 15rpx 15rpx 15rpx;

		// background-color: #dbe4fd;


		.check-btn {
			width: 50%;
			// clip-path: polygon(83% 0%, 99% 96%, -5000% 100%);
			text-align: center;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 700;
			font-size: 32rpx;
			color: #333333;
			line-height: 44rpx;
			padding-top: 25rpx;
			border-top-right-radius: 55rpx;
			// z-index: 11;
			// border: 1px solid rgba(0, 0, 0, 0.71);
			position: relative;

			// background-color: rgba(255, 255, 255, 0.71);
			.sel-sign {
				width: 35rpx;
				height: 35rpx;
				position: absolute;
				top: 66rpx;
				left: 46%;
			}
		}

		.btn {
			width: 50%;
			// clip-path: polygon(83% 0%, 99% 96%, -5000% 100%);
			text-align: center;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 32rpx;
			color: #666666;
			line-height: 48rpx;
			padding-top: 25rpx;
			background-color: #dbe4fd;
			border-bottom-left-radius: 105rpx;
			border-top-right-radius: 15rpx;
			z-index: 9;
		}

		.check-btn-left {
			width: 50%;
			// clip-path: polygon(83% 0%, 99% 96%, -5000% 100%);
			text-align: center;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 700;
			font-size: 32rpx;
			color: #333333;
			line-height: 44rpx;
			padding-top: 25rpx;
			border-top-left-radius: 55rpx;
			// z-index: 11;
			// border: 1px solid rgba(0, 0, 0, 0.71);
			position: relative;

			// background-color: rgba(255, 255, 255, 0.71);
			.sel-sign {
				width: 35rpx;
				height: 35rpx;
				position: absolute;
				top: 66rpx;
				left: 46%;
			}
		}

		.btn-right {
			width: 50%;
			text-align: center;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 32rpx;
			color: #666666;
			line-height: 48rpx;
			padding-top: 25rpx;
			background-color: #dbe4fd;
			border-bottom-right-radius: 105rpx;
			border-top-left-radius: 15rpx;
			z-index: 9;
		}
	}


	.user-info {
		position: relative;
		z-index: 10;
		margin-top: 0rpx;
		display: flex;
		align-items: center;
		box-sizing: border-box;
		// padding: 15rpx 0 0 15rpx;
		padding: 0 20rpx;
		border-radius: 15rpx 15rpx 15rpx 15rpx;
		// border-top: 2rpx solid #FFFFFF;

		.user-avatar {
			width: 200rpx;
			height: 200rpx;
			// margin: 0 20rpx;
			// border: 5px solid #FFFFFF;
			// border-radius: 50%;
			// box-shadow: 0 0 5px rgba(17, 144, 255, 0.3),
			// 	0 0 10px rgba(23, 164, 252, 0.2);
		}

		.user {
			margin-left: 10rpx;

			.user-name {
				font-size: 44rpx;
				color: #333333;
				line-height: 64rpx;
				font-family: PingFang SC;
				font-weight: bold;
			}

			.sex-icon {
				width: 44rpx;
				height: 44rpx;
				position: relative;
				top: 10rpx;
				margin-right: 20rpx;
			}

			.user-other {
				font-size: 28rpx;
				color: #666666;
				line-height: 40rpx;
				margin-top: 10rpx;

				text {
					border-right: 2rpx solid #E2E2E2;
					padding-right: 10rpx;
					margin-right: 10rpx
				}

				text:last-child {
					border-right: none;
				}
			}
		}
	}
}

.user-detail-info {
	display: flex;
	align-items: center;
	justify-content: space-around;
	// margin-top: 20rpx;
	padding: 0 10rpx 20rpx 10rpx;
	border-bottom: 2rpx solid #FFFFFF;
	border-radius: 15rpx 15rpx 15rpx 15rpx;

	.info-group {
		display: block;
		width: 50%;

		.info-item {
			width: 100%;
			height: 96rpx;
			margin-top: 8rpx;
			padding-right: 15rpx;
			border: 1px solid #E7F3FF;

		}

		margin-left: 0rpx;
	}

	.info-item {
		width: 45%;
		margin-top: 8rpx;
		height: 200rpx;
		background: linear-gradient(180deg, rgba(202, 238, 255, 0.28) 0%, rgba(198, 227, 255, 0.13) 100%);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		border: 1px solid #E7F3FF;
		// border: 2rpx solid;
		// border-image: linear-gradient(360deg, rgba(67.88760557770729, 215.02979278564453, 190.5060911178589, 0.4699999988079071), rgba(255, 255, 255, 0.9)) 2 2;
		// border: 2rpx solid rgba(134, 187, 230, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8rpx 0;
		box-sizing: border-box;

		.item-icon {
			width: 56rpx;
			height: 56rpx;
			margin-right: 16rpx;
		}

		.detail-name {
			font-weight: 500;
			font-size: 30rpx;
			color: #333333;
			margin-right: 30rpx;
		}

		.detail-count {
			font-size: 30rpx;
			color: #000000;

			text {
				font-weight: 700;
				font-family: PangMenZhengDao, PangMenZhengDao;
				margin-right: 2rpx;
				color: #557AF2;
			}

			.text-error {
				color: #F45C5C;
			}
		}
	}
}

.report-main {
	background: rgba(255, 255, 255, 0.61);
	box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.05), inset 2rpx 2rpx 2rpx 0rpx #FFFFFF;
	border-radius: 15rpx 15rpx 15rpx 15rpx;
	padding: 20rpx;
	margin-top: 20rpx;

	.title {
		display: flex;
		// padding: 15rpx 0 0 15rpx;

		span {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 700;
			font-size: 32rpx;
			color: #333333;
			line-height: 56rpx;
			text-align: left;
			margin-left: 15rpx;
		}

		.title-icon {
			width: 38rpx;
			height: 26rpx;
			margin-right: 15rpx;
			position: relative;
			left: 15rpx;
			top: 15rpx;
		}
	}

	.report-title {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 700;
		font-size: 31rpx;
		color: #333333;
		position: relative;
	}

	.report-title::after {
		content: '';
		display: inline-block;
		width: 60rpx;
		height: 10rpx;
		background: linear-gradient(to right, #dcfdff 0%, #69acea 100%);
		border-radius: 5rpx 5rpx 5rpx 5rpx;
		position: absolute;
		bottom: -12rpx;
		left: 0;
	}

	.report-doctor {
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.doctor-name {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 27rpx;
			color: #666666;
		}

		.icon-doctor {
			width: 38rpx;
			height: 38rpx;
			margin-right: 4rpx;
		}
	}

	.report-content-item {
		background: #f6fcff;
		border-radius: 15rpx 15rpx 15rpx 15rpx;
		border: 2rpx solid rgba(111, 187, 231, 0.32);
		padding: 15rpx 20rpx;
		margin-top: 16rpx;

		.item-title {
			display: flex;
			align-items: baseline;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 28rpx;
			color: #333333;
		}

		.item-title::before {
			content: '';
			display: inline-block;
			width: 12rpx;
			height: 12rpx;
			background-color: #557AF2;
			border: 2px solid #fff;
			border-radius: 50%;
			box-shadow: 0rpx 0rpx 12rpx 0rpx rgba(34, 127, 173, 0.62);
			margin-right: 10rpx;

		}

		.item-info {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 27rpx;
			color: #666666;
			line-height: 42rpx;
			padding-left: 30rpx;
			margin-top: 10rpx;
		}
	}
}

.result {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	// background: rgba(255, 255, 255, 0.61);
	box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.05), inset 2rpx 2rpx 2rpx 0rpx #FFFFFF;
	border-radius: 15rpx 15rpx 15rpx 15rpx;
	padding: 28rpx 30rpx;
	position: relative;
	background: url('../../static/images/ai-bg.png') no-repeat;
	background-size: 100% 100%;

	.time-bg {
		background: url('../../static/images/time-bg.png') no-repeat;
		background-size: 100% 100%;
		width: 140rpx;
		height: 40rpx;
		position: absolute;
		top: -8rpx;
		left: 20rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 23rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: center;
		z-index: 999;
	}

	.icon-robot {
		width: 200rpx;
		height: 200rpx;
	}

	&-title {
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		font-size: 32rpx;
		color: #333333;
		line-height: 44rpx;

		text {
			color: #FA6464;
			font-size: 44rpx;
			font-weight: 600;
		}
	}

	&-content {
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 27rpx;
		color: #666666;
		line-height: 38rpx;
		margin-top: 10rpx;
		width: 420rpx;
		overflow-wrap: break-word;
	}

	&-btn {
		width: 245rpx;
		height: 57rpx;
		background: #557AF2;
		//  linear-gradient(90deg, #65c8e9 0%, #3771d6 100%);
		box-shadow: inset 4rpx 4rpx 4rpx 0rpx rgba(208, 255, 246, 0.5);
		border-radius: 20rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 350;
		font-size: 27rpx;
		color: #FFFFFF;
		line-height: 57rpx;
		text-align: center;
		margin: 20rpx 0 0;
		display: flex;
		padding: 0 20rpx;

		// auto 0;
		.right-arrow {
			width: 28rpx;
			height: 28rpx;
			text-align: center;
			border-radius: 50%;
			background-color: #FFFFFF;
			margin: 15rpx 0 0 25rpx;
		}
	}
}

.text-error {
	color: #F45C5C;
}
</style>
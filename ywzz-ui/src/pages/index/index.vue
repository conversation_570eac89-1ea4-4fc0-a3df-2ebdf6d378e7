<template>
	<view class="consult-content">
		<div class="consult-content__chat-list">
			<template v-for="(item, index) in chatContent" :key="index">
				<Suggest v-if="item.type === 'suggest'" @check="handleSuggest" />
				<Question v-else-if="item.type === 'question'" :content="item.content" />
			</template>
		</div>
	</view>
	<ChatInput ref="chatInput" @chat="handleChat" :question="question" :type="reportId ? 2 : 1" />
	<uv-toast ref="toast"></uv-toast>
	<Popup />
	<div class="page-bottom" />
</template>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script> -->

<script lang="ts" setup>
import { ref } from "vue"
import Suggest from '../consult/components/suggest.vue'
import Question from '../consult/components/question.vue'
import ChatInput from '../consult/components/chatInput.vue'
import Popup from '../consult/components/popup.vue'
// import CryptoJS from 'crypto-js';
// const keyId = '1014366783126769664'// "john-key"; // 密钥 ID
// const secretKey = "D47FEE88F50D495E8D2AB35A179A1A50"; // 秘密密钥
// const gmtTime = new Date().toUTCString();
// const data = keyId + secretKey + gmtTime
// const hmac = CryptoJS.SHA256(data).toString(CryptoJS.enc.Hex);
// console.log('语音助手生成签名hmac', hmac);
type ChatContentType = {
	type: 'suggest' | 'report' | 'question',
	content?: string,
	departments?: any[],
	message?: string
}

const reportId = ref('')
const chatContent = ref<ChatContentType[]>([
	{
		type: 'suggest',
	}
])
const chatInput = ref()

// 推荐问题
const handleSuggest = (message, templateId = null, pageName) => {
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	chatInput.value.handleInput(message, templateId, pageName)
}
const question = ref(false)
const toast = ref()

// 问答
const handleChat = async ({ type, content, templateId, pageName }) => {
	if (pageName == 'znwy') {
		uni.navigateTo({
			url: `/pages/consult/medication?content=${content}&type=${type}&templateId=${templateId || ''}`,
		});
	} else if (pageName == 'xxzx') {
		uni.navigateTo({
			url: `/pages/consult/info?content=${content}&type=${type}&templateId=${templateId || ''}`,
		});
	} else {
		uni.navigateTo({
			url: `/pages/consult/index?content=${content}&type=${type}&templateId=${templateId || ''}`,
		});
	}

}

</script>

<style lang="scss" scoped>
.consult-content {
	padding-bottom: 400rpx;
}

.page-bottom {
	// margin-top:150rpx;
	height: 2rpx;
}
</style>
<template>
	<view class="content">
		<div>data</div>
		<uv-textarea placeholder="data" v-model="data" height="200" />

		<div>count: {{count}}</div>
		<u-button type="success" @click="()=>{
			if (count > 0 && count < 100) return
			count = 0 ;
			fullClose()
		}">测试</u-button>

		<div>验签结果: {{verifyResult}}</div>

		<div>解密结果: {{decryptData}}</div>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from "vue"
	import { login } from '@/api/login.ts'
	const data = ref('')

	const count = ref(0)
	const verifyResult = ref()
	const decryptData = ref('')
	const handleLogin = async () => {
		try {
			const res = await login({
				"message": data.value,
			})
			if (res.data === data.value) {
				verifyResult.value = true
				decryptData.value = res
				if (count.value < 1000) {
					setTimeout(()=>{
						count.value++;
						fullClose()
					},100)
				}
			}else{
				console.log(data.value);
				console.log(res.data);
				verifyResult.value = false
				decryptData.value = res.data
			}
			
		} catch (e) {
			verifyResult.value = false
			decryptData.value = e
			
		} 
	};
	function fullClose(n = 5, m = 6000) {
		let result = Math.random() * (m + 1 - n) + n;
		while (result > m) {
			result = Math.random() * (m + 1 - n) + n;
		}
		generateRandomString(Math.floor(result));
	}
	function generateRandomString(length) {
		const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+~`|}{[]:;?><,./-='; // Add any additional special characters you want to include
		let result = '';
		for (let i = 0; i < length; i++) {
			const randomIndex = Math.floor(Math.random() * characters.length);
			result += characters.charAt(randomIndex);
		}
		data.value = result
		handleLogin()
	}
</script>

<style lang="scss">
	.content {
		padding: 20px;
		box-sizing: border-box;
		background: #fff;
		width: 100%;
		// #ifdef H5
		height: calc(100vh - var(--window-top) - var(--window-bottom));
		// #endif
		// #ifndef H5
		height: 100vh;
		// #endif
	}
</style>
<template>
    <view>
        <svg :style="{ width: width, height: height }" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="500" height="500"
            viewBox="0 0 500 500">
            <defs>
                <clipPath id="master_svg0_13_2273">
                    <rect x="0" y="0" width="500" height="500" rx="0" />
                </clipPath>
            </defs>
            <g clip-path="url(#master_svg0_13_2273)">
                <g transform="matrix(-1,0,0,1,872,0)">
                    <g>
                        <path
                            d="M445.9086,312.0639679641724Q449.7783,313.93496796417236,457.7739,315.07496796417234C459.4782,315.3689679641724,461.7974,315.85596796417235,463.3863,316.5349679641724C465.5646,317.48296796417236,466.2565,318.2129679641724,467.3584,320.5059679641724C469.08820000000003,324.10596796417235,466.1924,329.89596796417237,464.0782,332.79096796417235C460.657,337.47896796417234,459.4525,340.4119679641724,459.4525,345.1259679641724C459.4525,349.8399679641724,461.7846,353.20896796417236,465.2058,355.82196796417236C466.4359,356.75796796417234,467.0253,356.9749679641724,467.32,357.10296796417236C467.525,357.34696796417234,461.2336,361.5999679641724,465.3595,373.85796796417236C466.8715,378.36796796417235,472.4839,381.91596796417235,476.251,385.23396796417234C476.6739,385.6049679641724,479.1597,387.5779679641724,479.0572,391.0879679641724C478.9291,395.85296796417236,474.4315,402.33396796417236,474.124,403.02596796417237C470.9463,410.08396796417236,471.2666,417.60396796417234,473.5218,422.4199679641724C477.1481,430.15696796417234,481.4918,438.90596796417236,494.21569999999997,444.5299679641724C501.545,447.77096796417237,520.3297,445.25996796417235,536.744,449.55096796417234C547.059,452.24096796417234,549.404,460.0809679641724,552.133,462.9239679641724C556.1179999999999,467.07496796417234,555.9639999999999,500.99496796417236,557.181,509.99996796417236L777.703,509.99996796417236C776.293,502.19896796417237,774.986,472.8259679641724,774.345,465.35796796417236C768.1569999999999,393.87996796417235,766.7090000000001,363.96896796417235,771.693,349.53296796417237C783.046,316.6499679641724,806.648,275.36496796417237,814.5930000000001,251.21796796417237C829.405,206.15396796417235,829.469,173.61696796417237,800.011,118.01036796417236C782.713,85.35846796417236,766.85,71.28066796417237,732.0219999999999,58.57336796417236C697.182,45.86618796417236,646.761,45.69966796417236,607.91,50.38801796417236C569.06,55.07636796417236,526.5443,71.90826796417237,512.4751,80.60606796417235C507.92629999999997,83.41136796417237,495.6125,97.82236796417237,492.71659999999997,103.30486796417236C485.1823,117.61326796417237,469.8573,178.47196796417236,469.3576,189.52696796417237C468.7169,203.65596796417236,475.8028,211.61096796417237,476.9817,218.80996796417236C477.6223,222.72996796417237,471.3822,233.05396796417236,468.781,239.06196796417237C467.5381,241.55996796417236,464.2193,246.74796796417237,458.2354,258.3019679641724C456.07,261.86296796417236,445.02469,280.50096796417233,444.56342,281.30796796417235C441.29599,287.08596796417237,436.849648,292.19696796417236,436.208971,295.9499679641724C434.63293,305.28796796417237,442.37228,310.36096796417235,445.93448,312.07696796417235L445.9086,312.0639679641724Z"
                            fill="#F1CFBA" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M700.1291622741406,252.46089994140624Q700.2444407441407,252.35842014140624,700.3085707441406,252.30718994140625C700.1676404441406,252.42247294140626,700.1035101441406,252.47372194140624,700.1291622741406,252.46089994140624Z"
                            fill="#473C33" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M465.98739249206545,306.953356171875C463.4246824920654,307.401703171875,460.56728249206543,308.324006171875,459.49091249206543,308.926036171875C455.72373249206544,311.065256171875,461.02855249206544,309.925196171875,465.0776024920654,309.681806171875C472.4710824920654,309.246286171875,471.33058249206545,310.258246171875,474.96958249206546,311.359876171875C478.32678249206543,312.371836171875,480.14628249206544,310.488826171875,479.73618249206544,309.284706171875C479.4414824920654,308.400836171875,479.1852824920654,306.761199171875,473.40628249206543,306.453759071875C471.62518249206545,306.364081771875,469.49818249206544,306.325654271875,465.9744824920654,306.940534171875L465.98739249206545,306.953356171875Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M494.03622303466796,368.10684685820314Q484.86182303466796,372.4877846582031,480.761423034668,373.19229465820314Q468.511693034668,375.3058946582031,467.25599613466795,373.3075846582031Q466.78190103466795,376.3819046582031,471.98419303466795,377.34262465820314C474.59813303466797,377.8165846582031,481.696923034668,377.3938746582031,485.246223034668,376.01040465820313Q496.714323034668,371.52701465820314,494.03622303466796,368.0940246582031L494.03622303466796,368.10684685820314Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M488.14204741088867,205.82079432128907C491.2429074108887,212.63560432128907,495.2920274108887,210.48350432128908,499.80232741088867,208.22900432128907C506.28602741088866,204.98817432128905,515.1657274108886,206.46128432128907,524.7759274108887,205.57741432128907C531.2467274108886,204.97535432128907,537.9866274108887,202.91299432128906,547.0970274108887,205.21874432128905C559.6671274108887,208.40840432128905,563.9084274108886,211.95660432128906,566.0739274108887,213.48100432128905C569.9180274108887,216.18380432128907,575.5175274108886,220.55190432128907,576.1069274108887,218.19490432128907C576.6964274108886,215.83800432128908,560.9870274108887,204.12990432128908,553.4910274108887,199.91550432128906C549.6469274108887,197.75066492128906,546.4179274108886,197.35356732128906,529.8116274108887,198.31429432128905C526.2750274108887,198.51925432128905,515.3964274108887,198.50644232128906,510.0787274108887,199.09568432128907C504.76112741088866,199.68493432128906,497.1754974108887,200.50475432128906,492.11413741088865,201.23490432128906C489.5001974108887,201.60638432128906,487.5140404108887,204.42451432128905,488.1419694108887,205.80797432128907L488.14204741088867,205.82079432128907Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M500.35328821411133,399.1831918261719C498.5978882141113,396.90307182617187,496.66298821411135,395.2890318261719,491.7810882141113,394.2130218261719Q488.6288882141113,393.5212918261719,478.3908882141113,394.2770518261719L479.08279121411135,390.67753362617185Q488.5519882141113,390.4213238261719,493.5876882141113,391.2795908261719C499.2769882141113,392.2531418261719,500.6223882141113,396.7237118261719,501.4552882141113,398.50428182617185C502.82628821411134,401.4249218261719,501.94218821411135,401.28402182617185,500.3404882141113,399.1831918261719L500.35328821411133,399.1831918261719Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M552.9400991699218,235.64176702148438C551.9918991699219,235.30871702148437,551.1718991699219,235.06532702148436,551.0437991699218,235.02690702148436C542.9071991699219,232.1831470214844,539.8446991699219,230.68441702148436,530.9264991699218,228.96791702148437C529.9654991699218,228.78858702148437,523.7892991699218,227.96876702148438,517.7668991699219,228.14809702148438C516.4727691699219,228.18652702148438,514.2175691699218,228.08404702148437,510.2582291699219,227.39233702148437C509.24598916992187,227.21299702148437,507.5801671699219,226.32912602148437,506.95231616992186,225.93202842148438C506.50387416992186,225.65021302148438,506.5807521699219,227.41795702148437,507.5801671699219,228.31463702148437C509.91223916992186,230.4026170214844,510.98853916992186,230.99185702148438,512.4492991699219,231.37614702148437C515.1017091699218,232.08068702148438,527.4154991699219,232.55463702148438,531.5286991699219,233.4769370214844C538.7554991699219,235.10377702148438,540.6006991699219,236.16694702148436,541.0875991699219,236.5128470214844C541.9845991699219,237.12764702148436,538.6273991699219,240.12514702148437,538.3070991699219,240.36854702148437C538.1404991699219,240.48384702148437,533.3866991699218,243.00734702148438,528.5303991699219,244.60854702148438C523.9815991699219,246.09444702148437,521.7006991699219,246.01764702148438,520.9831991699218,246.03044702148438L517.4850991699219,245.74864702148437C515.7552891699219,245.63334702148438,515.0249091699219,245.49244702148437,513.3463391699219,246.05604702148437Q509.5919791699219,247.84944702148437,512.2443891699219,247.47794702148437C513.7563791699218,247.26014702148439,515.6271891699218,246.9271470214844,516.7803991699219,247.09364702148437C517.9335991699219,247.26014702148439,515.4733491699219,247.73414702148438,514.8326791699219,248.70764702148438Q512.7568891699219,251.8844470214844,514.5123391699219,250.32164702148438C517.3825991699218,248.25934702148436,521.7263991699218,248.98944702148438,524.7502991699218,248.40024702148438C525.9035991699219,248.16964702148437,526.9798991699219,248.13124702148437,533.4763991699219,246.54284702148436C541.2669991699219,244.64694702148438,544.3678991699219,239.47184702148436,544.7266991699219,239.25404702148438C547.1227991699219,237.72974702148437,547.4687991699219,238.66484702148438,550.3646991699219,237.8834470214844C551.6203991699218,237.55034702148438,554.1574991699218,236.10284702148437,552.9401991699219,235.66736702148438L552.9400991699218,235.64176702148438Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M513.0771481816406,230.81250068378907C513.0002701816406,230.81250068378907,512.2186630816407,230.83812588378908,512.2827151516407,231.31207788378907C512.5133491816406,233.09262088378907,513.1412001816407,235.28309088378907,513.5000211816406,236.24382088378906C514.7428911816406,239.57435088378907,515.2811211816406,240.67598088378907,517.0493911816407,243.60943088378906C517.8950511816406,245.01843088378905,519.7274611816406,245.97923088378906,520.0861911816406,245.60773088378906C520.4450111816407,245.23623088378906,519.4455211816406,244.94163088378906,518.8433211816406,242.93053088378906C518.5358111816406,241.89293088378906,518.4845011816407,241.47013088378907,519.2917611816406,240.44540088378906C520.1118511816406,239.40782088378907,520.5346411816406,236.62811088378908,518.4332811816406,235.68019088378907C517.8054211816407,235.39837088378906,516.6009511816407,235.18061088378906,516.2165611816406,234.62978088378907C514.2817011816406,231.85008088378908,521.6623111816406,233.04137088378906,522.0723611816406,234.14301088378906C522.9821611816407,236.61527088378907,523.4946611816406,240.06108088378906,523.1102611816407,242.11063088378907C522.7514611816406,244.05773088378908,522.3029611816406,245.00563088378905,522.6361611816407,245.04403088378905C523.0590611816406,245.09533088378907,524.8528611816406,244.90313088378906,524.6607611816406,243.44283088378907C524.1097611816406,239.52308088378905,525.6857611816406,241.06023088378907,525.3782611816406,237.75533088378907C524.8913611816406,232.54178088378907,524.4557611816406,232.66988088378906,523.9431611816407,231.60667588378905C523.7125611816406,231.13272388378905,515.4093811816406,230.51784388378906,513.0901311816406,230.79965898378907L513.0771481816406,230.81250068378907Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M511.3730064658203,224.42045352539063C516.0371524658203,224.21549352539063,526.6980524658203,223.42129352539064,536.3081524658203,226.0728935253906C542.2665524658203,227.71253352539063,546.5461524658203,228.87822352539064,553.4142524658203,232.52896352539062C554.0420524658203,232.86206352539062,555.5412524658203,232.32396352539064,554.8365524658203,231.86286352539062C548.8141524658204,227.93027352539062,544.8547524658203,225.07372352539062,537.5125524658204,223.12664352539062C529.3118524658203,220.94899452539062,520.7781024658203,221.80724152539062,512.9233924658203,222.05062952539063C512.2955424658203,222.06343252539062,510.69385146582033,224.47166352539062,511.3857544658203,224.43323352539062L511.3730064658203,224.42045352539063Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M569.2389077856445,440.3664329589844C569.3157877856445,440.3535329589844,569.3926677856446,440.3279329589844,569.4695477856445,440.31513295898435C600.6064177856446,433.32103295898435,615.1754177856445,429.0810329589844,627.8737177856445,423.6497329589844C634.7417177856445,420.7162329589844,653.6289177856445,411.3267329589844,665.1614177856445,396.97993295898436C670.9524177856446,389.76803295898435,676.3214177856445,381.68513295898435,679.6914177856445,371.97543295898436C684.0614177856445,359.3706429589844,682.4084177856446,362.0990929589844,683.6254177856446,355.0025329589844C683.0484177856446,356.1554029589844,679.5254177856445,363.63627295898436,676.4884177856445,370.3997329589844C669.2354177856446,384.52883295898437,660.3684177856445,394.87913295898437,653.8596177856446,401.30963295898437C646.7096177856445,408.3677329589844,631.1412177856446,418.92293295898435,618.3660177856445,423.0604329589844C609.4863177856446,425.9426329589844,587.3573177856446,433.00083295898435,568.5854877856445,437.3048329589844C565.5102077856445,438.0093329589844,562.5887577856445,438.64983295898435,559.7954177856445,439.22633295898436C562.9731477856445,439.6105329589844,566.1381377856445,439.99493295898435,569.2646377856445,440.3664329589844L569.2389077856445,440.3664329589844Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M822.0504259338379,156.478C813.1954259338379,132.62619999999998,808.8394259338379,123.5313,798.9474259338378,108.2109C793.3474259338379,99.5387,784.2374259338378,86.1654,775.1524259338379,78.4796C757.7394259338379,63.7612,735.5454259338379,51.0668,702.7174259338378,45.71237C680.8064259338379,42.13846,665.635425933838,39.71743,634.1524259338379,41.728552C602.6694259338378,43.73967,558.0270259338379,55.0763,532.5665259338379,61.7629C516.9084259338379,65.8748,505.7734159338379,72.2925,501.74994593383786,76.968C500.2636059338379,78.6973,496.7270559338379,83.8084,498.5722109338379,87.6769C501.74994593383786,94.37639999999999,506.60632593383787,92.8649,510.46322593383786,92.5318C524.9169259338379,91.2765,565.3179259338378,88.00999999999999,578.5287259338379,88.00999999999999C591.7395259338379,88.00999999999999,598.5434259338379,99.4362,598.5434259338379,111.8232C598.5434259338379,121.4433,594.1868259338379,167.763,600.8754259338378,184.582C604.5014259338379,193.703,615.6244259338379,196.252,622.8894259338379,204.015C645.8764259338379,228.571,648.5164259338379,242.431,649.1574259338379,257.149C649.6694259338378,268.844,658.7804259338379,263.477,668.3774259338379,262.914C674.0664259338379,262.581,673.7204259338379,248.464,677.9874259338379,242.431C685.6754259338379,231.543,694.529425933838,226.88,708.0994259338379,223.87C719.2724259338379,221.385,735.0844259338379,223.562,746.539425933838,232.196C757.4824259338379,240.445,756.8414259338379,255.548,757.4824259338379,266.56399999999996C757.9954259338379,275.365,753.279425933838,306.3,752.3054259338379,314.14C750.2304259338379,330.767,752.8444259338379,340.54,754.3044259338378,346.087C758.712425933838,362.791,769.5914259338379,366.608,773.4484259338378,366.634C774.6914259338379,361.036,780.8284259338379,343.858,784.9804259338379,334.622C799.0754259338379,303.251,811.1844259338379,274.596,818.2704259338379,255.138C824.1514259338379,238.972,839.1944259338379,202.644,822.0504259338379,156.478Z"
                            fill="#473C33" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M715.4414706542968,302.31626020507815C718.2731706542969,302.1497602050781,726.7302706542969,288.91726020507815,729.7029706542969,281.2058602050781C730.2154706542968,279.88646020507815,735.0205706542969,261.92726020507814,734.7898706542969,257.71286020507813C734.3414706542969,249.54030020507813,732.8549706542968,247.29860020507812,730.1385706542969,243.82718020507812C727.4348706542969,240.35574460507812,722.2325706542969,239.99707420507812,718.4269706542968,240.49665220507814C708.1119906542968,241.88010020507812,701.8589806542968,252.80676020507812,698.1686706542969,255.90676020507811C699.1296856542968,255.15096020507812,703.7425306542968,251.78196020507812,708.7269706542969,248.46429020507813C714.4674706542969,244.64700020507811,718.8112706542969,242.81520020507813,725.7690706542969,246.93993020507813C727.5373706542969,247.99033020507812,730.0872706542968,254.93316020507814,730.3562706542968,257.2133602050781C731.3429706542969,265.27056020507814,729.2927706542969,274.0836602050781,726.0252706542968,281.5901602050781C722.6937706542968,289.2375602050781,712.5838706542969,302.4955602050781,715.4156706542968,302.31626020507815L715.4414706542968,302.31626020507815Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M729.8566490966797,306.0951187011719C731.0738490966797,304.5195187011719,737.3269490966796,296.9490187011719,739.9665490966797,292.5937187011719C746.4886490966796,281.84631870117187,748.0903490966797,269.0238187011719,749.0001490966797,261.2996187011719C750.9222490966797,245.09531870117186,742.2217490966797,233.97649870117186,735.8406490966797,229.96707870117189C731.3174490966796,227.12332470117187,721.0409490966797,224.40767870117187,714.1217490966797,227.55884870117188C710.9953490966797,228.98072870117187,700.2959090966797,238.84421870117188,699.5656090966797,239.4847187011719C699.0915090966797,239.89461870117188,698.6942190966797,240.34291870117187,698.5404590966797,240.79131870117186C698.4763290966797,240.97061870117187,698.4508390966797,241.14991870117188,698.4763290966797,241.32931870117187C698.6172590966797,242.30281870117187,700.2446090966797,241.76481870117186,700.9365490966796,241.53421870117188C702.2820490966797,241.08591870117186,703.6915490966796,240.21481870117188,704.8702490966797,239.51031870117188C706.2669490966797,238.67771870117187,707.3048490966797,237.4223187011719,708.5349490966797,236.4103187011719C710.0724490966796,235.14218870117188,711.5332490966797,233.64345870117188,713.4552490966797,232.88768870117187C714.6725490966796,232.41373870117187,716.0949490966797,231.77324870117187,717.4017490966797,231.54266870117186C720.8613490966796,230.92780870117187,724.5133490966797,230.67161870117187,727.9857490966797,231.32490870117186C729.4593490966797,231.60671870117187,730.9073490966797,232.05506870117188,732.2143490966797,232.78520870117188C739.4924490966797,236.7818187011719,742.5676490966797,240.1380187011719,744.4384490966797,247.8238187011719C744.9382490966797,249.86051870117188,745.9249490966797,259.26281870117185,745.1048490966797,264.16901870117186C740.7610490966797,290.19821870117187,725.6026490966797,304.1224187011719,722.3992490966797,307.8756187011719C719.1830490966797,311.64171870117184,707.3561490966797,322.5299187011719,702.3332490966797,323.43941870117186C692.8511690966797,325.1687187011719,695.4266990966797,317.5469187011719,694.1966490966797,316.8937187011719C692.9794290966797,316.2532187011719,690.3141120966797,316.33001870117187,690.4295466466797,317.7903187011719C690.5448250966797,319.2634187011719,689.9554520966797,327.4109187011719,700.4496490966797,327.4109187011719C704.3706490966797,327.4109187011719,709.2653490966796,326.2319187011719,715.4799490966797,320.0192187011719C718.0169490966797,317.48291870117185,728.6394490966796,307.61941870117187,729.8438490966797,306.04381870117186L729.8566490966797,306.0951187011719Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M702.1409847949219,280.37324865722655C701.2056247949218,281.3851486572266,698.9504247949219,291.2870486572266,698.7581947949219,293.59284865722657C698.5659567949219,295.89854865722657,701.0262147949219,297.58944865722657,704.3320447949219,297.0130486572266C707.6378847949219,296.4365486572266,712.9812147949219,285.85574865722657,715.2877147949218,285.34334865722656C717.5942147949219,284.8181486572266,722.5658147949218,282.02564865722655,723.2448147949219,279.50214865722654C723.9240147949218,276.9786486572266,722.0531147949218,265.55234865722656,721.4894147949219,262.1450086572266C720.9640147949219,258.99380865722657,719.8364147949219,254.15174865722656,712.7633147949218,254.15174865722656C704.4473247949219,254.15174865722656,704.0372047949219,259.00661865722657,703.3965247949219,259.42933865722654C703.0505347949219,259.26280865722657,708.2401147949219,257.20044865722656,711.1488147949219,257.9946586572266C711.3667147949219,258.0587086572266,716.0180147949219,258.08433865722657,717.5812147949218,262.38837865722655C718.7601147949218,265.6292486572266,718.2090147949219,265.6804486572266,719.2085147949218,273.17414865722657Q719.3495147949219,277.5678486572266,718.9267147949218,278.43894865722655C717.8760147949218,280.6421486572266,715.6208147949219,282.81984865722654,713.6219147949218,282.9351486572266C711.6229147949218,283.0504486572266,706.8306247949218,289.3527486572266,706.3950147949218,290.53124865722657C705.9593947949219,291.70974865722656,703.6785447949219,291.58164865722654,703.6272447949219,290.62094865722656C703.5759347949219,289.67304865722656,704.1654647949218,284.17764865722654,706.4846347949219,281.6797486572266C708.8551147949219,279.1306486572266,711.4691147949219,274.7241486572266,710.9565147949219,272.46964865722657C709.2524147949218,265.0527486572266,697.9122967949219,265.6804486572266,697.7841927949219,266.44904865722657L697.6689147949219,267.21764865722656C701.9229447949219,272.3287486572266,706.4846347949219,271.66264865722655,703.0633647949219,279.3484486572266L702.1280047949218,280.36034865722655L702.1409847949219,280.37324865722655Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M486.3481214611816,311.62891494140626C484.3235614611816,312.42311494140625,485.32305146118165,314.88251494140627,485.45116146118164,315.70241494140623C485.50238146118164,316.08661494140625,485.9509014611816,317.4701149414062,488.50074146118163,315.62551494140627C491.84514146118164,313.2045149414063,497.31654146118166,307.52981494140624,493.11364146118166,298.42207494140627C490.40994146118163,292.56803494140627,482.1324114611816,289.42966194140627,479.96692146118164,289.27595174140623C477.78860746118164,289.10941994140626,477.76303346118164,290.31353494140626,480.3129114611816,290.96684494140624C483.7085314611816,291.83789494140626,489.32084146118166,294.54073494140624,490.98664146118165,299.62621494140626C492.1654414611816,303.22571494140624,490.2562414611816,307.4273149414063,488.94924146118166,309.18221494140624C487.64222146118163,310.92431494140624,486.86061146118163,311.43671494140625,486.34804146118165,311.64171494140624L486.3481214611816,311.62891494140626Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M509.59193472595217,352.7480021484375Q507.6827347259522,353.1067021484375,503.22363472595214,352.0179021484375C498.76453472595216,350.9290321484375,490.75603472595213,347.3807621484375,487.65513472595217,346.1894421484375C486.16883472595214,345.6258121484375,480.91523472595213,343.7299821484375,477.82713472595213,342.5514921484375C474.72633472595214,341.3730061484375,469.90840472595215,340.5531861484375,466.8075547259522,341.2833291484375C464.42425472595215,341.8469591484375,462.34846472595217,343.1023021484375,461.34897272595214,344.7419421484375C460.34947972595216,346.3815821484375,461.64365972595215,352.2228021484375,466.53844472595216,355.1178021484375C471.45883472595216,358.0256021484375,477.82713472595213,354.5926021484375,478.55753472595217,353.7472021484375C480.46673472595216,351.5695021484375,478.82663472595215,354.6567021484375,470.72849472595215,352.9273021484375C468.05042472595215,352.3509021484375,467.43540472595214,349.6864921484375,470.17752472595214,348.5848521484375C472.90683472595214,347.4960221484375,475.54643472595217,346.5865421484375,480.91523472595213,348.5848521484375C489.98723472595213,351.9666021484375,497.85473472595214,353.0170021484375,500.31503472595216,353.1067021484375Q502.77513472595217,353.1964021484375,509.6048347259522,352.7480021484375L509.59193472595217,352.7480021484375Z"
                            fill="#C69E83" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M470.29279720947267,339.06726868859374C464.29606720947265,338.9263615585937,461.27209520947264,340.86061755859373,461.0926872094727,342.6924175585938C460.42635720947266,349.5967975585937,461.72053720947264,345.15187755859375,463.55286720947265,343.9605875585938C466.32055720947267,342.18003755859377,470.21591720947265,341.30896755859374,475.11067720947267,342.33374755859376C478.4165772094727,343.02545755859376,480.6717772094727,343.62751755859375,481.65837720947263,344.54981755859376C482.22217720947265,345.07501755859374,482.4912772094726,344.6522975585938,481.5302772094727,343.5762875585938C479.37767720947267,341.1680575585938,475.0850772094727,339.19537355859376,470.3056272094727,339.08009085859373L470.29279720947267,339.06726868859374Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M513.9484910644532,348.43117638671873C513.1027910644531,348.7770463867187,514.3712910644531,350.72410638671874,508.36179106445314,350.46791638671874C507.15729106445315,350.41668638671877,501.6089910644531,350.59601638671876,492.9214910644531,346.42004638671875C492.5754910644531,346.25351638671873,488.46239106445313,344.56264638671877,486.11749106445313,343.30729838671874C485.0539910644531,342.74366838671875,484.51579106445314,343.05110788671874,484.6823910644531,343.78125038671874C484.7463910644531,344.07586638671876,484.8104910644531,344.3705063867188,484.8360910644531,344.61387638671874C484.9001910644531,345.1006463867187,485.0923910644531,345.5745963867187,485.6945910644531,345.9204663867188C487.2065910644531,346.76591638671874,489.1798910644531,347.5729063867187,489.7436910644531,347.85472638671877C495.27909106445316,350.49351638671874,498.4055910644531,351.60795638671874,503.0697910644531,352.31249638671875C505.1198910644531,352.61993638671873,506.8369910644531,352.83769638671873,508.31059106445315,352.97860638671875C507.3623910644531,353.42699638671877,506.3756910644531,353.78559638671874,505.47869106445313,353.91369638671875C502.42909106445313,354.3363963867188,500.3276910644531,354.6053963867187,496.4963910644531,353.79839638671876C490.6277910644531,352.56868638671875,484.57989106445314,351.4670463867187,477.6349310644531,353.3372963867188Q473.4193010644531,354.47729638671876,469.5495910644531,355.88639638671873Q472.4454610644531,357.12899638671877,480.4538910644531,355.2715963867187C490.0255910644531,353.05549638671874,500.46869106445314,357.6669963867188,507.0419910644531,356.2706963867187C507.1573910644531,356.2450963867187,507.2726910644531,356.20669638671876,507.3879910644531,356.18099638671873C507.00359106445313,357.11609638671877,505.8247910644531,357.6028963867187,504.72279106445313,359.19129638671876C502.31379106445314,362.66279638671875,500.95559106445313,365.87799638671873,501.1605910644531,366.6977963867188C501.31429106445313,367.31269638671876,502.24969106445315,366.87709638671873,502.50599106445316,366.30069638671876C504.4023910644531,362.03509638671875,511.27049106445315,355.51489638671876,513.2820910644531,354.6438963867187C517.4721910644531,352.81209638671874,519.3556910644531,354.84879638671873,516.9339910644532,350.25015638671874C516.3060910644531,349.05885638671873,514.6274910644531,348.1493663867187,513.9355910644531,348.43117638671873L513.9484910644532,348.43117638671873Z"
                            fill="#B98765" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g @click="clickBody('郂部')">
                    <path
                        d="M353.5,445.4999640625Q379,446.5000640625,386,440.0000640625C393,433.5000640625,400.5,421.5000640625,400.5,412.0000640625C400,404.0000640625,395.5,400.0000640625,393.5,395.0000340625C393.0851,395.0000340625,392.3672,394.8209940625,391.42629999999997,394.5863540625C386.698,393.4071462625,376.3383,390.8235040625,370.5,402.5000340625L353.5,445.4999640625Z"
                        fill="#F25555" :fill-opacity="curr == '郂部' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g @click="clickBody('颧部')" transform="matrix(-1,0,0,1,648,0)">
                    <path
                        d="M324,264.8027L330.97436,301.8084L379.0974,319.03499999999997Q377.1196,302.29859999999996,384.1969,291.4926Q397.0847,271.8146,440,271.8027L416.2872,208L361,221Q361,261.8338,324,264.8027Z"
                        fill="#F25555" :fill-opacity="curr == '颧部' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g @click="clickBody('眶下部')" transform="matrix(-1,0,0,1,758,0)">
                    <path d="M379,323L435,303L429.5,267Q417,271.5,403.5,266Q390,260.5,384,256L379,323Z" fill="#F25555"
                        :fill-opacity="curr == '眶下部' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g @click="clickBody('颌部')">
                    <path
                        d="M208.00006616210936,278.5000152375Q185.99997616210936,346.4999984375,190.99999816210936,357.9999984375C196.00001616210938,369.4999984375,213.50006616210936,408.9998984375,249.4999661621094,419.9998984375C254.50006616210936,395.9998984375,266.50006616210936,330.4999984375,266.50006616210936,321.4999984375C265.92826616210937,320.1114984375,265.6500661621094,317.9756984375,265.3122661621094,315.3825984375C263.5823661621094,302.1020984375,260.2899661621094,276.8267184375,208.00006616210936,278.5000152375Z"
                        fill="#F25555" :fill-opacity="curr == '颌部' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g @click="clickBody('颊部')">
                    <path d="M254,422C265,431,308,444,330,444Q361.5,374,368,323L318.5,307.5L273,323L254,422Z"
                        fill="#F25555" :fill-opacity="curr == '颊部' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g>
                    <g>
                        <ellipse cx="387" cy="426.9998073577881" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g>
                        <g>
                            <rect x="418" y="406" width="63" height="29" rx="6" fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M444.52,419.07L444.52,421.98199999999997C444.52,423.396,444.17,425.272,441.356,426.336C441.608,426.546,441.93,426.938,442.084,427.176C445.192,425.902,445.64,423.802,445.64,421.996L445.64,419.07L444.52,419.07ZM445.234,425.104C446.13,425.678,447.194,426.51800000000003,447.684,427.106L448.48199999999997,426.28C447.964,425.692,446.858,424.894,445.99,424.362L445.234,425.104ZM442.336,417.152L442.336,424.026L443.512,424.026L443.512,418.16L446.704,418.16L446.704,424.026L447.936,424.026L447.936,417.152L445.304,417.152C445.486,416.788,445.682,416.382,445.864,415.962L448.37,415.962L448.37,414.772L441.986,414.772L441.986,415.962L444.562,415.962C444.436,416.354,444.296,416.788,444.156,417.152L442.336,417.152ZM440.096,417.838C439.886,418.328,439.634,418.804,439.354,419.266L437.52,419.364C438.08,418.692,438.64,417.88,439.088,417.082L441.664,417.082L441.664,415.892L439.62,415.892C439.508,415.402,439.228,414.702,438.92,414.184L437.716,414.38C437.94,414.842,438.164,415.43,438.29,415.892L435.63,415.892L435.63,417.082L437.674,417.082C437.226,417.922,436.722,418.664,436.526,418.902C436.274,419.238,436.036,419.448,435.812,419.504C435.952,419.826,436.148,420.428,436.218,420.68C436.456,420.582,436.82,420.512,438.584,420.358C437.744,421.422,436.736,422.318,435.644,422.962C435.882,423.2,436.274,423.73199999999997,436.428,423.984C438.416,422.668,440.194,420.582,441.258,418.174L440.096,417.838ZM440.656,420.75C439.606,423.046,437.66,424.936,435.406,426.028C435.644,426.28,436.036,426.812,436.19,427.078C437.464,426.364,438.654,425.426,439.676,424.306C440.194,424.838,440.74,425.412,441.034,425.818L441.902,424.936C441.594,424.544,440.978,423.942,440.418,423.41C440.964,422.71,441.44,421.94,441.832,421.114L440.656,420.75ZM457.666,414.898L457.666,427.134L458.842,427.134L458.842,416.088L460.802,416.088C460.438,417.166,459.934,418.65,459.472,419.756C460.648,420.96,460.97,421.996,460.97,422.822C460.984,423.298,460.886,423.704,460.634,423.858C460.48,423.942,460.284,423.984,460.088,423.998C459.836,424.012,459.486,424.012,459.122,423.97C459.332,424.334,459.444,424.866,459.45799999999997,425.216C459.864,425.23,460.284,425.23,460.606,425.188C460.956,425.146,461.264,425.048,461.516,424.88C461.992,424.544,462.188,423.858,462.188,422.962C462.188,422.01,461.936,420.904,460.73199999999997,419.602C461.29200000000003,418.342,461.922,416.732,462.398,415.416L461.488,414.842L461.29200000000003,414.898L457.666,414.898ZM452.318,414.436C452.5,414.842,452.696,415.346,452.836,415.78L450.05,415.78L450.05,416.984L454.852,416.984C454.642,417.754,454.264,418.818,453.914,419.56L451.856,419.56L452.864,419.28C452.724,418.65,452.374,417.726,451.98199999999997,417.012L450.848,417.306C451.184,418.02,451.534,418.93,451.646,419.56L449.658,419.56L449.658,420.764L457.036,420.764L457.036,419.56L455.188,419.56C455.51,418.888,455.86,418.034,456.168,417.278L454.908,416.984L456.728,416.984L456.728,415.78L454.236,415.78C454.068,415.29,453.774,414.632,453.522,414.1L452.318,414.436ZM450.4,421.926L450.4,427.12L451.646,427.12L451.646,426.462L455.132,426.462L455.132,427.022L456.448,427.022L456.448,421.926L450.4,421.926ZM451.646,425.3L451.646,423.116L455.132,423.116L455.132,425.3L451.646,425.3Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-119.62757734397758,153.42561923915525)">
                        <path
                            d="M436.20307041091917,435.75676350097655L388.7433469109192,427.01995050097656L388.9243939109192,426.03647650097656L436.3840704109192,434.77329350097654L436.20307041091917,435.75676350097655Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
                <g>
                    <g>
                        <ellipse cx="357" cy="285.9998092651367" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g>
                        <g>
                            <rect x="426" y="250" width="63" height="29" rx="6" fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M450.258,258.996L442.754,258.996L442.754,270.532L450.468,270.532L450.468,269.328L444.014,269.328L444.014,260.2L450.258,260.2L450.258,258.996ZM440.878,263.014L440.878,264.764L439.184,264.764L439.184,263.014L440.878,263.014ZM440.878,261.88L439.184,261.88L439.184,260.172L440.878,260.172L440.878,261.88ZM440.878,265.898L440.878,267.704L439.184,267.704L439.184,265.898L440.878,265.898ZM437.966,258.996L437.966,270.07L439.184,270.07L439.184,268.88L442.04,268.88L442.04,258.996L437.966,258.996ZM444.504,267.046L444.504,268.18L449.964,268.18L449.964,267.046L447.766,267.046L447.766,265.156L449.502,265.156L449.502,264.05L447.766,264.05L447.766,262.356L449.712,262.356L449.712,261.222L444.616,261.222L444.616,262.356L446.548,262.356L446.548,264.05L444.826,264.05L444.826,265.156L446.548,265.156L446.548,267.046L444.504,267.046ZM451.756,259.206L451.756,260.55L457.006,260.55L457.006,271.148L458.42,271.148L458.42,264.05C459.946,264.89,461.71,265.996,462.62,266.766L463.572,265.548C462.48,264.694,460.26800000000003,263.448,458.658,262.664L458.42,262.944L458.42,260.55L464.258,260.55L464.258,259.206L451.756,259.206ZM473.666,258.898L473.666,271.134L474.842,271.134L474.842,260.088L476.802,260.088C476.438,261.166,475.93399999999997,262.65,475.472,263.756C476.648,264.96,476.97,265.996,476.97,266.822C476.984,267.298,476.886,267.704,476.634,267.858C476.48,267.942,476.284,267.984,476.088,267.998C475.836,268.012,475.486,268.012,475.122,267.97C475.332,268.334,475.444,268.866,475.45799999999997,269.216C475.864,269.23,476.284,269.23,476.606,269.188C476.956,269.146,477.264,269.048,477.516,268.88C477.992,268.544,478.188,267.858,478.188,266.962C478.188,266.01,477.936,264.904,476.73199999999997,263.602C477.29200000000003,262.342,477.922,260.732,478.398,259.416L477.488,258.842L477.29200000000003,258.898L473.666,258.898ZM468.318,258.436C468.5,258.842,468.696,259.346,468.836,259.78L466.05,259.78L466.05,260.984L470.852,260.984C470.642,261.754,470.264,262.818,469.914,263.56L467.856,263.56L468.864,263.28C468.724,262.65,468.374,261.726,467.98199999999997,261.012L466.848,261.306C467.184,262.02,467.534,262.93,467.646,263.56L465.658,263.56L465.658,264.764L473.036,264.764L473.036,263.56L471.188,263.56C471.51,262.888,471.86,262.034,472.168,261.278L470.908,260.984L472.728,260.984L472.728,259.78L470.236,259.78C470.068,259.29,469.774,258.632,469.522,258.1L468.318,258.436ZM466.4,265.926L466.4,271.12L467.646,271.12L467.646,270.462L471.132,270.462L471.132,271.022L472.448,271.022L472.448,265.926L466.4,265.926ZM467.646,269.3L467.646,267.116L471.132,267.116L471.132,269.3L467.646,269.3Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-74.48336345890868,135.4473472779431)">
                        <path
                            d="M431.8075704109192,289.9786535009766L358.8068474109192,286.0274825009766L358.8608934109192,285.02894450097654L431.8616704109192,288.9801135009766L431.8075704109192,289.9786535009766Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
                <g transform="matrix(-1,0,0,1,520,0)">
                    <g>
                        <ellipse cx="263" cy="235" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g transform="matrix(-1,0,0,1,832,0)">
                        <g>
                            <rect x="416" y="189" width="63" height="29" rx="6" fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M434.862,200.712L435.926,200.712L435.926,201.832L434.862,201.832L434.862,200.712ZM438.152,200.712L439.23,200.712L439.23,201.832L438.152,201.832L438.152,200.712ZM437.298,199.998L437.298,202.56L440.098,202.56L440.098,199.998L437.298,199.998ZM443.612,208.062C444.41,208.636,445.404,209.476,445.852,210.022L446.58,209.154C446.104,208.622,445.082,207.838,444.298,207.306L443.612,208.062ZM437.256,206.102L437.256,206.858L435.604,206.858L435.604,206.102L437.256,206.102ZM442.856,202.042L442.856,204.968C442.856,206.35399999999998,442.576,208.23,440.126,209.308L440.126,208.608L438.376,208.608L438.376,207.726L439.972,207.726L439.972,206.858L438.376,206.858L438.376,206.102L439.944,206.102L439.944,205.234L438.376,205.234L438.376,204.478L440.182,204.478L440.182,203.554L438.446,203.554C438.32,203.246,438.11,202.882,437.886,202.602L437.004,202.952C437.13,203.13400000000001,437.242,203.344,437.34,203.554L435.828,203.554L436.136,202.84L435.184,202.546C434.778,203.652,434.106,204.758,433.392,205.5C433.588,205.724,433.896,206.2,434.008,206.41C434.162,206.242,434.33,206.046,434.484,205.836L434.484,210.176L435.604,210.176L435.604,209.532L440.126,209.532L440.126,209.49C440.35,209.686,440.588,209.98,440.7,210.162C443.528,208.874,443.92,206.746,443.92,204.968L443.92,202.042L442.856,202.042ZM437.256,205.234L435.604,205.234L435.604,204.478L437.256,204.478L437.256,205.234ZM437.256,207.726L437.256,208.608L435.604,208.608L435.604,207.726L437.256,207.726ZM437.76,197.184L437.76,198.08L436.29,198.08L436.29,197.184L435.142,197.184L435.142,198.08L433.63,198.08L433.63,199.046L435.142,199.046L435.142,199.69L436.29,199.69L436.29,199.046L437.76,199.046L437.76,199.69L438.894,199.69L438.894,199.046L440.112,199.046L440.112,198.08L438.894,198.08L438.894,197.184L437.76,197.184ZM434.022,199.998L434.022,202.546L436.808,202.546L436.808,199.998L434.022,199.998ZM440.77,200.124L440.77,206.998L441.918,206.998L441.918,201.132L444.844,201.132L444.844,206.998L446.034,206.998L446.034,200.124L443.514,200.124L444.074,198.92L446.37,198.92L446.37,197.814L440.364,197.814L440.364,198.92L442.814,198.92C442.688,199.312,442.548,199.746,442.408,200.124L440.77,200.124ZM455.666,197.898L455.666,210.13400000000001L456.842,210.13400000000001L456.842,199.088L458.802,199.088C458.438,200.166,457.934,201.65,457.472,202.756C458.648,203.96,458.97,204.996,458.97,205.822C458.984,206.298,458.886,206.704,458.634,206.858C458.48,206.942,458.284,206.984,458.088,206.998C457.836,207.012,457.486,207.012,457.122,206.97C457.332,207.334,457.444,207.86599999999999,457.45799999999997,208.216C457.864,208.23,458.284,208.23,458.606,208.188C458.956,208.14600000000002,459.264,208.048,459.516,207.88C459.992,207.544,460.188,206.858,460.188,205.962C460.188,205.01,459.936,203.904,458.73199999999997,202.602C459.29200000000003,201.342,459.922,199.732,460.398,198.416L459.488,197.842L459.29200000000003,197.898L455.666,197.898ZM450.318,197.436C450.5,197.842,450.696,198.346,450.836,198.78L448.05,198.78L448.05,199.984L452.852,199.984C452.642,200.754,452.264,201.818,451.914,202.56L449.856,202.56L450.864,202.28C450.724,201.65,450.374,200.726,449.98199999999997,200.012L448.848,200.306C449.184,201.02,449.534,201.93,449.646,202.56L447.658,202.56L447.658,203.764L455.036,203.764L455.036,202.56L453.188,202.56C453.51,201.888,453.86,201.034,454.168,200.278L452.908,199.984L454.728,199.984L454.728,198.78L452.236,198.78C452.068,198.29,451.774,197.632,451.522,197.1L450.318,197.436ZM448.4,204.926L448.4,210.12L449.646,210.12L449.646,209.462L453.132,209.462L453.132,210.022L454.448,210.022L454.448,204.926L448.4,204.926ZM449.646,208.3L449.646,206.11599999999999L453.132,206.11599999999999L453.132,208.3L449.646,208.3Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-63.025778979063034,101.39955475926399)">
                        <line x1="265" y1="234.5" x2="358.30059814453125" y2="234.5" fill-opacity="0" stroke-opacity="1"
                            stroke="#557AF2" fill="none" stroke-width="1" />
                    </g>
                </g>
                <g transform="matrix(-1,0,0,1,472,0)">
                    <g>
                        <ellipse cx="239" cy="330.2069396972656" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g transform="matrix(-1,0,0,1,728,0)">
                        <g>
                            <rect x="364" y="315.20703125" width="63" height="29" rx="6" fill="#557AF2"
                                fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M390.338,328.41703125L390.338,331.14703125C390.338,332.54703125,390.016,334.43703125,386.992,335.54303125C387.272,335.78103125,387.636,336.20103125,387.818,336.45303125C391.122,335.12303125,391.584,332.96703125,391.584,331.16103125L391.584,328.41703125L390.338,328.41703125ZM391.192,334.07303125C392.06,334.75903125,393.152,335.75303125,393.67,336.39703125L394.552,335.50103125C394.02,334.87103125,392.9,333.93303125,392.032,333.30303125L391.192,334.07303125ZM384.43,323.32103125C383.786,324.88903125,382.652,326.38703125,381.476,327.35303125C381.686,327.66103125,382.05,328.37503125,382.148,328.66903125C382.344,328.50103125,382.526,328.31903125,382.722,328.12303125L382.722,329.01903125L386.754,329.01903125L386.754,327.84303125L382.988,327.84303125C383.66,327.12903125,384.276,326.27503125,384.808,325.36503125C385.55,326.09303125,386.292,326.98903125,386.684,327.61903125L387.51,326.54103125C387.076,325.89703125,386.236,324.97303125,385.396,324.24503125L385.634,323.72703125L384.43,323.32103125ZM387.958,326.37303125L387.958,333.12103125L389.218,333.12103125L389.218,327.60503125L392.73199999999997,327.60503125L392.73199999999997,333.07903125L394.062,333.07903125L394.062,326.37303125L391.318,326.37303125L391.724,325.19703125L394.412,325.19703125L394.412,323.97903125L387.468,323.97903125L387.468,325.19703125L390.296,325.19703125C390.226,325.58903125,390.128,325.99503125,390.044,326.37303125L387.958,326.37303125ZM382.302,330.16703125L382.302,335.97703125L383.548,335.97703125L383.548,335.16503125L387.048,335.16503125L387.048,330.16703125L382.302,330.16703125ZM383.548,331.24503125L385.83,331.24503125L385.83,334.10103125L383.548,334.10103125L383.548,331.24503125ZM403.666,324.10503125L403.666,336.34103125L404.842,336.34103125L404.842,325.29503125L406.802,325.29503125C406.438,326.37303125,405.934,327.85703125,405.472,328.96303125C406.648,330.16703125,406.97,331.20303125,406.97,332.02903125C406.984,332.50503125,406.886,332.91103125,406.634,333.06503125C406.48,333.14903125,406.284,333.19103125,406.088,333.20503125C405.836,333.21903125,405.486,333.21903125,405.122,333.17703125C405.332,333.54103125,405.444,334.07303125,405.45799999999997,334.42303125C405.864,334.43703125,406.284,334.43703125,406.606,334.39503125C406.956,334.35303125,407.264,334.25503125,407.516,334.08703125C407.992,333.75103125,408.188,333.06503125,408.188,332.16903125C408.188,331.21703125,407.936,330.11103125,406.73199999999997,328.80903125C407.29200000000003,327.54903125,407.922,325.93903125,408.398,324.62303125L407.488,324.04903125L407.29200000000003,324.10503125L403.666,324.10503125ZM398.318,323.64303125C398.5,324.04903125,398.696,324.55303125,398.836,324.98703125L396.05,324.98703125L396.05,326.19103125L400.852,326.19103125C400.642,326.96103125,400.264,328.02503125,399.914,328.76703125L397.856,328.76703125L398.864,328.48703125C398.724,327.85703125,398.374,326.93303125,397.98199999999997,326.21903125L396.848,326.51303125C397.184,327.22703125,397.534,328.13703125,397.646,328.76703125L395.658,328.76703125L395.658,329.97103125L403.036,329.97103125L403.036,328.76703125L401.188,328.76703125C401.51,328.09503125,401.86,327.24103125,402.168,326.48503125L400.908,326.19103125L402.728,326.19103125L402.728,324.98703125L400.236,324.98703125C400.068,324.49703125,399.774,323.83903125,399.522,323.30703125L398.318,323.64303125ZM396.4,331.13303125L396.4,336.32703125L397.646,336.32703125L397.646,335.66903125L401.132,335.66903125L401.132,336.22903125L402.448,336.22903125L402.448,331.13303125L396.4,331.13303125ZM397.646,334.50703125L397.646,332.32303125L401.132,332.32303125L401.132,334.50703125L397.646,334.50703125Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-95.87541276697209,98.75223472577568)">
                        <path
                            d="M303.0748704109192,352.1918458404541L240.6677374109192,330.2069388404541L241.0000034109192,329.2637528404541L303.4070704109192,351.2486458404541L303.0748704109192,352.1918458404541Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
                <g transform="matrix(-1,0,0,-1,622,892)">
                    <g>
                        <ellipse cx="314" cy="492" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g transform="matrix(-1,0,0,-1,934,950)">
                        <g>
                            <rect x="467" y="475" width="63" height="29" rx="6" fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M489.348,486.782C489.208,487.496,488.928,488.51800000000003,488.69,489.176L489.558,489.456C489.838,488.854,490.16,487.902,490.454,487.09L489.348,486.782ZM484.924,486.992C485.204,487.748,485.414,488.756,485.456,489.4L486.492,489.092C486.436,488.434,486.198,487.454,485.89,486.712L484.924,486.992ZM491.28,486.376L491.28,492.844L492.498,492.844L492.498,487.384L495.676,487.384L495.676,492.802L496.95,492.802L496.95,486.376L494.248,486.376L494.752,485.088L497.384,485.088L497.384,483.884L490.804,483.884L490.804,485.088L493.478,485.088C493.352,485.522,493.198,485.984,493.044,486.376L491.28,486.376ZM493.562,488.056C493.534,492.76,493.436,494.328,490.174,495.224C490.384,495.42,490.678,495.868,490.79,496.134C492.554,495.63,493.506,494.902,494.038,493.698C494.962,494.426,496.068,495.378,496.614,496.008L497.426,495.168C496.81,494.538,495.55,493.53,494.612,492.83L494.22,493.194C494.598,491.976,494.654,490.324,494.668,488.056L493.562,488.056ZM487.024,483.198L487.024,485.088L484.784,485.088L484.784,486.306L487.024,486.306L487.024,488.098C487.024,488.63,487.01,489.176,486.968,489.708L484.588,489.708L484.588,490.926L486.814,490.926C486.534,492.48,485.89,493.95,484.406,495.154C484.686,495.364,485.106,495.826,485.288,496.12C486.576,495.07,487.318,493.838,487.738,492.508C488.48,493.348,489.236,494.3,489.628,494.916L490.608,494.132C490.048,493.362,488.928,492.13,488.046,491.206L488.088,490.926L490.804,490.926L490.804,489.708L488.214,489.708C488.242,489.176,488.256,488.644,488.256,488.098L488.256,486.306L490.496,486.306L490.496,485.088L488.256,485.088L488.256,483.198L487.024,483.198ZM506.666,483.898L506.666,496.134L507.842,496.134L507.842,485.088L509.802,485.088C509.438,486.166,508.934,487.65,508.472,488.756C509.648,489.96,509.97,490.996,509.97,491.822C509.984,492.298,509.886,492.704,509.634,492.858C509.48,492.942,509.284,492.984,509.088,492.998C508.836,493.012,508.486,493.012,508.122,492.97C508.332,493.334,508.444,493.866,508.45799999999997,494.216C508.864,494.23,509.284,494.23,509.606,494.188C509.956,494.146,510.264,494.048,510.516,493.88C510.992,493.544,511.188,492.858,511.188,491.962C511.188,491.01,510.936,489.904,509.73199999999997,488.602C510.29200000000003,487.342,510.922,485.732,511.398,484.416L510.488,483.842L510.29200000000003,483.898L506.666,483.898ZM501.318,483.436C501.5,483.842,501.696,484.346,501.836,484.78L499.05,484.78L499.05,485.984L503.852,485.984C503.642,486.754,503.264,487.818,502.914,488.56L500.856,488.56L501.864,488.28C501.724,487.65,501.374,486.726,500.98199999999997,486.012L499.848,486.306C500.184,487.02,500.534,487.93,500.646,488.56L498.658,488.56L498.658,489.764L506.036,489.764L506.036,488.56L504.188,488.56C504.51,487.888,504.86,487.034,505.168,486.278L503.908,485.984L505.728,485.984L505.728,484.78L503.236,484.78C503.068,484.29,502.774,483.632,502.522,483.1L501.318,483.436ZM499.4,490.926L499.4,496.12L500.646,496.12L500.646,495.462L504.132,495.462L504.132,496.022L505.448,496.022L505.448,490.926L499.4,490.926ZM500.646,494.3L500.646,492.116L504.132,492.116L504.132,494.3L500.646,494.3Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-145.51902997493744,132.94546449184418)">
                        <line x1="316" y1="491.5" x2="409.30059814453125" y2="491.5" fill-opacity="0" stroke-opacity="1"
                            stroke="#557AF2" fill="none" stroke-width="1" />
                    </g>
                </g>
            </g>
        </svg>
    </view>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"
const props = defineProps({
    width: {
        type: String,
        default: ''
    },
    height: {
        type: String,
        default: ''
    },
})
const emit = defineEmits(['selectBody'])
const curr = ref('')
const clickBody = (body) => {
    curr.value = body
    emit('selectBody', body)
}
onMounted(async () => { })
</script>
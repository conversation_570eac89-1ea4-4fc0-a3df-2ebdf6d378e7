<template>
    <view>
        <svg :style="{ width: width, height: height }" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="500" height="500"
            viewBox="0 0 500 500">
            <defs>
                <clipPath id="master_svg0_15_266">
                    <rect x="0" y="0" width="500" height="500" rx="0" />
                </clipPath>
                <linearGradient x1="0.5" y1="1" x2="0.5" y2="0" id="master_svg1_15_1902">
                    <stop offset="30.238038301467896%" stop-color="#E6AA89" stop-opacity="1" />
                    <stop offset="100%" stop-color="#E6AA89" stop-opacity="0" />
                </linearGradient>
                <linearGradient x1="0.5" y1="1" x2="0.5" y2="0" id="master_svg2_15_1998">
                    <stop offset="59.523749351501465%" stop-color="#FDD2BB" stop-opacity="1" />
                    <stop offset="100%" stop-color="#FDD2BB" stop-opacity="0" />
                </linearGradient>
                <linearGradient x1="1" y1="0.5" x2="0" y2="0.5" id="master_svg3_15_2008">
                    <stop offset="0%" stop-color="#F6C5AB" stop-opacity="1" />
                    <stop offset="100%" stop-color="#F6C5AB" stop-opacity="0" />
                </linearGradient>
                <linearGradient x1="1" y1="0.5" x2="0" y2="0.5" id="master_svg4_15_2008">
                    <stop offset="0%" stop-color="#F6C5AB" stop-opacity="1" />
                    <stop offset="100%" stop-color="#F6C5AB" stop-opacity="0" />
                </linearGradient>
                <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="master_svg5_15_1613">
                    <stop offset="0%" stop-color="#ECB191" stop-opacity="1" />
                    <stop offset="100%" stop-color="#ECB191" stop-opacity="0" />
                </linearGradient>
                <linearGradient x1="0.8693336248397827" y1="0.8010901212692261" x2="0.03220596259786049"
                    y2="0.10714144959107119" id="master_svg6_15_1435">
                    <stop offset="0%" stop-color="#FFCEB3" stop-opacity="1" />
                    <stop offset="100%" stop-color="#FDD2BB" stop-opacity="1" />
                </linearGradient>
                <linearGradient x1="0.5" y1="1" x2="-0.02383046143995603" y2="0.18533121299551306"
                    id="master_svg7_15_1525">
                    <stop offset="0%" stop-color="#F2BC9E" stop-opacity="1" />
                    <stop offset="100%" stop-color="#F7C8AF" stop-opacity="1" />
                </linearGradient>
            </defs>
            <g clip-path="url(#master_svg0_15_266)">
                <g>
                    <path
                        d="M0,326.531L40.9591,380.35Q60.3126,422.366,92.7746,439.399Q149.279,469.046,245.5,423C317.5,460.5,400.5,487,501,319.192L501,91L394.606,91Q353.5,148.5,328.672,170.4545C318,172,306.194,170.4545,288.212,159.69060000000002C244.679,146.6505,211.251,159.96460000000002,198.863,164.8985C196.82,165.7122,195.349,166.2979,194.5,166.5C193.398,166.7621,192.111,167.26510000000002,190.637,167.841C184.086,170.4013,173.849,174.4016,159.84,165.0726Q142.681,153.6451,109.391,91L0,91L0,326.531Z"
                        fill="url(#master_svg1_15_1902)" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M0,318.531L40.9591,372.35Q94.9053,483.392,245.255,416.852C320.68,450.122,394.606,482.435,501,311.192L501,83L394.606,83Q353.5,140.5,328.672,162.4545C318,164,306.194,162.4545,288.212,151.69060000000002C244.679,138.6505,211.251,151.96460000000002,198.863,156.8985C196.82,157.7122,195.349,158.2979,194.5,158.5C193.398,158.7621,192.111,159.26510000000002,190.637,159.841C184.086,162.4013,173.849,166.4016,159.84,157.0726Q142.681,145.6451,109.391,83L0,83L0,318.531Z"
                        fill="url(#master_svg2_15_1998)" fill-opacity="1" />
                </g>
                <g>
                    <path d="M240.36975,375Q248.3789,417.4286,232,429Q242.3973,417.8948,240.36975,375Z" fill="#E6AA89"
                        fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,518,0)">
                    <path d="M267.36975,375Q275.3789,417.4286,259,429Q269.3973,417.8948,267.36975,375Z" fill="#E6AA89"
                        fill-opacity="1" />
                </g>
                <g
                    transform="matrix(0.9806230664253235,0.1959039568901062,-0.1959039568901062,0.9806230664253235,36.79686640200089,-32.022679301881)">
                    <path
                        d="M180.27587890625,170Q247.77587890625,268,199.77587890625,323Q252.77587890625,274.5,180.27587890625,170Z"
                        fill="#E6AA89" fill-opacity="1" />
                </g>
                <g
                    transform="matrix(0.9970341920852661,-0.07695973664522171,0.07695973664522171,0.9970341920852661,-19.179518687247764,16.231003847904503)">
                    <path
                        d="M201,256.9609375Q242.0641,330.9548375,212.863,372.4819375Q245.1059,335.86253750000003,201,256.9609375Z"
                        fill="#E6AA89" fill-opacity="1" />
                </g>
                <g
                    transform="matrix(-0.9970341920852661,-0.07695973664522171,-0.07695973664522171,0.9970341920852661,605.4946583131095,23.333959151099407)">
                    <path
                        d="M293.29443359375,256.9609375Q334.35853359375,330.9548375,305.15743359375,372.4819375Q337.40033359375,335.86253750000003,293.29443359375,256.9609375Z"
                        fill="#E6AA89" fill-opacity="1" />
                </g>
                <g
                    transform="matrix(0.9586133360862732,0.2847112715244293,-0.2847112715244293,0.9586133360862732,56.093412641610485,-44.94185049488442)">
                    <path
                        d="M182.630859375,170.470703125Q241.708559375,257.379603125,199.697759375,306.15470312499997Q246.084659375,263.144003125,182.630859375,170.470703125Z"
                        fill="#E6AA89" fill-opacity="1" />
                </g>
                <g
                    transform="matrix(-0.8720394968986511,0.4894355237483978,0.4894355237483978,0.8720394968986511,491.1258823253411,-128.4024496267939)">
                    <path
                        d="M323.52618408203125,234Q385.48738408203127,323.9585,341.42608408203125,374.445Q390.07708408203126,329.9251,323.52618408203125,234Z"
                        fill="#E6AA89" fill-opacity="1" />
                </g>
                <g
                    transform="matrix(-0.8236559629440308,0.5670899152755737,0.5670899152755737,0.8236559629440308,453.18346082141943,-140.92339958770754)">
                    <path
                        d="M321.60382080078125,235.07958984375Q375.83382080078127,314.85708984375003,337.27022080078126,359.63058984375Q379.85082080078126,320.14838984375,321.60382080078125,235.07958984375Z"
                        fill="#E6AA89" fill-opacity="1" />
                </g>
                <g>
                    <ellipse cx="244.5" cy="351" rx="3.5" ry="17" fill="#F2BC9E" fill-opacity="1" />
                </g>
                <g>
                    <ellipse cx="244.5" cy="351.5" rx="1.5" ry="9.5" fill="#D48F73" fill-opacity="1" />
                </g>
                <g>
                    <g>
                        <g>
                            <ellipse cx="248.5" cy="198" rx="49.5" ry="46" fill="url(#master_svg3_15_2008)"
                                fill-opacity="1" />
                        </g>
                        <g>
                            <ellipse cx="246" cy="203" rx="44" ry="46" fill="url(#master_svg4_15_2008)"
                                fill-opacity="1" />
                            <path
                                d="M246,249Q247.08010000000002,249,248.159,248.94459999999998Q249.2378,248.88920000000002,250.3128,248.7785Q251.3877,248.6678,252.4561,248.50209999999998Q253.5246,248.3364,254.584,248.11610000000002Q255.64339999999999,247.8958,256.6911,247.6214Q257.7389,247.34699999999998,258.7725,247.0192Q259.8062,246.6914,260.8231,246.311Q261.8401,245.9306,262.8381,245.4985Q263.836,245.0663,264.8124,244.58350000000002Q265.7889,244.10070000000002,266.7414,243.5684Q267.694,243.0361,268.6205,242.4555Q269.547,241.875,270.4451,241.2476Q271.3432,240.6202,272.2108,239.9475Q273.0783,239.2749,273.9133,238.55849999999998Q274.7483,237.84210000000002,275.54859999999996,237.0838Q276.3489,236.3254,277.1127,235.5269Q277.87649999999996,234.7284,278.6018,233.89170000000001Q279.3272,233.055,280.0125,232.1821Q280.6977,231.3092,281.3411,230.4022Q281.9846,229.49509999999998,282.5847,228.5562Q283.1847,227.6173,283.7401,226.64870000000002Q284.2953,225.68009999999998,284.8045,224.6842Q285.3137,223.6883,285.77549999999997,222.66750000000002Q286.2373,221.6467,286.65070000000003,220.6034Q287.06399999999996,219.5602,287.4279,218.49689999999998Q287.79179999999997,217.4337,288.10540000000003,216.35309999999998Q288.4189,215.2725,288.6814,214.1771Q288.9438,213.0817,289.1545,211.9742Q289.3653,210.8666,289.5238,209.7496Q289.68219999999997,208.6326,289.7881,207.5088Q289.894,206.385,289.947,205.2571Q290,204.1292,290,203Q290,201.8708,289.947,200.7429Q289.894,199.615,289.7881,198.4912Q289.68219999999997,197.3674,289.5238,196.2504Q289.3653,195.1334,289.1545,194.0258Q288.9438,192.9183,288.6814,191.8229Q288.4189,190.7275,288.10540000000003,189.64690000000002Q287.79179999999997,188.5663,287.4279,187.5031Q287.06399999999996,186.4398,286.65070000000003,185.3966Q286.2373,184.3533,285.77549999999997,183.3325Q285.3137,182.3116,284.8045,181.3157Q284.2953,180.3198,283.7401,179.3513Q283.1847,178.3827,282.5847,177.4438Q281.9846,176.5048,281.3411,175.5978Q280.6977,174.6908,280.0125,173.8179Q279.3272,172.945,278.6019,172.10829999999999Q277.87649999999996,171.2716,277.1127,170.4731Q276.3489,169.6746,275.54859999999996,168.9162Q274.7483,168.1579,273.9133,167.4415Q273.0783,166.72514,272.2108,166.05245Q271.3432,165.37977,270.4451,164.7524Q269.547,164.12503,268.6205,163.54448Q267.694,162.96394,266.7414,162.43162Q265.7888,161.8993,264.8124,161.41649Q263.836,160.93368,262.8381,160.50154Q261.8401,160.0694,260.8231,159.68897Q259.8061,159.30854,258.7725,158.98074Q257.7389,158.65294,256.6911,158.37856Q255.64339999999999,158.10418,254.584,157.883877Q253.5246,157.663574,252.4561,157.49788Q251.3877,157.332187,250.3128,157.221503Q249.2378,157.110818,248.159,157.055409Q247.08010000000002,157,246,157Q244.91989999999998,157,243.841,157.055409Q242.7622,157.110818,241.6872,157.221503Q240.6123,157.332187,239.5438,157.49788Q238.4754,157.663574,237.416,157.883877Q236.35660000000001,158.10418,235.3089,158.37856Q234.2611,158.65294,233.2275,158.98074Q232.1938,159.30854,231.17680000000001,159.68897Q230.1598,160.0694,229.1619,160.50154Q228.164,160.93368,227.1876,161.41649Q226.2111,161.8993,225.2585,162.43162Q224.3059,162.96394,223.3795,163.54448Q222.453,164.12503,221.5549,164.7524Q220.6568,165.37977,219.7892,166.05245Q218.9217,166.72514,218.0867,167.4415Q217.2517,168.1579,216.4514,168.9162Q215.65109999999999,169.6746,214.8873,170.4731Q214.1235,171.2716,213.3981,172.10829999999999Q212.6728,172.945,211.98754,173.8179Q211.30231,174.6908,210.65887,175.5978Q210.01543,176.5048,209.41534,177.4438Q208.81524,178.3827,208.25994,179.3513Q207.70464,180.3198,207.19546,181.3157Q206.68628999999999,182.3116,206.22447,183.3325Q205.76265,184.3533,205.3493,185.3966Q204.93595,186.4398,204.57206,187.5031Q204.20817,188.5663,203.89462,189.64690000000002Q203.58108,190.7275,203.31862,191.8229Q203.05617,192.9183,202.845447,194.0258Q202.634723,195.1334,202.476233,196.2504Q202.317744,197.3674,202.211872,198.4912Q202.106,199.615,202.0529999,200.7429Q202,201.8708,202,203Q202,204.1292,202.0529999,205.2571Q202.106,206.385,202.211872,207.5088Q202.317744,208.6326,202.476233,209.7496Q202.634723,210.8666,202.845447,211.9742Q203.05617,213.0817,203.31862,214.1771Q203.58108,215.2725,203.89462,216.35309999999998Q204.20817,217.4337,204.57206,218.49689999999998Q204.93595,219.5602,205.3493,220.6034Q205.76265,221.6467,206.22447,222.66750000000002Q206.68628999999999,223.6883,207.19546,224.6842Q207.70464,225.68009999999998,208.25994,226.64870000000002Q208.81524,227.6173,209.41534,228.5562Q210.01543,229.4952,210.65887,230.4022Q211.30231,231.3092,211.98754,232.1821Q212.6728,233.055,213.3981,233.89170000000001Q214.1235,234.7284,214.8873,235.5269Q215.65109999999999,236.3254,216.4514,237.0837Q217.2517,237.84210000000002,218.0867,238.55849999999998Q218.9217,239.2749,219.7892,239.9475Q220.6568,240.6202,221.5549,241.2476Q222.453,241.875,223.3795,242.4555Q224.3059,243.0361,225.2585,243.5684Q226.2111,244.10070000000002,227.1876,244.58350000000002Q228.164,245.0663,229.1619,245.4985Q230.1598,245.9306,231.17680000000001,246.311Q232.1938,246.6914,233.2275,247.0192Q234.2611,247.34699999999998,235.3089,247.6214Q236.35660000000001,247.8958,237.416,248.11610000000002Q238.4754,248.3364,239.5438,248.50209999999998Q240.6123,248.6678,241.6872,248.7785Q242.7622,248.88920000000002,243.841,248.94459999999998Q244.91989999999998,249,246,249ZM276.3901,171.1643Q289,184.3474,289,203Q289,221.6526,276.3901,234.8357Q263.7981,248,246,248Q228.202,248,215.6099,234.8357Q203,221.6526,203,203Q203,184.3474,215.6099,171.1643Q228.2019,158,246,158Q263.7981,158,276.3901,171.1643Z"
                                fill-rule="evenodd" fill="url(#master_svg5_15_1613)" fill-opacity="1" />
                        </g>
                        <g>
                            <ellipse cx="297.8526725769043" cy="234.90803146362305" rx="35.14731979370117"
                                ry="42.09196853637695" fill="#F5C5AA" fill-opacity="1"
                                style="mix-blend-mode:passthrough" />
                        </g>
                        <g transform="matrix(-1,0,0,1,669.472900390625,0)">
                            <path
                                d="M369.6323501953125,196.0549869C350.3565501953125,196.0549869,334.7364501953125,214.681,334.7364501953125,237.6735C334.7364501953125,241.7634,335.2349421953125,245.7429,336.1765701953125,249.5014C347.6977501953125,227.5591,372.6787501953125,213.2994,391.8438501953125,223.8008C408.5162501953125,232.6992,388.5203501953125,258.2342,372.9557501953125,263.76120000000003C365.0903501953125,265.972,355.5631501953125,266.801,347.0330501953125,269.3436C353.1260501953125,275.4785,360.9914501953125,279.23699999999997,369.5769501953125,279.23699999999997C388.8527501953125,279.23699999999997,404.4728501953125,260.6109,404.4728501953125,237.61849999999998C404.4728501953125,214.626,388.8527501953125,196,369.5769501953125,196L369.6323501953125,196.0549869Z"
                                fill="#ECB79A" fill-opacity="1" style="mix-blend-mode:passthrough" />
                        </g>
                        <g>
                            <path
                                d="M285.7464,218.5846C296.8485,215.063,309.2338,218.4169,320.3916,215.007C321.4517,214.6716,322.5674,214.2803,323.3485,213.4418C324.4642,212.268,324.7432,210.4792,324.6316,208.8023C324.3526,204.0508,321.7305,199.69072,318.1043,196.61634C314.478,193.54197,309.959,191.75316,305.3285,190.802798C291.7717,188.00785,276.9875,192.64739,267.3917,202.5974C258.85595,211.5413,254.72751,224.4539,256.345449,236.6959C256.791774,239.9939,258.57699,252.45940000000002,263.37488,247.8197C265.2717,245.9751,265.49491,242.5652,266.1086,240.1616C266.9454,236.7518,268.1728,233.5096,270.0697,230.5469C273.696,224.90109999999999,279.3865,220.5969,285.7464,218.5846Z"
                                fill="#FFABA6" fill-opacity="1" style="mix-blend-mode:passthrough" />
                        </g>
                        <g>
                            <path
                                d="M324.63164021606445,208.8023C324.35264021606446,204.0508,321.73054021606447,199.69072,318.10434021606443,196.61634C314.47804021606447,193.54197,309.95904021606447,191.75316,305.3285402160644,190.802798C291.7717402160645,188.00785,276.9875402160645,192.64739,267.3917602160644,202.5974C264.10021021606445,206.0631,261.42226021606444,210.0879,259.52544021606445,214.448C263.65389021606444,216.1249,267.2801602160645,219.4789,266.4991102160645,223.3919C264.43493021606446,231.5531,261.19914021606445,239.7144,259.91600721606443,248.1551C260.86442021606445,249.0495,261.98019021606444,249.1612,263.3749202160644,247.8197C265.2717402160645,245.9751,265.49495021606447,242.5652,266.10863021606445,240.1616C266.94544021606447,236.7518,268.1728102160645,233.5096,270.0697402160645,230.5469C273.69604021606443,224.90109999999999,279.3865402160645,220.5969,285.74654021606443,218.5844C296.84864021606444,215.0628,309.23394021606447,218.4168,320.3917402160645,215.0068C321.4517402160644,214.6714,322.56754021606446,214.2801,323.34864021606444,213.4417C324.46434021606444,212.2678,324.74334021606444,210.47899999999998,324.63164021606445,208.8023Z"
                                fill="#F8958F" fill-opacity="1" style="mix-blend-mode:passthrough" />
                        </g>
                    </g>
                    <g>
                        <g>
                            <ellipse cx="237.10411834716797" cy="234.381507396698" rx="34.895877838134766"
                                ry="41.61848831176758" fill="#FFCEB3" fill-opacity="1"
                                style="mix-blend-mode:passthrough" />
                        </g>
                        <g>
                            <path
                                d="M237.1041405090332,192.76317352643431C217.8283405090332,192.76317352643431,202.2082405090332,211.38918662643434,202.2082405090332,234.38168662643432C202.2082405090332,238.47158662643432,202.7067325090332,242.45108662643432,203.6483605090332,246.20958662643432C215.1695405090332,224.26728662643433,240.1505405090332,210.00758662643432,259.3156405090332,220.50898662643434C275.9880405090332,229.40738662643432,255.99214050903322,254.9423866264343,240.4275405090332,260.46938662643436C232.5621405090332,262.6801866264343,223.0349405090332,263.5091866264343,214.50484050903322,266.0517866264343C220.5978405090332,272.1866866264343,228.4632405090332,275.9451866264343,237.0487405090332,275.9451866264343C256.3245405090332,275.9451866264343,271.9446405090332,257.31908662643434,271.9446405090332,234.3266866264343C271.9446405090332,211.33418662643433,256.3245405090332,192.70818662643433,237.0487405090332,192.70818662643433L237.1041405090332,192.76317352643431Z"
                                fill="#F2BC9E" fill-opacity="1" style="mix-blend-mode:passthrough" />
                        </g>
                        <g>
                            <path
                                d="M223.5336,214.2631C234.5563,210.781,246.8529,214.0973,257.931,210.7257C258.9834,210.3941,260.0912,210.0072,260.8667,209.1781C261.97450000000003,208.01749999999998,262.2514,206.2488,262.1406,204.5908C261.8637,199.8928,259.26030000000003,195.58171,255.66,192.54192C252.0596,189.50212,247.573,187.73344,242.9756,186.793767C229.5158,184.03026,214.8374,188.61761,205.3102,198.4557C196.83552,207.299,192.73661,220.0664,194.342978,232.1706C194.78611,235.4315,196.55855,247.7568,201.32212,243.1693C203.20537,241.34550000000002,203.42698000000001,237.97390000000001,204.0363,235.5973C204.8671,232.2259,206.0857,229.0201,207.969,226.0908C211.5694,220.5085,217.2192,216.2527,223.5336,214.2631Z"
                                fill="#FFABA6" fill-opacity="1" style="mix-blend-mode:passthrough" />
                        </g>
                        <g>
                            <path
                                d="M262.1405568803787,204.59008556480407C261.8635568803787,199.89208556480406,259.26025688037873,195.58096556480407,255.65985688037873,192.54117556480406C253.6104568803787,190.82780556480407,251.2286568803787,189.50137756480407,248.73605688037873,188.50638556480408C250.2870568803787,190.10926556480408,251.67175688037872,191.87778556480407,252.6688568803787,193.86760556480408C253.7766568803787,197.18385556480408,251.56105688037871,199.3945855648041,249.34535688037872,201.66068556480408C237.10405688037872,209.45388556480407,222.64725688037873,207.18778556480407,210.4060568803787,212.77008556480408C200.21423688037873,217.46808556480408,199.43876688037872,229.29588556480408,195.94915688037872,239.79728556480407C197.05694688037872,243.1686855648041,198.77402688037873,245.7112855648041,201.32201688037873,243.22408556480408C203.20526688037873,241.40018556480408,203.42687688037873,238.02868556480408,204.03617688037872,235.65208556480408C204.86699688037874,232.28068556480406,206.08555688037873,229.07488556480408,207.96895688037873,226.1455855648041C211.56925688037873,220.5631855648041,217.21905688037873,216.30748556480407,223.5335568803787,214.31768556480407C234.55625688037873,210.8355855648041,246.8529568803787,214.15188556480408,257.93095688037874,210.78028556480407C258.98345688037875,210.44868556480407,260.0911568803787,210.06178556480407,260.86665688037874,209.2326855648041C261.97445688037874,208.07208556480407,262.2514568803787,206.30338556480407,262.14065688037874,204.6453855648041L262.1405568803787,204.59008556480407Z"
                                fill="#F8958F" fill-opacity="1" style="mix-blend-mode:passthrough" />
                        </g>
                    </g>
                    <g>
                        <g>
                            <g>
                                <path
                                    d="M329.882,186.3336C355.477,207.6231,364.36699999999996,243.83499999999998,361.25800000000004,276.983C360.699,283.071,359.743,289.23900000000003,356.872,294.611C354.002,299.983,348.979,304.56,342.919,305.395C335.743,306.39,328.68600000000004,301.973,324.6596,295.964C320.6331,289.955,319.1181,282.673,318.1613,275.51C316.2078,261.2246,315.7693,245.86430000000001,307.477,234.0457C301.45709999999997,225.49009999999998,291.889,219.8792,281.8824,216.8152C271.8758,213.75119999999998,261.3111,212.9552,250.8659,212.3185C242.6135,211.841,233.20492,210.8064,228.38099,204.1211C224.9524293,199.3459,224.9125842,193.0187,225.0321802,187.1294C225.55047,160.4678,240.97899999999998,160.66684,262.7862,162.935085C286.4672,165.44209,311.1049,170.69484,329.842,186.2938L329.882,186.3336Z"
                                    fill="url(#master_svg6_15_1435)" fill-opacity="1"
                                    style="mix-blend-mode:passthrough" />
                            </g>
                            <g>
                                <path
                                    d="M356.03475959777836,226.28562035217286C355.8757595977783,234.40352035217285,355.4767595977783,242.52132035217284,354.5997595977783,250.40042035217286C354.5997595977783,257.60302035217285,349.8157595977783,263.1740203521729,342.5997595977783,264.80562035217287C337.77575959777835,266.39732035217287,332.99175959777835,261.62212035217283,331.3967595977783,258.39892035217287C312.1812595977783,221.62992035217286,280.1681595977783,195.24692035217285,236.9125595977783,194.45102035217286C231.65005959777832,194.45102035217286,227.54376959777832,189.63601035217286,225.27138059777832,183.70672035217285C225.19162959777833,184.82093035217287,225.11193979777832,185.93514035217285,225.11193979777832,187.12889035217285C224.99234379777832,193.01835035217286,225.03218889777833,199.34552035217285,228.46074959777832,204.12062035217286C233.2846795977783,210.84572035217286,242.6932595977783,211.84052035217286,250.94565959777833,212.31802035217285C261.39085959777833,212.95472035217284,271.95555959777835,213.71082035217285,281.96215959777834,216.81472035217286C291.96875959777833,219.87872035217285,301.5368595977783,225.48962035217284,307.5567595977783,234.04512035217286C315.8490595977783,245.86382035217287,316.2876595977783,261.22392035217285,318.2410595977783,275.50992035217286C319.19785959777835,282.6726203521729,320.7127595977783,289.95472035217284,324.73935959777833,295.96372035217286C328.76575959777836,301.97272035217287,335.86275959777834,306.38972035217284,342.9987595977783,305.39472035217284C349.0587595977783,304.5587203521728,354.08175959777833,299.98272035217286,356.95175959777833,294.61072035217285C359.8227595977783,289.23872035217283,360.7397595977783,283.03092035217287,361.33775959777836,276.98232035217285C362.9317595977783,260.11002035217285,361.37675959777835,242.40192035217285,356.1147595977783,226.28562035217286L356.03475959777836,226.28562035217286Z"
                                    fill="url(#master_svg7_15_1525)" fill-opacity="1"
                                    style="mix-blend-mode:passthrough" />
                            </g>
                            <g>
                                <path
                                    d="M364.7258724609375,276.7832020532227C365.4833724609375,281.2400420532227,364.5664724609375,285.7764920532227,363.2508724609375,290.11399205322266C361.3770724609375,296.36159205322264,358.5465724609375,302.4499920532227,353.9618724609375,307.06599205322266C349.3771724609375,311.6820920532227,342.8389724609375,314.66659205322264,336.3406724609375,313.87059205322265C330.1612724609375,313.1145920532227,324.7792524609375,309.01579205322264,321.3506924609375,303.84279205322264C317.9221324609375,298.66969205322266,316.2477324609375,292.54149205322267,315.3706544609375,286.37339205322263C315.0915565609375,284.38379205322263,314.8922704609375,282.2746920532227,315.7294424609375,280.40441205322264C319.9154824609375,271.2519927532227,335.1047724609375,279.25037205322263,342.2409724609375,278.37491205322266C349.0581724609375,277.53929205322265,362.6527724609375,264.00957205322266,364.7657724609375,276.7832020532227L364.7258724609375,276.7832020532227Z"
                                    fill="#F8958F" fill-opacity="1" style="mix-blend-mode:passthrough" />
                            </g>
                            <g>
                                <path
                                    d="M348.18116036376955,281.55858396972656C340.16786036376953,287.9652939697266,329.76266036376956,281.55858396972656,320.15468036376956,281.55858396972656C318.1214903637695,281.79733396972654,316.40718036376956,282.4738739697266,315.09156036376953,283.42887396972657C315.09156036376953,284.38389396972656,315.21115636376953,285.37869396972656,315.33075236376953,286.33379396972657C316.24767036376954,292.46199396972656,317.8822303637695,298.62999396972657,321.3107903637695,303.8031939697266C324.7393503637695,308.9763939697266,330.16126036376954,313.07499396972656,336.30076036376954,313.83109396972657C342.7591603637695,314.6269939697266,349.33726036376953,311.64249396972656,353.92196036376953,307.02639396972654C358.50656036376955,302.41039396972656,361.33716036376956,296.3219939697266,363.2109603637695,290.07439396972654C364.52656036376953,285.77679396972655,365.4036603637695,281.20045396972654,364.68596036376954,276.74362396972657C364.52656036376953,275.74878396972656,364.2872603637695,274.91315996972656,363.9684603637695,274.19678692972656C358.4269603637695,274.1172439697266,353.2840603637695,278.33533396972655,348.14126036376956,281.5187439697266L348.18116036376955,281.55858396972656Z"
                                    fill="#F8958F" fill-opacity="1" style="mix-blend-mode:passthrough" />
                            </g>
                            <g style="opacity:0.20000000298023224;mix-blend-mode:multiply">
                                <path
                                    d="M364.72587481689453,276.7844474926758C364.5664748168945,275.7896174926758,364.32727481689454,274.95399049267576,364.0083748168945,274.2376174926758C362.8522748168945,274.2376174926758,361.6960748168945,274.39682449267576,360.57977481689454,274.7151184926758C361.17777481689455,291.94561749267575,351.41037481689455,311.48411749267575,332.19457481689454,308.7384174926758C328.5267848168945,308.2609174926758,324.81912481689454,306.0324174926758,321.15137481689453,303.4856174926758C321.23112571689455,303.60501749267576,321.27097081689453,303.7244174926758,321.3507218168945,303.8437174926758C324.7792848168945,309.0168174926758,330.20119481689454,313.1155174926758,336.34067481689453,313.8716174926758C342.7990748168945,314.66751749267576,349.3771748168945,311.6830174926758,353.9618748168945,307.0670174926758C358.5465748168945,302.45101749267576,361.37707481689455,296.3626174926758,363.2508748168945,290.1150174926758C364.5664748168945,285.81741749267576,365.4435748168945,281.24104749267576,364.72587481689453,276.7844474926758Z"
                                    fill="#E1918D" fill-opacity="1" style="mix-blend-mode:multiply" />
                            </g>
                            <g>
                                <path
                                    d="M342.20113665771487,278.37585653808594C335.06493665771484,279.2513165380859,319.8755966577148,271.25281053808595,315.68961665771485,280.4053565380859C315.25107765771486,281.3603265380859,315.09163665771484,282.3950265380859,315.09163665771484,283.42972653808596C316.40725665771487,282.43482653808593,318.12150665771486,281.79812653808597,320.15475665771487,281.5594265380859C329.76273665771487,281.5594265380859,340.16793665771485,287.96602653808594,348.18123665771486,281.5594265380859C353.3240366577148,278.3361465380859,358.4669366577148,274.1180565380859,364.00843665771487,274.23743653808594C360.50013665771485,266.11955653808593,348.50013665771485,277.6198965380859,342.20113665771487,278.37585653808594Z"
                                    fill="#FFABA6" fill-opacity="1" style="mix-blend-mode:passthrough" />
                            </g>
                        </g>
                    </g>
                </g>
                <g @click="clickBody('尿生殖三角')">
                    <path d="M255,156L370.18100000000004,278.25L139.8186,278.25L255,156Z" fill="#F25555"
                        :fill-opacity="curr == '尿生殖三角' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g transform="matrix(1,0,0,-1,0,806)" @click="clickBody('肛门三角')">
                    <path d="M255,403L370.18100000000004,525.25L139.8186,525.25L255,403Z" fill="#F25555"
                        :fill-opacity="curr == '肛门三角' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g>
                    <g>
                        <ellipse cx="261" cy="224.20693969726562" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g>
                        <g>
                            <path
                                d="M323,232C323,235.3137,325.68629,238,329,238L410,238C413.3137,238,416,235.3137,416,232L416,215C416,211.68629,413.3137,209,410,209L329,209C325.68629,209,323,211.68629,323,215L323,232Z"
                                fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M337.08,218.934L345.074,218.934L345.074,220.418L337.08,220.418L337.08,218.934ZM335.75,217.716L335.75,221.818C335.75,224.044,335.638,227.152,334.378,229.322C334.714,229.448,335.302,229.77,335.568,229.994C336.898,227.698,337.08,224.198,337.08,221.818L337.08,221.622L346.404,221.622L346.404,217.716L335.75,217.716ZM337.458,223.47L337.458,224.64600000000002L339.544,224.64600000000002C339.054,226.48,338.088,227.67,336.758,228.342C337.024,228.51,337.486,229,337.668,229.252C339.334,228.328,340.51,226.592,340.972,223.652L340.23,223.456L340.006,223.47L337.458,223.47ZM345.816,222.728C345.27,223.386,344.416,224.226,343.646,224.88400000000001C343.296,224.324,343.002,223.708,342.778,223.064L342.778,221.832L341.448,221.832L341.448,228.678C341.448,228.86,341.392,228.902,341.182,228.902C341,228.916,340.356,228.916,339.726,228.902C339.908,229.238,340.09,229.77,340.146,230.12C341.098,230.12,341.756,230.09199999999998,342.19,229.89600000000002C342.652,229.7,342.778,229.364,342.778,228.692L342.778,225.598C343.688,227.236,344.962,228.538,346.558,229.28C346.754,228.916,347.16,228.412,347.454,228.14600000000002C346.194,227.684,345.116,226.844,344.276,225.794C345.13,225.164,346.124,224.296,346.936,223.526L345.816,222.728ZM351.15,217.38C350.646,219.354,349.736,221.286,348.602,222.518C348.938,222.686,349.54,223.078,349.806,223.302C350.296,222.7,350.772,221.958,351.192,221.118L354.342,221.118L354.342,223.932L350.31,223.932L350.31,225.206L354.342,225.206L354.342,228.454L348.742,228.454L348.742,229.742L361.314,229.742L361.314,228.454L355.714,228.454L355.714,225.206L360.11,225.206L360.11,223.932L355.714,223.932L355.714,221.118L360.628,221.118L360.628,219.83L355.714,219.83L355.714,217.184L354.342,217.184L354.342,219.83L351.78,219.83C352.06,219.144,352.312,218.416,352.522,217.688L351.15,217.38ZM370.638,217.17C370.596,217.618,370.54,218.136,370.47,218.668L367.6,218.668L367.6,219.788L370.31600000000003,219.788L370.12,220.824L368.146,220.824L368.146,228.706L366.914,228.706L366.914,229.84L375.496,229.84L375.496,228.706L374.278,228.706L374.278,220.824L371.226,220.824L371.464,219.788L375.146,219.788L375.146,218.668L371.702,218.668L371.954,217.226L370.638,217.17ZM369.308,228.706L369.308,227.67L373.074,227.67L373.074,228.706L369.308,228.706ZM369.308,223.764L373.074,223.764L373.074,224.772L369.308,224.772L369.308,223.764ZM369.308,222.812L369.308,221.818L373.074,221.818L373.074,222.812L369.308,222.812ZM369.308,225.696L373.074,225.696L373.074,226.732L369.308,226.732L369.308,225.696ZM363.904,224.562C364.38,224.87,364.968,225.276,365.444,225.64C364.786,227.278,363.862,228.468,362.686,229.238C362.966,229.434,363.414,229.924,363.582,230.218C365.766,228.678,367.222,225.626,367.698,220.852L366.942,220.656L366.704,220.684L364.996,220.684C365.122,220.152,365.234,219.606,365.318,219.046L367.348,219.046L367.348,217.842L362.588,217.842L362.588,219.046L364.086,219.046C363.764,221.14600000000002,363.19,223.106,362.294,224.394C362.546,224.64600000000002,362.98,225.178,363.134,225.43C363.778,224.478,364.296,223.246,364.688,221.874L366.382,221.874C366.256,222.77,366.088,223.596,365.878,224.352C365.444,224.058,364.954,223.75,364.548,223.526L363.904,224.562ZM377.694,218.528L377.694,219.886L388.32,219.886L388.32,218.528L377.694,218.528ZM378.632,223.078L378.632,224.422L387.214,224.422L387.214,223.078L378.632,223.078ZM376.896,227.894L376.896,229.238L389.076,229.238L389.076,227.894L376.896,227.894ZM393.948,221.608L396.72,221.608L396.72,223.13400000000001L393.948,223.13400000000001L393.948,221.608ZM393.948,220.418L393.892,220.418C394.256,220.012,394.592,219.592,394.886,219.172L398.61,219.172C398.31600000000003,219.606,397.966,220.054,397.616,220.418L393.948,220.418ZM401.004,221.608L401.004,223.13400000000001L398.05,223.13400000000001L398.05,221.608L401.004,221.608ZM394.536,217.128C393.85,218.528,392.562,220.194,390.714,221.426C391.036,221.622,391.47,222.084,391.694,222.406C392.002,222.182,392.31,221.944,392.59000000000003,221.692L392.59000000000003,223.988C392.59000000000003,225.682,392.436,227.838,390.882,229.35C391.176,229.518,391.70799999999997,230.022,391.904,230.302C392.842,229.406,393.36,228.23,393.64,227.026L396.72,227.026L396.72,229.868L398.05,229.868L398.05,227.026L401.004,227.026L401.004,228.58C401.004,228.79,400.93399999999997,228.86,400.696,228.86C400.45799999999997,228.874,399.618,228.874,398.82,228.846C399.002,229.196,399.226,229.77,399.282,230.148C400.43,230.148,401.214,230.12,401.704,229.91C402.20799999999997,229.7,402.36199999999997,229.322,402.36199999999997,228.594L402.36199999999997,220.418L399.156,220.418C399.688,219.844,400.192,219.186,400.556,218.612L399.65999999999997,217.996L399.43600000000004,218.052L395.628,218.052L395.992,217.408L394.536,217.128ZM393.948,224.282L396.72,224.282L396.72,225.85L393.85,225.85C393.906,225.304,393.93399999999997,224.772,393.948,224.282ZM401.004,224.282L401.004,225.85L398.05,225.85L398.05,224.282L401.004,224.282Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-59.406054024735,100.03987046939368)">
                        <path
                            d="M325.0748704109192,246.1918458404541L262.6677374109192,224.20693884045411L263.0000034109192,223.2637528404541L325.4070704109192,245.24864584045412L325.0748704109192,246.1918458404541Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
                <g>
                    <g>
                        <ellipse cx="261" cy="349.2069396972656" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g>
                        <g>
                            <path
                                d="M323,357C323,360.3137,325.68629,363,329,363L410,363C413.3137,363,416,360.3137,416,357L416,340C416,336.68629,413.3137,334,410,334L329,334C325.68629,334,323,336.68629,323,340L323,357Z"
                                fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M342.4,342.688L342.4,347.742C342.4,349.814,342.344,352.628,341.406,354.588C341.714,354.7,342.26,354.994,342.484,355.204C343.1,353.888,343.38,352.152,343.506,350.486L345.494,350.486L345.494,353.552C345.494,353.72,345.424,353.79,345.242,353.804C345.074,353.804,344.5,353.804,343.912,353.776C344.094,354.126,344.234,354.714,344.29,355.064C345.228,355.064,345.802,355.036,346.208,354.812C346.586,354.588,346.712,354.21,346.712,353.552L346.712,342.688L342.4,342.688ZM343.604,343.92L345.494,343.92L345.494,345.922L343.604,345.922L343.604,343.92ZM343.604,347.14L345.494,347.14L345.494,349.226L343.576,349.226L343.604,347.742L343.604,347.14ZM347.314,353.188L347.314,354.462L354.44,354.462L354.44,353.188L351.57,353.188L351.57,344.732L354.076,344.732L354.076,343.43L347.552,343.43L347.552,344.732L350.142,344.732L350.142,353.188L347.314,353.188ZM356.68,342.8C357.394,343.612,358.262,344.76,358.654,345.474L359.746,344.704C359.326,344.004,358.416,342.912,357.702,342.128L356.68,342.8ZM356.218,345.124L356.218,355.162L357.562,355.162L357.562,345.124L356.218,345.124ZM360.054,342.674L360.054,343.948L366.494,343.948L366.494,353.552C366.494,353.832,366.41,353.916,366.13,353.916C365.85,353.944,364.856,353.944,363.918,353.902C364.114,354.238,364.324,354.812,364.38,355.162C365.71,355.176,366.578,355.148,367.124,354.938C367.656,354.728,367.838,354.35,367.838,353.552L367.838,342.674L360.054,342.674ZM370.694,343.528L370.694,344.886L381.32,344.886L381.32,343.528L370.694,343.528ZM371.632,348.078L371.632,349.422L380.214,349.422L380.214,348.078L371.632,348.078ZM369.896,352.894L369.896,354.238L382.076,354.238L382.076,352.894L369.896,352.894ZM386.948,346.608L389.72,346.608L389.72,348.134L386.948,348.134L386.948,346.608ZM386.948,345.418L386.892,345.418C387.256,345.012,387.592,344.592,387.886,344.172L391.61,344.172C391.31600000000003,344.606,390.966,345.054,390.616,345.418L386.948,345.418ZM394.004,346.608L394.004,348.134L391.05,348.134L391.05,346.608L394.004,346.608ZM387.536,342.128C386.85,343.528,385.562,345.194,383.714,346.426C384.036,346.622,384.47,347.084,384.694,347.406C385.002,347.182,385.31,346.944,385.59000000000003,346.692L385.59000000000003,348.988C385.59000000000003,350.682,385.436,352.838,383.882,354.35C384.176,354.51800000000003,384.70799999999997,355.022,384.904,355.302C385.842,354.406,386.36,353.23,386.64,352.026L389.72,352.026L389.72,354.868L391.05,354.868L391.05,352.026L394.004,352.026L394.004,353.58C394.004,353.79,393.93399999999997,353.86,393.696,353.86C393.45799999999997,353.874,392.618,353.874,391.82,353.846C392.002,354.196,392.226,354.77,392.282,355.148C393.43,355.148,394.214,355.12,394.704,354.91C395.20799999999997,354.7,395.362,354.322,395.362,353.594L395.362,345.418L392.156,345.418C392.688,344.844,393.192,344.186,393.556,343.612L392.65999999999997,342.996L392.436,343.052L388.628,343.052L388.992,342.408L387.536,342.128ZM386.948,349.282L389.72,349.282L389.72,350.85L386.85,350.85C386.906,350.304,386.93399999999997,349.772,386.948,349.282ZM394.004,349.282L394.004,350.85L391.05,350.85L391.05,349.282L394.004,349.282Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-100.93847737196973,107.14136901360985)">
                        <path
                            d="M325.0748704109192,371.1918458404541L262.6677374109192,349.2069388404541L263.0000034109192,348.2637528404541L325.4070704109192,370.2486458404541L325.0748704109192,371.1918458404541Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
                <g @click="clickBody('会阴')">
                    <ellipse cx="248" cy="307" rx="9" ry="9" :fill="curr == '会阴' ? '#D46E6E' : '#F25555'"
                        :fill-opacity="curr == '会阴' ? '1' : '0.23999999821186066'" />
                </g>

                <g>
                    <g>
                        <ellipse cx=" 248" cy="307.2069396972656" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g>
                        <g>
                            <rect x="310" y="292.20703125" width="63" height="29" rx="6" fill="#557AF2"
                                fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M329.212,313.10303125C329.828,312.86503125,330.682,312.82303125,337.892,312.24903125000003C338.2,312.65503125,338.452,313.04703125,338.634,313.36903125L339.824,312.65503125C339.194,311.59103125,337.892,310.10703125,336.646,309.00103125L335.512,309.58903125C336.002,310.03703125,336.506,310.56903125,336.968,311.10103125L331.214,311.49303125C332.138,310.65303125,333.034,309.67303125,333.804,308.67903125L339.852,308.67903125L339.852,307.37703125L328.232,307.37703125L328.232,308.67903125L331.97,308.67903125C331.13,309.78503125,330.206,310.72303125,329.842,311.03103125C329.408,311.43703125,329.086,311.68903124999997,328.764,311.74503125C328.918,312.12303125,329.128,312.80903125,329.212,313.10303125ZM334.014,300.36303125C332.712,302.19703125,330.206,303.94703125,327.504,305.03903125C327.812,305.30503125,328.26,305.87903125,328.456,306.21503125C329.24,305.86503125,329.996,305.45903125,330.71,305.01103125L330.71,305.90703125L337.346,305.90703125L337.346,304.89903125C338.088,305.34703125,338.858,305.75303125,339.628,306.06103125C339.838,305.69703125,340.272,305.16503125,340.566,304.89903125C338.382,304.17103125,336.114,302.75703125,334.784,301.51103125L335.246,300.90903125L334.014,300.36303125ZM331.242,304.67503125C332.278,303.98903125,333.216,303.21903125,334.028,302.36503125C334.812,303.13503125,335.848,303.94703125,336.98199999999997,304.67503125L331.242,304.67503125ZM352.606,305.50103125L352.606,307.65703125L348.91,307.65703125L348.938,306.56503125L348.938,305.50103125L352.606,305.50103125ZM352.606,304.31103125L348.938,304.31103125L348.938,302.23903125L352.606,302.23903125L352.606,304.31103125ZM347.678,301.03503125L347.678,306.56503125C347.678,308.60903125,347.51,311.11503125,345.802,312.83703125C346.138,312.96303125,346.684,313.29903125,346.908,313.52303125C348.126,312.27703125,348.63,310.54103125,348.826,308.86103125L352.606,308.86103125L352.606,311.70303125C352.606,311.92703125,352.536,311.98303125,352.326,311.99703125C352.13,311.99703125,351.444,311.99703125,350.772,311.98303125C350.954,312.31903125,351.136,312.92103125,351.178,313.27103125C352.214,313.27103125,352.872,313.24303125,353.32,313.03303125C353.754,312.80903125,353.894,312.43103125,353.894,311.73103125L353.894,301.03503125L347.678,301.03503125ZM342.12,300.95103125L342.12,313.35503125L343.366,313.35503125L343.366,302.14103125L345.228,302.14103125C344.962,303.06503125,344.598,304.28303125,344.248,305.20703125C345.172,306.25703125,345.396,307.18103125,345.396,307.89503125C345.396,308.31503125,345.312,308.65103125,345.116,308.79103125C345.004,308.87503125,344.864,308.90303125,344.696,308.91703125C344.5,308.93103125,344.262,308.91703125,343.98199999999997,308.88903125C344.178,309.25303125,344.276,309.78503125,344.29,310.12103125C344.626,310.13503125,344.976,310.12103125,345.256,310.09303125C345.564,310.05103125,345.816,309.96703125,346.04,309.79903125C346.46,309.49103125,346.642,308.88903125,346.642,308.03503125C346.642,307.19503125,346.432,306.20103125,345.48,305.06703125C345.928,303.97503125,346.418,302.60303125,346.81,301.42703125L345.9,300.90903125L345.69,300.95103125L342.12,300.95103125Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-87.72213897589734,100.4358934746408)">
                        <path
                            d="M312.0748704109192,329.1918458404541L249.6677374109192,307.2069388404541L250.0000034109192,306.2637528404541L312.4070704109192,328.2486458404541L312.0748704109192,329.1918458404541Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
            </g>
        </svg>
    </view>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"
const props = defineProps({
    width: {
        type: String,
        default: ''
    },
    height: {
        type: String,
        default: ''
    },
})
const emit = defineEmits(['selectBody'])
const curr = ref('')
const clickBody = (body) => {
    curr.value = body
    emit('selectBody', body)
}
onMounted(async () => {
    nextTick(() => {
        uni.pageScrollTo({
            selector: '.page-bottom',
            duration: 20
        })
    })
})
</script>
<template>
    <view>
        <svg :style="{ width: width, height: height }" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="500" height="500"
            viewBox="0 0 500 500">
            <defs>
                <clipPath id="master_svg0_1_7910">
                    <rect x="0" y="0" width="500" height="500" rx="0" />
                </clipPath>
            </defs>
            <g clip-path="url(#master_svg0_1_7910)">
                <g>
                    <path
                        d="M482.534,388.456C484.759,358.759,486.985,267.348,488.083,184.609C489.181,101.87,434.802,79.9754,410.365,78.4791C385.928,76.9827,340.422,70.8449,318.21,66.753Q295.999,62.6611,299.337,0L201.633,0Q204.957,62.6,182.76,66.753C160.563,70.906,115.028,76.9675,90.6055,78.4791C66.1832,79.9906,11.77476,101.87,12.9024,184.609C14.03,267.348,16.211100000000002,358.759,18.436700000000002,388.456C19.549500000000002,411.359,4,481.013,4,504.999Q4,528.986,90.6055,504.999Q91.7183,423.879,90.6055,389.601Q89.4927,355.324,89.4927,274.203Q93.9439,339.338,102.8463,367.905Q108.395,387.326,113.945,431.879Q119.242,467.943,113.945,515.672L387.055,515.672Q381.758,467.943,387.055,431.879Q392.604,387.326,398.154,367.905Q407.056,339.338,411.507,274.203Q411.507,355.324,410.394,389.601Q409.282,423.879,410.394,504.999Q497,529.001,497,504.999C497,480.997,481.421,411.313,482.534,388.456Z"
                        fill="#FBD3C3" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.3100000023841858;mix-blend-mode:multiply">
                    <ellipse cx="167.5" cy="298.5" rx="12.5" ry="10.5" fill="#F8B28D" fill-opacity="1"
                        style="mix-blend-mode:multiply" />
                </g>
                <g style="opacity:0.3100000023841858;mix-blend-mode:multiply">
                    <ellipse cx="168" cy="299" rx="7" ry="6" fill="#F8B28D" fill-opacity="1"
                        style="mix-blend-mode:multiply" />
                </g>
                <g style="opacity:0.3100000023841858;mix-blend-mode:multiply">
                    <ellipse cx="345" cy="298.5" rx="12" ry="10.5" fill="#F8B28D" fill-opacity="1"
                        style="mix-blend-mode:multiply" />
                </g>
                <g style="opacity:0.3100000023841858;mix-blend-mode:multiply">
                    <ellipse cx="345.5" cy="299" rx="7.5" ry="6" fill="#F8B28D" fill-opacity="1"
                        style="mix-blend-mode:multiply" />
                </g>
                <g style="opacity:0.5;">
                    <g>
                        <path
                            d="M78.3646,299.69849999999997C71.8364,279.9348,58.95337,237.7925,74.03172,223C74.21948,234.1285,77.65690000000001,252.6304,88.7923,261.7066C95.4072,267.10360000000003,98.5413,314.1108,100,351.404Q94.2229,320.0248,91.3343,276.4686Q91.3343,357.241,92.4175,391.372Q93.5007,425.502,92.4175,506.275Q86.4526,507.963,77.6858,510C80.6146,486.248,82.2781,462.342,82.6686,438.394C83.7518,381.14,84.8783,319.523,78.3646,299.69849999999997Z"
                            fill="#FEB9A2" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.20000000298023224;">
                    <g>
                        <path
                            d="M70.0032,98.13159999999999C27.0652,98.13159999999999,28.1397,155.77100000000002,41.3582,163.5333Q54.5767,171.2955,81.1021,144.172C76.6861,159.1781,70.0621,184.598,51.3384,207.129C32.614599999999996,229.661,35.853,313.179,35.853,374.804C35.853,419.481,34.6901,474.751,34.0572,501.785C18.1007,502.733,5,500.748,5,491.653C5,468.381,20.4265,400.757,19.322499999999998,378.581C17.1292,349.769,14.90652,261.08000000000004,13.81724,180.80599999999998C12.74269,102.7089,63.865,80.48866,88.8889,78C115.856,92.4876,105.007,98.13159999999999,70.0032,98.13159999999999Z"
                            fill="#E78B7D" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g>
                    <path
                        d="M139,375.3123L141.62631,375.4629C143.31784,375.4629,145.85513,375.86990000000003,149.0453,375.7644L154.2534,375.7644L160.3667,375.3424C162.53300000000002,375.1313,164.818,374.7545,167.2515,374.4381C169.6849,374.1216,172.17770000000002,373.5639,174.7891,372.9309C179.9973,371.7252,185.5615,370.0823,191.3631,368.1079C197.1648,366.1335,203.1148,363.51103,209.4803,360.9488C211.1273,360.31581,212.6704,359.63756,214.43619999999999,359.03466C216.2903,358.31722,218.2014,357.762605,220.1488,357.376795L221.9442,357.150681C222.5971,357.0753406,222.8938,357.150681,223.428,357.150681L226.084,357.150681L231.4256,357.150681L253.13400000000001,357L274.70799999999997,357.0754326L280.48,357.0754326L281.8,357.271373C282.302,357.315161,282.799,357.405956,283.284,357.542655C284.353,357.813936,285.56899999999996,358.11539,286.48900000000003,358.44693C290.05,359.68282,293.241,361.03923,296.416,362.32039C302.722,364.86753,308.717,367.264,314.548,369.148C320.02099999999996,370.9222,325.589,372.3765,331.226,373.5037C336.015,374.3817,340.851,374.9805,345.70799999999997,375.2973L351.821,375.5083L357.044,375.3576C358.453,375.3426,359.85900000000004,375.247,361.25800000000004,375.0713L364.389,374.7547L367,374.5136L364.49199999999996,375.2672L361.451,376.1414C360.08299999999997,376.58050000000003,358.68399999999997,376.9131,357.26599999999996,377.1361L352.014,378.1158C350.08500000000004,378.4172,347.978,378.5228,345.722,378.7488C340.668,379.1201,335.592,379.0798,330.543,378.6282C327.76800000000003,378.372,324.875,378.01030000000003,321.89300000000003,377.558C318.90999999999997,377.1058,315.868,376.4277,312.767,375.6288C306.294,374.0311,299.914,372.0688,293.656,369.7508C290.43600000000004,368.6053,287.187,367.3996,284.05600000000004,366.46513L282.171,365.98282C281.789,365.87823,281.395,365.81752,280.999,365.80197L279.679,365.66628000000003L274.70799999999997,365.66628000000003L253.015,365.81696L231.30689999999998,365.66628000000003L225.96519999999998,365.66628000000003L223.3092,365.66628000000003C222.9043,365.63583,222.4976,365.63583,222.0925,365.66628000000003L221.2913,365.74162C219.8043,365.98246,218.34,366.35051,216.91410000000002,366.84184C215.4303,367.294,213.7388,367.912,212.166,368.4394C205.7856,370.7906,199.3756,372.961,193.2327,374.6038C187.433,376.1762,181.5332,377.3396,175.5756,378.0854C172.8009,378.3869,170.13,378.6129,167.6076,378.7637C165.0851,378.9144,162.66649999999998,378.7637,160.4112,378.7637L154.105,378.3567L148.79305,377.588L146.53768,377.2414L144.60874,376.6688L141.55213,375.9001L139,375.3123Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g>
                    <path
                        d="M426.4612,198C428.1967,201.83125,429.522,205.8152,430.4175,209.893C430.8447,211.7364,431.2404,213.7434,431.5253,215.8395C431.8414,218.1184,432,220.4127,432,222.7076C432,223.9118,432,225.1308,431.8417,226.3796C431.7731,227.6558,431.5878,228.9241,431.2878,230.1704C430.7618,232.832,429.7841,235.39679999999998,428.3919,237.767C426.9992,239.878,425.5908,241.9742,424.214,244.0405C422.8372,246.1069,421.4288,248.0247,420.27356,249.987C419.11836,251.9494,418.10552,253.8671,417.10853,255.7254C416.11154,257.5837,415.52602,259.4717,414.90881,261.211C414.29161,262.95029999999997,413.75356,264.5559,413.24719,265.9831L412.09194,269.7294Q411.427262,271.8107,411,273Q411,271.7512,411.158261,269.5808C411.158261,268.4807,411.316522,267.1576,411.474783,265.6412C411.633044,264.1248,411.933726,262.4153,412.26604,260.557C412.59836,258.69870000000003,413.16809,256.6918,413.84856,254.6105C414.59065,252.4042,415.50572999999997,250.2526,416.58632,248.17340000000002C417.10853,247.07330000000002,417.75737,246.0327,418.37457,244.9623C418.99178,243.892,419.64056,242.8216,420.32108,241.79579999999999C421.6345,239.7145,422.9955,237.58870000000002,424.3407,235.4777C425.4971,233.5408,426.3348,231.45080000000002,426.8252,229.2785C427.3595,227.0738,427.6983,224.8312,427.8381,222.5738C427.9805,220.3439,428.0438,218.1139,428.0121,216.0773C427.9804,214.0406,427.8697,212.0188,427.7431,210.1308Q427.1733,202.95048,426.4612,198Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g>
                    <path
                        d="M398,207Q398.654889,207.704973,399.66447,209.05629C401.06901,210.92881,402.36747,212.8907,403.55279,214.93131C405.24862,217.7666,406.78402,220.7094,408.1505,223.7438C408.819,225.4475,409.6513,227.1661,410.238,229.01670000000001C410.8246,230.8673,411.5204,232.718,412.0389,234.6273C412.5573,236.5367,413.0348,238.4461,413.4032,240.3555C413.809,242.2221,414.1277,244.1093,414.3582,246.0102C414.6239,247.7798,414.7879,249.5653,414.8494,251.35649999999998C414.9806,252.9533,415.0261,254.55689999999998,414.9858,256.15930000000003C414.9858,257.6281,414.9858,258.994,414.8903,260.1984C414.7948,261.4028,414.6583,262.4456,414.5765,263.3122Q414.3718,265.0159,414.2627,266Q414.2627,265.016,414.1808,263.2828C414.1808,262.4309,414.1808,261.3882,414.1808,260.1985C414.1808,259.0088,413.9625,257.6576,413.8943,256.2035C413.825,254.6333,413.6701,253.06889999999999,413.4305,251.5182C413.1849,249.8585,413.0075,248.1107,412.6392,246.33350000000002C412.2708,244.5562,412.0116,242.691,411.5341,240.8403C411.0566,238.9897,410.6882,237.1097,410.1016,235.2591C409.5149,233.4084,409.0374,231.5578,408.4098,229.766C407.78224,227.9741,407.141,226.2263,406.52704,224.5225C405.16272,221.1884,403.89391,218.0599,402.57052,215.48962C401.24713,212.9193,400.19662,210.76028,399.35075,209.29152Q398.504875,207.822768,398,207Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g>
                    <path
                        d="M74.49206,198Q73.77778,202.94068,73.20635,210.2552C73.07936,212.0801,73.01587,214.0683,72.93651,216.1899C72.85714,218.3116,72.93651,220.4481,73.11111,222.6736C73.25131999999999,224.9265,73.59111,227.1646,74.12698,229.365C74.61889,231.53300000000002,75.4591,233.619,76.61905,235.5519C77.96825,237.6587,79.3333,239.7804,80.6508,241.8576Q81.6667,243.3413,82.6032,245.0178C83.2222,246.086,83.873,247.1246,84.3968,248.2225C85.4807,250.2976,86.3986,252.445,87.1429,254.64690000000002C87.7778,256.5991,88.30760000000001,258.5799,88.7302,260.5816C89.0635,262.4362,89.3175,264.1425,89.5238,265.6558C89.7302,267.1691,89.7619,268.4896,89.8413,269.5876Q89.9683,271.7538,90,273Q89.5714,271.8131,88.9048,269.7359L87.746,265.997C87.2381,264.5133,86.66669999999999,263.0296,86.1587,261.2343C85.6508,259.4391,84.69839999999999,257.6883,83.9365,255.7596C83.1746,253.8308,82.0317,251.9613,80.7619,250.0326C79.4921,248.1038,78.19048000000001,246.175,76.80952,244.0979L72.61905,237.83679999999998C71.22266,235.4712,70.24191,232.9115,69.714281,230.2552C69.413471,229.0113,69.227604,227.7455,69.158728,226.4718C69,225.2255,69.0476171,224.00900000000001,69,222.8072C69.000108991,220.5151,69.15917,218.2254,69.476189,215.9526C69.761904,213.8012,70.15873,211.7983,70.5873,210.0179C71.45475,205.8998,72.76291,201.87356,74.49206,198Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g>
                    <path
                        d="M103,207Q102.482,207.822502,101.6368,209.29126C100.7916,210.76002,99.6874,212.90445,98.4196,215.48939000000001C97.1518,218.0743,95.8158,221.1882,94.47984,224.5223C93.85276,226.226,93.11662,227.9592,92.58496,229.7658C92.05330000000001,231.57229999999998,91.37169,233.3936,90.89456,235.2589C90.41741999999999,237.1243,89.88577,238.9896,89.46317,240.8402C89.04057,242.6909,88.69976,244.54149999999998,88.35896,246.33339999999998C88.01815,248.1252,87.81366,249.85840000000002,87.56828,251.5181C87.3288,253.0688,87.17404,254.6332,87.10479,256.2035C87.03663,257.6722,86.859406,259.0088,86.81851,260.1985C86.777615,261.3882,86.81851,262.431,86.81851,263.2828Q86.81851,265.016,86.736715,266Q86.627654,265.016,86.423174,263.3121C86.341378,262.4455,86.232323,261.4028,86.109633,260.1984C85.9869421,258.994,86.0278372,257.6427,86.0142072,256.15930000000003C85.9738893,254.55689999999998,86.0194075,252.9533,86.150528,251.3564C86.211896,249.5652,86.375799,247.7797,86.64129,246.0101C86.871611,244.1092,87.1901,242.222,87.59555,240.3554C88.00451,238.446,88.42712,236.522,88.95877,234.6272C89.49043,232.73239999999998,90.10388,230.83780000000002,90.75823,229.0165C91.41258,227.1953,92.12146,225.4475,92.8576,223.74360000000001C94.21355,220.7067,95.74331,217.7635,97.438,214.93107C98.6271,212.89387,99.9244,210.93223,101.3232,209.05603Q102.3457,207.704976,103,207Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.30000001192092896;">
                    <g>
                        <path
                            d="M328.6442,183.0139431C302.9513,182.342032,272.1286,206.0823,262.7673,230.793C248.89924,267.5829,259.86907,327.15700000000004,297.5461,345.224C328.6297,360.155,319.2829,347.449,304.7917,335.534C276.3455,312.077,260.69508,254.0555,291.9815,224.8655C304.35699999999997,213.3089,321.7029,213.294,335.8463,205.2014C349.98969999999997,197.1089,356.5107,183.745546,328.6442,183.0139431Z"
                            fill="#EFB09B" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g>
                    <path
                        d="M262.93704,231L262.68138,232.48709C262.48587,233.42396,262.23022,234.83671,261.95953,236.57657C261.18074,241.3475,260.67863,246.1587,260.45565,250.9864C260.33534,253.9606,260.30526,257.1727,260.45565,260.5484C260.60605,263.9241,260.75642,267.4931,261.11736,271.1364C261.4783,274.7798,262.04976,278.5719,262.62124,282.3194C263.1927,286.0669,264.1251,289.9035,265.1929,293.6658C266.2606,297.4282,267.4036,301.1013,268.7571,304.68510000000003C270.0792,308.171,271.5851,311.5861,273.2687,314.9163C274.8278,318.0439,276.5807,321.0734,278.5172,323.9875C280.2417,326.6259,282.1197,329.1632,284.1417,331.586C285.0892,332.717,286.09680000000003,333.71299999999997,286.984,334.73900000000003L289.69100000000003,337.475C291.5107,339.081,293.0747,340.539,294.5034,341.609C295.9321,342.68,297.0299,343.528,297.8119,344.108L299,345L297.4961,344.569C296.1388,344.152,294.808,343.656,293.5109,343.082C291.4367,342.233,289.4265,341.23900000000003,287.4954,340.108L284.02139999999997,337.907C282.8634,337.044,281.6303,336.137,280.3971,335.126C277.7249,332.948,275.2493,330.5448,272.998,327.9432C270.4688,325.0455,268.1853,321.9469,266.1704,318.6786C264.0112,315.19849999999997,262.12576,311.5593,260.53088,307.7932C258.88383,303.9223,257.49724,299.9481,256.38019,295.8965C255.30171,291.8447,254.46808,287.733,253.883747,283.5834C253.337257,279.5704,253.0459405,275.52750000000003,253.0114966,271.4784C252.9506174,267.7085,253.131442,263.9386,253.552893,260.1914C253.906029,256.8331,254.47361,253.5003,255.25228,250.213C255.87234,247.4422,256.66081,244.7109,257.61338,242.0341C258.32956,239.96681,259.16303,237.94113,260.10982,235.96673C260.68579,234.6953,261.32832,233.45436,262.03477,232.24901L262.93704,231Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.20000000298023224;">
                    <g>
                        <path
                            d="M281.0360737,270C280.9552275,268.4432,281.010297,266.8824,281.200666,265.3349C281.383683,263.551,281.683552,261.7807,282.09845,260.03499999999997L282.90643,256.7871C283.29545,255.6798,283.71441,254.5136,284.13339,253.303C284.56094,252.0313,285.06049,250.7843,285.62968,249.5679C286.21508,248.24099999999999,286.87959,246.9494,287.61974,245.7Q288.78684,243.766,290.04371,241.7583C290.95836,240.418,291.9579,239.136,293.0363,237.91989999999998C295.16,235.2683,297.5076,232.799,300.0539,230.5384C301.3257,229.35739999999998,302.822,228.4125,304.1986,227.3349L306.3233,225.77C307.0117,225.2158,307.7476,224.7219,308.5228,224.2937L313.1463,221.75447L315.4506,220.51434C316.2436,220.14526,317.0666,219.86476,317.8746,219.54002C319.4756,218.90521999999999,321.0766,218.2852,322.6627,217.70941L325.01189999999997,216.83836L327.4059,216.23311C330.578,215.43594,333.6454,214.68301,336.5632,214.15156C339.4809,213.620113,342.3239,213.531538,344.8975,213.295279C346.1843,213.191927,347.3963,213.0295549,348.5784,213L351.9151,213.0885747C354.0098,213.0885747,355.8353,213.221482,357.3316,213.295279Q360.3242,213.442873,362,213.605335Q360.3242,214.01874,357.4064,214.60921C355.9101,214.90449,354.1894,215.27357,352.1694,215.61309L348.9674,216.17409C347.8451,216.42504,346.693,216.74987,345.481,217.05984C343.057,217.69464,340.4385,218.35899,337.6704,218.97902C334.90229999999997,219.59904,332.179,220.89819,329.2314,221.78403L327.0318,222.49263L324.9071,223.4669C323.4856,224.1313,322.0342,224.7661,320.5679,225.3861C319.8347,225.7109,319.0716,226.0061,318.3684,226.3604L316.2885,227.5562L312.084,229.9183C311.3641,230.273,310.68240000000003,230.6984,310.049,231.1879L308.1038,232.5756C306.817,233.5205,305.4255,234.3324,304.2584,235.38060000000002C301.8817,237.34,299.6281,239.4402,297.5102,241.6697C296.5076,242.7917,295.4154,243.81040000000002,294.5176,244.9471L291.8841,248.29829999999998C290.9714,249.3612,290.38785,250.5718,289.60977,251.63479999999998C288.86882,252.6648,288.19415,253.7397,287.58978,254.85309999999998L285.86906,257.9238L284.49249,260.7879C283.63959,262.6037,282.9962,264.2425,282.45754,265.5859C281.918876,266.9293,281.559759,268.0365,281.350281,268.7894Q281.140802,269.5424,281.0360737,270Z"
                            fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.30000001192092896;">
                    <g>
                        <path
                            d="M169.567,183.0139431C195.5427,182.342032,226.7048,206.0823,236.1692,230.793C250.19,267.5829,239.0993,327.15700000000004,201.0074,345.224C169.5816,360.155,179.0314,347.449,193.6821,335.534C222.44150000000002,312.077,238.26420000000002,254.0555,206.648,224.8655C194.1362,213.26409999999998,176.5993,213.26409999999998,162.3002,205.2013C148.0011,197.1386,141.37897,183.745546,169.567,183.0139431Z"
                            fill="#EFB09B" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g>
                    <path
                        d="M236.2037,231L236.9413,232.29325C237.65449999999998,233.49731,238.30270000000002,234.73785,238.8832,236.00951C239.81889999999999,237.98649,240.648,240.01102,241.3671,242.0745C242.326,244.7499,243.1202,247.4802,243.7455,250.2502C244.51760000000002,253.5378,245.0857,256.8688,245.4466,260.2247C245.8684,263.9705,246.0494,267.739,245.9885,271.5073C245.9489,275.5551,245.6522,279.5964,245.1003,283.6075C244.5136,287.7541,243.6842,291.8637,242.6165,295.9157C241.4915,299.9637,240.1037,303.9359,238.46179999999998,307.8078C236.8602,311.5727,234.9679,315.2105,232.8016,318.68899999999996C230.7948,321.95889999999997,228.5137,325.0567,225.98239999999998,327.9499C223.7289,330.5505,221.2508,332.953,218.5761,335.13C217.3417,336.141,216.1073,337.047,214.94819999999999,337.909L211.4557,340.109C209.52445,341.244,207.51199,342.23699999999997,205.43432,343.082C204.13189,343.659,202.79458,344.156,201.43008,344.569L200,345L201.20429,344.108C201.98708,343.528,203.14618,342.741,204.51606,341.611C205.88593,340.481,207.52676,339.084,209.31813,337.478L212.0428,334.743C212.931,333.717,213.9245,332.721,214.8729,331.592C216.8969,329.16949999999997,218.7767,326.6332,220.5029,323.9957C222.452,321.0865,224.2118,318.0579,225.7716,314.9281C227.4506,311.5963,228.9578,308.1829,230.2877,304.7009C231.64249999999998,301.1185,232.8167,297.41700000000003,233.8554,293.686C234.8941,289.9549,235.707,286.1196,236.4145,282.344C237.122,278.5683,237.6489,274.8223,237.9198,271.1654C238.1908,267.5086,238.5069,263.9559,238.5671,260.5815C238.6273,257.2071,238.6424,253.9963,238.5671,251.0233C238.3363,246.1981,237.8338,241.3892,237.0618,236.61912C236.7908,234.87993,236.5349,233.46774,236.3392,232.53123L236.2037,231Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.20000000298023224;">
                    <g>
                        <path
                            d="M217.95170000000002,270Q217.8483,269.5863,217.5972,268.8328C217.3462,268.0794,216.9769,267.0008,216.5042,265.6268C216.0316,264.2528,215.3521,262.6719,214.4955,260.8251C214.06709999999998,259.9239,213.624,258.9635,213.1366,257.9588L211.4528,254.88569999999999C210.8562,253.7714,210.1902,252.6957,209.4588,251.6648C208.6907,250.6011,208.1147,249.3896,207.21370000000002,248.32580000000002L204.6141,244.9719C203.7427,243.83429999999998,202.6644,242.8148,201.6601,241.692C199.5641,239.4608,197.3346,237.359,194.9839,235.3981C193.8318,234.349,192.4582,233.5365,191.2027,232.5909L189.2678,231.2021C188.6477,230.7122,187.9796,230.2865,187.2738,229.9315L183.1233,227.5676L181.0702,226.3708C180.3613,226.0163,179.5932,225.7208,178.899,225.3958C177.422,224.7753,175.9449,224.14,174.6156,223.4751L172.5035,222.50002L170.3322,221.79087C167.3782,220.90443,164.69,219.81104,162.0165,218.98367C159.3431,218.1563,156.6992,217.69829,154.3064,217.063C153.11,216.7527,151.958,216.4277,150.8649,216.17656L147.68932,215.61512C145.71011,215.27534,143.96721,214.90597,142.53449,214.61046Q139.58042,214.01945,138,213.605807Q139.66904,213.443308,142.60834,213.295509C144.08537,213.221654,145.88735,213.118222,147.94043,213.0886436L151.249,213C152.40109999999999,213,153.6122,213.192076,154.8825,213.295509C157.423,213.531862,160.185,213.783094,163.0948,214.15246C166.0045,214.52182,169.0029,215.43783,172.1342,216.23563L174.4974,216.84134L176.8312,217.71308C178.39679999999998,218.28931,179.9772,218.90981,181.5577,219.54511C182.3553,219.8701,183.1529,220.15083,183.9357,220.52019L186.2103,221.76129L190.77429999999998,224.3025C191.5465,224.7273,192.2782,225.2218,192.9603,225.7799L195.043,227.346C196.41660000000002,228.4245,197.8936,229.3701,199.1343,230.5521C201.6563,232.81040000000002,203.9789,235.282,206.0764,237.9393C207.1362,239.1602,208.1226,240.4429,209.03050000000002,241.7807L211.4232,245.7255C212.1492,246.976,212.80020000000002,248.2686,213.37290000000002,249.59640000000002C213.97230000000002,250.8065,214.4904,252.0553,214.9238,253.33440000000002C215.3521,254.54590000000002,215.76569999999998,255.7131,216.1498,256.8211L216.94729999999998,260.0716C217.34980000000002,261.82,217.6458,263.5913,217.8336,265.3757C218.0042,266.911,218.0437,268.45799999999997,217.95170000000002,270Z"
                            fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.5;">
                    <g>
                        <path
                            d="M425.6418,299.69849999999997C432.1575,279.9348,445.0443,237.7925,429.9759,223C429.7737,234.1285,426.3353,252.6304,415.1965,261.7066C408.57976,267.10360000000003,405.44471,314.1108,404,351.404Q409.77885,320.0248,412.66827,276.4686Q412.66827,357.241,411.58474,391.372Q410.50121,425.502,411.58474,506.275Q417.5659,507.963,426.3208,510C423.3936,486.248,421.7248,462.342,421.3221,438.394C420.2675,381.14,419.1261,319.523,425.6418,299.69849999999997Z"
                            fill="#FEB9A2" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.20000000298023224;">
                    <g>
                        <path
                            d="M432.0234,98.1317C474.9975,98.1317,473.9224,155.7711,460.6974,163.5333Q447.4723,171.29559999999998,420.9339,144.1721C425.352,159.1782,431.9793,184.598,450.7123,207.129C469.4453,229.661,466.14639999999997,313.179,466.14639999999997,374.804C466.14639999999997,419.481,467.2952,474.751,467.9432,501.785C483.9075,502.733,497,500.748,497,491.653C497,468.381,481.56579999999997,400.757,482.6704,378.581C484.8795,349.769,487.0886,261.08000000000004,488.1784,180.80599999999998C489.25350000000003,102.7089,438.1794,80.48866,413.0695,78C386.1482,92.4877,397.016852,98.1317,432.0234,98.1317Z"
                            fill="#E78B7D" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.6000000238418579;">
                    <g>
                        <path
                            d="M220,11L228.29941,30.796C229.56172,33.7462,230.9982,36.8587,232.5217,40.1334C234.0452,43.4082,235.7428,46.8009,237.6145,50.1642C239.438,53.528,241.5707,56.7087,243.9841,59.6639C246.3346,62.4371,249.2365,64.576,251.0937,64.31049999999999L251.1808,64.31049999999999L250.8326,64.31049999999999L250.9777,64.31049999999999L251.36939999999998,64.31049999999999L252.1529,64.2072C252.3134,64.2011,252.4691,64.15010000000001,252.6027,64.05969999999999C252.68439999999998,64.01259999999999,252.7724,63.9778,252.8639,63.9564C253.15370000000001,63.8502,253.43110000000001,63.7116,253.6909,63.5434C254.0345,63.3909,254.35649999999998,63.1924,254.6486,62.9533C256.0589,61.9596,257.3325,60.7789,258.4355,59.4426C260.886,56.5058,263.0576,53.3399,264.9212,49.9871C266.8365,46.6533,268.5631,43.2754,270.13009999999997,40.030100000000004C271.6972,36.784800000000004,273.1626,33.6576,274.483,30.7517L283,11.0442065Q282.8549,13.22739,282.3036,16.94465C281.544,22.147199999999998,280.5025,27.3032,279.18399999999997,32.388999999999996C277.3832,39.692499999999995,274.8905,46.8014,271.7407,53.6159C269.90250000000003,57.6507,267.5987,61.4487,264.8777,64.93C263.3624,66.9166,261.5683,68.666,259.5527,70.1224C259.0122,70.54050000000001,258.4341,70.906,257.8261,71.214C257.1786,71.5989,256.4989,71.9247,255.7948,72.1875L254.547,72.6006C254.1957,72.7145,253.8369,72.80330000000001,253.4733,72.8661L252.6898,72.9988L252.298,72.9988L252.19639999999998,72.9988L251.7031,72.9988L251.3984,72.9988C248.3611,73.03829999999999,245.3931,72.07560000000001,242.9394,70.2551C240.9039,68.7801,239.0863,67.0167,237.5419,65.0185C234.8626,61.5014,232.6027,57.6738,230.8095,53.6159C227.73075,46.7612,225.30152,39.6238,223.55479,32.3005C222.31129,27.2001,221.34255,22.0345,220.652899,16.82666Q220.13058,13.18313,220,11Z"
                            fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g>
                    <path
                        d="M264,142C264.877658,140.549,265.86383,139.1658,266.95018,137.8621C269.75676,134.353,272.97012,131.1861,276.5235,128.427C278.9154,126.5371,281.4613,124.84870000000001,284.135,123.3793C287.1495,121.7008,290.3337,120.3433,293.6346,119.32939999999999C295.3935,118.783,297.181,118.3323,298.9892,117.9794L300.3905,117.7153L301.7033,117.5392L304.4322,117.1871C308.061,116.7615,311.7192,116.3947,315.3479,116.1453C322.6053,115.5877,329.7448,115.20609999999999,336.3089,114.2083C339.5836,113.6801,342.7403,113.1518,345.6758,112.3888C348.6112,111.62583000000001,351.3696,110.83345,353.8625,109.90901C356.35540000000003,108.98457,358.6123,108.0308,360.6037,107.10636C362.5951,106.18192,364.277,105.28686,365.663,104.50916C367.05,103.73147,368.141,103.12985,368.86400000000003,102.67497L370,102L369.1,102.95377C368.495,103.57008,367.625,104.42112,366.401,105.55097C363.0532,108.5485,359.3592,111.13947,355.39660000000003,113.2693C352.7319,114.7183,349.9606,115.9642,347.1066,116.9963C343.9328,118.1683,340.6795,119.1147,337.371,119.8283C333.93399999999997,120.562,330.3791,121.17830000000001,326.7504,121.6478C323.1217,122.1173,319.4782,122.4108,315.8494,122.6896C312.2207,122.9684,308.6363,123.1885,305.1403,123.5407C301.80899999999997,123.8278,298.4986,124.3178,295.2277,125.008C292.2114,125.7765,289.2537,126.7572,286.3772,127.9427C283.7671,129.05270000000002,281.21659999999997,130.2968,278.7362,131.6698C274.9735,133.8087,271.36548,136.2063,267.93853,138.8452Q265.31283,140.7821,264,142Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.10000000149011612;">
                    <g>
                        <path
                            d="M259,133L259.766947,131.9454C260.28317,131.2717,261.02061,130.2464,262.09727,129.0161C263.35971,127.5556,264.6988,126.1623,266.10896,124.8418C267.9304,123.1054,269.8615,121.4861,271.8905,119.9938C274.2974,118.1979,276.842,116.5917,279.5009,115.1897C282.492,113.6052,285.6342,112.3198,288.8812,111.3523C292.341,110.3414,295.8851,109.6405,299.47090000000003,109.2578C303.11379999999997,108.8623,306.7715,108.5255,310.385,108.2325C313.9985,107.9396,317.6267,107.7052,321.1517,107.383C324.6766,107.0608,328.1426,106.6946,331.4759,106.1088C333.1277,105.8598,334.7354,105.5375,336.3135,105.2007C337.8916,104.8638,339.4108,104.5123,340.8856,104.1022C342.3605,103.6921,343.8354,103.2673,345.1628,102.82794C346.4957,102.40907,347.8053,101.92016,349.086,101.36327C351.6081,100.44052,353.79089999999997,99.29809,355.7967,98.43394C357.8026,97.56979,359.40999999999997,96.47129,360.767,95.6657L365,93L361.283,96.36871C360.044,97.39399,358.481,98.55105,356.6521,99.86928C354.5611,101.27222,352.3839,102.54406,350.1332,103.6774C348.8616,104.37,347.5518,104.9911,346.21,105.5375C344.794,106.1668,343.34680000000003,106.72409999999999,341.8738,107.2073C340.3989,107.7345,338.806,108.2618,337.1837,108.6719C335.5613,109.082,333.8947,109.5214,332.19849999999997,109.873C325.1237,111.2447,317.9643,112.145,310.7685,112.56790000000001C307.1402,112.81700000000001,303.5415,113.0952,300.046,113.43209999999999C296.6522,113.7089,293.2846,114.2426,289.9726,115.0286C286.89639999999997,115.8096,283.8879,116.83330000000001,280.9758,118.0897C278.3583,119.2587,275.8122,120.5792,273.3506,122.04429999999999C269.6194,124.273,266.07772,126.8006,262.76094,129.602Q260.31265,131.7111,259,133Z"
                            fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g>
                    <path
                        d="M236,142Q234.628,140.7821,232.119,138.8452C228.68259999999998,136.2102,225.0684,133.8127,221.3019,131.6698C218.822,130.2939,216.2704,129.0497,213.6578,127.9427C210.78,126.7572,207.8212,125.7765,204.80360000000002,125.008C201.53640000000001,124.3168,198.2294,123.82679999999999,194.9016,123.5407C191.4041,123.1885,187.8034,122.98310000000001,184.1732,122.6896C180.543,122.3962,176.8832,122.088,173.2677,121.6478C169.6522,121.2076,166.08100000000002,120.562,162.6574,119.8283C159.3432,119.1126,156.0837,118.1664,152.903,116.9963C150.0512,115.9674,147.2834,114.7214,144.6243,113.2693C140.6534,111.1427,136.95261,108.55155,133.60073,105.55097C132.39065,104.47978,131.50523,103.57003,130.914942,102.95377L130,102L131.13629,102.67497C131.85938,103.12985,132.96616,103.73147,134.35333,104.50916C135.7405,105.28686,137.45231,106.15259,139.415,107.10636C141.3777,108.06013,143.665,108.96988,146.1442,109.90901C148.6234,110.84813,151.4125,111.62583000000001,154.3491,112.3888C157.2858,113.1518,160.4438,113.6801,163.7199,114.2083C170.272,115.20609999999999,177.4144,115.5877,184.6749,116.1453C188.3199,116.3947,191.9796,116.7616,195.60989999999998,117.1871L198.3252,117.5392L199.6533,117.7153L201.0552,117.9794C202.85930000000002,118.3323,204.64260000000002,118.783,206.3973,119.32939999999999C209.6995,120.3433,212.885,121.7008,215.9008,123.3793C218.57479999999998,124.8424,221.1174,126.5312,223.5008,128.427C227.05970000000002,131.1866,230.279,134.3534,233.09300000000002,137.8621C234.15800000000002,139.172,235.13,140.5547,236,142Z"
                        fill="#FBBFAB" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.10000000149011612;">
                    <g>
                        <path
                            d="M241,133Q239.704,131.7087,237.262,129.66910000000001C233.9121,126.8626,230.3355,124.3304,226.5675,122.0976C224.078,120.627,221.5017,119.304,218.8523,118.1357C215.91660000000002,116.877,212.8834,115.8515,209.7818,115.069C206.4321,114.2815,203.0263,113.74680000000001,199.5941,113.4696C196.0642,113.13210000000001,192.4449,112.8533,188.766,112.6038C181.5042,112.1801,174.2792,111.2781,167.1396,109.9039C165.4119,109.5518,163.7289,109.1849,162.1054,108.7007C160.4819,108.2164,158.8882,107.7615,157.369,107.2333C155.88060000000002,106.7519,154.4189,106.1935,152.9901,105.5605C151.6338,105.016,150.3109,104.3937,149.0283,103.697C146.7506,102.56143,144.547,101.28726,142.43012,99.88188C140.59813,98.56127000000001,139.01934,97.40205,137.76823,96.37489L134,93L138.27458000000001,95.7293C139.65974,96.53632,141.38746,97.41674,143.30882,98.50261C145.2302,99.58848,147.5388,100.54225,150.0857,101.43731C151.378,101.99769,152.7006,102.48754,154.0476,102.90466C155.4327,103.3449,156.8477,103.8438,158.352,104.1813C159.8563,104.5187,161.3308,104.9883,162.9841,105.2818C164.6373,105.5753,166.18630000000002,105.9421,167.86939999999998,106.1915C171.1908,106.7784,174.6909,107.0719,178.2953,107.46809999999999C181.8997,107.8643,185.5041,108.0697,189.1681,108.3191C192.8321,108.5686,196.5259,108.9501,200.1898,109.3463C203.81119999999999,109.7282,207.3903,110.4305,210.88389999999998,111.44460000000001C214.1686,112.4113,217.34699999999998,113.6992,220.3715,115.2891C223.0521,116.6935,225.6168,118.3027,228.042,120.102C230.0956,121.5972,232.0506,123.21940000000001,233.8954,124.9589C235.534,126.4996,236.874,127.89359999999999,237.947,129.1409C239.019,130.3881,239.764,131.35660000000001,240.3,132.0756L241,133Z"
                            fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.10000000149011612;">
                    <g>
                        <path
                            d="M235.11024,329Q232.07349,346.175,226,355L276,355Q267.5123,345.1528,266.0699,338.68879C261.2414,345.1528,237.16,347.1827,235.11024,329Z"
                            fill="#E78B7D" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.20000000298023224;">
                    <g>
                        <path
                            d="M393.90267529296875,356.3508Q399.54667529296876,338.6671,404.67267529296873,321.0571C409.76867529296874,304.3313,411.27967529296876,280.7383,404.12467529296873,262.80409L401.9616752929687,258Q394.9686752929688,343.928,277.35467529296875,325.3012C277.50190529296873,338.2032,282.72438529296875,350.53430000000003,291.9027752929687,359.652C312.53967529296875,366.666,351.68027529296876,380.504,393.90267529296875,356.3508Z"
                            fill="#E78B7D" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M123.41470000000001,332.949403553009Q90.27406,317.785603553009,89.340739,263.48190355300903C89.120165,265.52300355300906,89.00643692,267.574083553009,89,269.62696355300903C89,284.024503553009,90.09629,301.07450355300904,93.94813,314.941403553009Q99.1777,334.77670355300904,104.9555,354.62660355300903C147.9182,381.638903553009,187.69580000000002,366.415903553009,208.911,358.48750355300905C220.866,342.277503553009,224.659,323.20850355300905,226.333,312.80460355300903C202.525,358.06020355300905,140.74790000000002,340.848103553009,123.41470000000001,332.949403553009Z"
                            fill="#E78B7D" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g style="opacity:0.10000000149011612;">
                    <path
                        d="M294.4377,136.02348C296.4281,139.35953,292.9741,145.6364,286.6077,150.0318C280.24118,154.4272,273.55274,155.3126,271.5623,151.9765C269.57186,148.6405,273.02586,142.36361,279.39234,137.96822C285.7588,133.572838,292.4473,132.68744,294.4377,136.02348Z"
                        fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.05000000074505806;">
                    <ellipse cx="59" cy="315.5" rx="14" ry="48.5" fill="#FFFFFF" fill-opacity="1"
                        style="mix-blend-mode:passthrough" />
                </g>
                <g style="opacity:0.05000000074505806;">
                    <ellipse cx="445.5" cy="315.5" rx="13.5" ry="48.5" fill="#FFFFFF" fill-opacity="1"
                        style="mix-blend-mode:passthrough" />
                </g>
                <g @click="clickBody('右侧乳房')">
                    <ellipse cx="171.5" cy="278.5" rx="75.5" ry="75.5" fill="#F25555"
                        :fill-opacity="curr == '右侧乳房' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g @click="clickBody('左侧乳房')">
                    <ellipse cx="331" cy="278.5" rx="75" ry="75.5" fill="#F25555"
                        :fill-opacity="curr == '左侧乳房' ? '0.20000000298023224' : '0.07999999821186066'" />
                </g>
                <g transform="matrix(-1,0,0,1,382,0)">
                    <g>
                        <ellipse cx="198.0346817970276" cy="234" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g transform="matrix(-1,0,0,1,654,0)">
                        <g>
                            <rect x="327" y="144" width="63" height="29" rx="6" fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M336.586,152.184C336.418,153.024,336.208,153.864,335.928,154.704L331.854,154.704L331.854,155.992L335.466,155.992C334.584,158.13400000000001,333.282,160.094,331.378,161.39600000000002C331.658,161.662,332.064,162.152,332.26,162.46C333.198,161.788,333.996,160.99,334.682,160.094L334.682,165.19L336.012,165.19L336.012,164.406L341.794,164.406L341.794,165.12L343.194,165.12L343.194,158.512L335.718,158.512C336.18,157.714,336.558,156.86,336.894,155.992L344.174,155.992L344.174,154.704L337.342,154.704C337.58,153.962,337.79,153.192,337.972,152.436L336.586,152.184ZM336.012,163.132L336.012,159.786L341.794,159.786L341.794,163.132L336.012,163.132ZM351.65,162.698C352.308,163.426,353.092,164.434,353.428,165.078L354.254,164.462C353.904,163.86,353.092,162.894,352.434,162.18L351.65,162.698ZM348.99,153.038L348.99,161.956L350.026,161.956L350.026,154.018L352.84,154.018L352.84,161.9L353.932,161.9L353.932,153.038L348.99,153.038ZM356.886,152.324L356.886,163.748C356.886,163.958,356.816,164.014,356.634,164.014C356.452,164.014,355.892,164.014,355.262,164C355.402,164.336,355.556,164.84,355.598,165.13400000000001C356.522,165.13400000000001,357.11,165.106,357.488,164.91C357.852,164.728,357.992,164.392,357.992,163.748L357.992,152.324L356.886,152.324ZM354.856,153.514L354.856,161.998L355.892,161.998L355.892,153.514L354.856,153.514ZM350.936,154.844L350.936,159.8C350.936,161.452,350.698,163.23,348.584,164.42000000000002C348.78,164.574,349.13,164.98,349.242,165.19C351.566,163.888,351.916,161.69,351.916,159.814L351.916,154.844L350.936,154.844ZM347.562,152.184C347.072,154.284,346.288,156.38400000000001,345.336,157.784C345.546,158.092,345.868,158.778,345.98,159.058C346.274,158.624,346.554,158.148,346.82,157.61599999999999L346.82,165.13400000000001L347.898,165.13400000000001L347.898,155.096C348.206,154.228,348.472,153.346,348.696,152.478L347.562,152.184ZM367.70799999999997,152.534L367.70799999999997,162.81C367.70799999999997,164.35,368.044,164.812,369.304,164.812C369.54200000000003,164.812,370.634,164.812,370.872,164.812C372.09000000000003,164.812,372.384,163.972,372.51,161.606C372.15999999999997,161.522,371.628,161.27,371.334,161.018C371.278,163.10399999999998,371.20799999999997,163.636,370.774,163.636C370.536,163.636,369.696,163.636,369.514,163.636C369.094,163.636,369.024,163.538,369.024,162.824L369.024,152.534L367.70799999999997,152.534ZM366.28,152.156C364.74,152.59,362.108,152.898,359.84,153.038C359.98,153.318,360.148,153.808,360.19,154.11599999999999C362.5,154.004,365.244,153.738,367.134,153.22L366.28,152.156ZM362.36,154.452C362.64,155.208,362.962,156.216,363.088,156.86L364.20799999999997,156.454C364.068,155.824,363.73199999999997,154.858,363.438,154.13L362.36,154.452ZM365.72,153.752C365.44,154.62,364.894,155.852,364.46,156.608L365.51,157.014C365.95799999999997,156.3,366.504,155.194,366.98,154.214L365.72,153.752ZM360.19,154.774C360.554,155.516,360.988,156.496,361.17,157.14L360.33,157.14L360.33,158.274L364.502,158.274C363.998,158.834,363.382,159.422,362.836,159.814L362.836,160.612L359.588,160.878L359.728,162.124L362.836,161.816L362.836,163.79C362.836,163.958,362.78,163.986,362.584,164C362.402,164.014,361.716,164.014,361.072,163.986C361.24,164.322,361.422,164.826,361.478,165.162C362.444,165.162,363.074,165.148,363.536,164.966C363.998,164.784,364.11,164.448,364.11,163.818L364.11,161.69L366.896,161.41L366.896,160.22L364.11,160.486L364.11,160.206C365.006,159.506,365.972,158.582,366.672,157.756L365.804,157.07L365.524,157.14L361.198,157.14L362.304,156.678C362.094,156.062,361.66,155.096,361.254,154.368L360.19,154.774ZM379.146,152.506C379.286,152.814,379.426,153.178,379.552,153.528L374.79200000000003,153.528L374.79200000000003,156.804C374.79200000000003,159.03,374.666,162.306,373.392,164.574C373.742,164.7,374.344,165.008,374.61,165.204C375.884,162.86599999999999,376.108,159.408,376.122,157.028L381.106,157.028L380.04200000000003,157.392C380.28,157.812,380.574,158.386,380.742,158.792L376.528,158.792L376.528,159.87L378.978,159.87C378.76800000000003,161.844,378.236,163.328,375.884,164.154C376.15,164.378,376.5,164.85399999999998,376.64,165.148C378.488,164.448,379.384,163.38400000000001,379.86,161.998L383.724,161.998C383.612,163.188,383.45799999999997,163.72,383.262,163.888C383.136,164,382.996,164.014,382.744,164.014C382.464,164.014,381.722,164,380.98,163.93C381.162,164.238,381.31600000000003,164.686,381.33,165.008C382.128,165.05,382.898,165.064,383.29,165.036C383.752,164.994,384.074,164.91,384.354,164.644C384.73199999999997,164.28,384.914,163.426,385.082,161.466C385.11,161.298,385.124,160.962,385.124,160.962L380.126,160.962C380.196,160.612,380.238,160.248,380.28,159.87L385.978,159.87L385.978,158.792L381.134,158.792L382.002,158.47C381.834,158.092,381.512,157.49,381.204,157.028L385.558,157.028L385.558,153.528L381.008,153.528C380.854,153.094,380.644,152.59,380.448,152.17L379.146,152.506ZM376.122,154.648L384.242,154.648L384.242,155.908L376.122,155.908L376.122,154.648Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-58.37620495186957,75.29850043943134)">
                        <path
                            d="M303.53700000000003,208.83964167285157L191.115501,235.53126467285156L190.884499,234.55836467285155L303.306,207.86668767285155L303.53700000000003,208.83964167285157Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
                <g>
                    <g>
                        <ellipse cx="330.0346817970276" cy="234" rx="3" ry="3" fill="#557AF2" fill-opacity="1" />
                    </g>
                    <g>
                        <g>
                            <rect x="396" y="144" width="63" height="29" rx="6" fill="#557AF2" fill-opacity="1" />
                        </g>
                        <g>
                            <path
                                d="M405.068,152.184C404.942,152.982,404.802,153.808,404.62,154.63400000000001L400.896,154.63400000000001L400.896,155.908L404.326,155.908C403.57,158.778,402.366,161.536,400.336,163.342C400.602,163.594,401.008,164.084,401.218,164.392C402.856,162.894,403.99,160.906,404.816,158.722L404.816,159.64600000000002L407.784,159.64600000000002L407.784,163.538L403.332,163.538L403.332,164.812L413.342,164.812L413.342,163.538L409.142,163.538L409.142,159.64600000000002L412.76800000000003,159.64600000000002L412.76800000000003,158.372L404.942,158.372C405.236,157.574,405.474,156.748,405.698,155.908L413.104,155.908L413.104,154.63400000000001L406.006,154.63400000000001C406.16,153.878,406.314,153.122,406.44,152.36599999999999L405.068,152.184ZM420.65,162.698C421.308,163.426,422.092,164.434,422.428,165.078L423.254,164.462C422.904,163.86,422.092,162.894,421.434,162.18L420.65,162.698ZM417.99,153.038L417.99,161.956L419.026,161.956L419.026,154.018L421.84,154.018L421.84,161.9L422.932,161.9L422.932,153.038L417.99,153.038ZM425.886,152.324L425.886,163.748C425.886,163.958,425.816,164.014,425.634,164.014C425.452,164.014,424.892,164.014,424.262,164C424.402,164.336,424.556,164.84,424.598,165.13400000000001C425.522,165.13400000000001,426.11,165.106,426.488,164.91C426.852,164.728,426.992,164.392,426.992,163.748L426.992,152.324L425.886,152.324ZM423.856,153.514L423.856,161.998L424.892,161.998L424.892,153.514L423.856,153.514ZM419.936,154.844L419.936,159.8C419.936,161.452,419.698,163.23,417.584,164.42000000000002C417.78,164.574,418.13,164.98,418.242,165.19C420.566,163.888,420.916,161.69,420.916,159.814L420.916,154.844L419.936,154.844ZM416.562,152.184C416.072,154.284,415.288,156.38400000000001,414.336,157.784C414.546,158.092,414.868,158.778,414.98,159.058C415.274,158.624,415.554,158.148,415.82,157.61599999999999L415.82,165.13400000000001L416.898,165.13400000000001L416.898,155.096C417.206,154.228,417.472,153.346,417.696,152.478L416.562,152.184ZM436.70799999999997,152.534L436.70799999999997,162.81C436.70799999999997,164.35,437.044,164.812,438.304,164.812C438.54200000000003,164.812,439.634,164.812,439.872,164.812C441.09000000000003,164.812,441.384,163.972,441.51,161.606C441.15999999999997,161.522,440.628,161.27,440.334,161.018C440.278,163.10399999999998,440.20799999999997,163.636,439.774,163.636C439.536,163.636,438.696,163.636,438.514,163.636C438.094,163.636,438.024,163.538,438.024,162.824L438.024,152.534L436.70799999999997,152.534ZM435.28,152.156C433.74,152.59,431.108,152.898,428.84,153.038C428.98,153.318,429.148,153.808,429.19,154.11599999999999C431.5,154.004,434.244,153.738,436.134,153.22L435.28,152.156ZM431.36,154.452C431.64,155.208,431.962,156.216,432.088,156.86L433.20799999999997,156.454C433.068,155.824,432.73199999999997,154.858,432.438,154.13L431.36,154.452ZM434.72,153.752C434.44,154.62,433.894,155.852,433.46,156.608L434.51,157.014C434.95799999999997,156.3,435.504,155.194,435.98,154.214L434.72,153.752ZM429.19,154.774C429.554,155.516,429.988,156.496,430.17,157.14L429.33,157.14L429.33,158.274L433.502,158.274C432.998,158.834,432.382,159.422,431.836,159.814L431.836,160.612L428.588,160.878L428.728,162.124L431.836,161.816L431.836,163.79C431.836,163.958,431.78,163.986,431.584,164C431.402,164.014,430.716,164.014,430.072,163.986C430.24,164.322,430.422,164.826,430.478,165.162C431.444,165.162,432.074,165.148,432.536,164.966C432.998,164.784,433.11,164.448,433.11,163.818L433.11,161.69L435.896,161.41L435.896,160.22L433.11,160.486L433.11,160.206C434.006,159.506,434.972,158.582,435.672,157.756L434.804,157.07L434.524,157.14L430.198,157.14L431.304,156.678C431.094,156.062,430.66,155.096,430.254,154.368L429.19,154.774ZM448.146,152.506C448.286,152.814,448.426,153.178,448.552,153.528L443.79200000000003,153.528L443.79200000000003,156.804C443.79200000000003,159.03,443.666,162.306,442.392,164.574C442.742,164.7,443.344,165.008,443.61,165.204C444.884,162.86599999999999,445.108,159.408,445.122,157.028L450.106,157.028L449.04200000000003,157.392C449.28,157.812,449.574,158.386,449.742,158.792L445.528,158.792L445.528,159.87L447.978,159.87C447.76800000000003,161.844,447.236,163.328,444.884,164.154C445.15,164.378,445.5,164.85399999999998,445.64,165.148C447.488,164.448,448.384,163.38400000000001,448.86,161.998L452.724,161.998C452.612,163.188,452.45799999999997,163.72,452.262,163.888C452.136,164,451.996,164.014,451.744,164.014C451.464,164.014,450.722,164,449.98,163.93C450.162,164.238,450.31600000000003,164.686,450.33,165.008C451.128,165.05,451.898,165.064,452.29,165.036C452.752,164.994,453.074,164.91,453.354,164.644C453.73199999999997,164.28,453.914,163.426,454.082,161.466C454.11,161.298,454.124,160.962,454.124,160.962L449.126,160.962C449.196,160.612,449.238,160.248,449.28,159.87L454.978,159.87L454.978,158.792L450.134,158.792L451.002,158.47C450.834,158.092,450.512,157.49,450.204,157.028L454.558,157.028L454.558,153.528L450.008,153.528C449.854,153.094,449.644,152.59,449.448,152.17L448.146,152.506ZM445.122,154.648L453.242,154.648L453.242,155.908L445.122,155.908L445.122,154.648Z"
                                fill="#FFFFFF" fill-opacity="1" />
                        </g>
                    </g>
                    <g
                        transform="matrix(0.9431880116462708,-0.3322593867778778,0.3322593867778778,0.9431880116462708,-50.87702248917731,119.1567394941112)">
                        <path
                            d="M435.53700000000003,208.83964167285157L323.115501,235.53126467285156L322.884499,234.55836467285155L435.306,207.86668767285155L435.53700000000003,208.83964167285157Z"
                            fill-rule="evenodd" fill="#557AF2" fill-opacity="1" />
                    </g>
                </g>
            </g>
        </svg>
    </view>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"
const props = defineProps({
    width: {
        type: String,
        default: ''
    },
    height: {
        type: String,
        default: ''
    },
})
const emit = defineEmits(['selectBody'])
const curr = ref('')
const clickBody = (body) => {
    curr.value = body
    emit('selectBody', body)
}
onMounted(async () => {
    nextTick(() => {
        uni.pageScrollTo({
            selector: '.page-bottom',
            duration: 20
        })
    })
})
</script>
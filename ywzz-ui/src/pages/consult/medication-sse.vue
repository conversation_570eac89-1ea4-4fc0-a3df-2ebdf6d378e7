<template>
	<view class="consult-content">
		<div class="consult-content__chat-list">
			<template v-for="(item, index) in chatContent" :key="index">
				<Question v-if="item.type === 'question'" :content="item.content" />
				<!-- 智能问药组件 -->
				<responseDrag v-else-if="item.type === 'responseRag'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<!-- 智能问药 提示语-->
				<InfoSuggest v-else-if="item.type === 'znwy'">
				</InfoSuggest>
			</template>
		</div>
	</view>
	<ChatInput ref="chatInput" @chat="handleChat" :tab="2" :question="question" :type="reportId ? 2 : 1" />
	<uv-toast ref="toast"></uv-toast>
	<Popup />
	<div class="page-bottom" />
</template>

<script lang="ts" setup>
import { nextTick, ref, onUnmounted } from "vue"
import Report from './components/report.vue'
import Question from './components/question.vue'
import ResponseLlm from './components/responseLlm.vue'
import ResponseDrag from './components/responseDrag.vue'
import InfoSuggest from './components/infoSuggest.vue'
import AdviceSuggest from './components/adviceSuggest.vue'

import ChatInput from './components/chatInput.vue'
import Popup from './components/popup.vue'
import { sendConsult, sendMessage } from '@/api/consult'
import { onLoad } from "@dcloudio/uni-app"


import { SSEUtil } from '@/utils/request/sse-util'

const messages: any = ref([])
let sseClient: any = null

const startChat = () => {
	// sseClient = new SSEUtil('http://************:10011/ywzz/api/consult/answer', {
	sseClient = new SSEUtil('http://************:10011/ywzz/api/sse/connect/003', {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			// 'Authorization': 'Bearer your_token'
		},
		// body: { // POST 请求体
		// 	type: 'question',
		// 	content: '头疼挂什么科'
		// },
		// params: { // GET 参数（当使用 GET 方法时）
		// 	topicId: 123
		// }
	})

	// 监听消息事件
	sseClient.addEventListener('message', (event) => {
		// messages.value += event
		messages.value.push(event?.data)
		console.log('messages:', event)
		// messages.value += event.data.consultInfo.recommendations
	})

	// 监听自定义事件
	sseClient.addEventListener('status', (event) => {
		// console.log('状态更新:', event.data.consultInfo.recommendations)
		console.log('状态更新:', event)
	})

	// 错误处理
	sseClient.addEventListener('error', (err) => {
		console.error('SSE错误:', err)
	})

	// 连接关闭
	sseClient.addEventListener('close', () => {
		console.log('连接已关闭')
	})

	sseClient.connect()
}

// startChat();

onUnmounted(() => {
	sseClient?.close()
})









type ChatContentType = {
	type: 'suggest' | 'report' | 'question' | 'responseLlm' | 'responseRag',
	content?: string,
	recordId?: string,
	departments?: any[],
	message?: string,
	knowledgeBaseList?: any[],
}
const requestId = ref('')
const reportId = ref('')
onLoad((options) => {
	reportId.value = options?.reportId
	if (reportId.value) {
		chatContent.value.push({
			type: 'report',
		})
	}
	if (options?.type == 'znwy') {
		chatContent.value.push({
			type: 'znwy',
		})
	} else {
		if (options?.content && options?.type) {
			handleChat({
				type: options?.type,
				content: options?.content,
				templateId: options?.templateId
			})
		}
	}
});
const chatContent = ref<ChatContentType[]>([])
const chatInput = ref()

// 推荐问题
const handleSuggest = (message, templateId = null) => {
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	chatInput.value.handleInput(message, templateId)
}
const question = ref(false)
const toast = ref()

const dealResult = () => {
	const response: any = ref<ChatContentType>({
		type: 'responseRag',
		knowledgeBaseList: []
	})
	const start = (item) => {
		if (item.type === 'btn') {
			response.value.knowledgeBaseList.push(item)
			const shift = messages.value.shift()
			if (shift) {
				start(shift)
			} else {
				question.value = false
			}
			nextTick(() => {
				uni.pageScrollTo({
					selector: '.page-bottom',
					duration: 20
				})
			})
		} else {
			response.value.knowledgeBaseList.push({
				type: item.type,
				content: "",
				url: item.url,
				newLineFlag: item.newLineFlag
			})
			const target = response.value.knowledgeBaseList[response.value.knowledgeBaseList.length - 1]
			typewriter(target, item.content?.split(''), () => {
				const shift = messages.value.shift()
				if (shift) {
					start(shift)
				} else {
					question.value = false
				}
				uni.pageScrollTo({
					selector: '.page-bottom',
					duration: 20
				})
			})
		}
	}
	const shift = messages.value.shift()
	shift && start(shift)
	chatContent.value.push(response.value)
}

// 问答
const handleChat = async (content) => {
	if (content.type == 'znwy') {
		chatContent.value.push({
			type: content.type,
		})
		return;
	}
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	try {
		uni.pageScrollTo({
			selector: '.page-bottom',
			duration: 20
		})
		requestId.value = generate16CharUUID()
		question.value = true
		chatContent.value.push(content)
		let param = { message: content, requestId: requestId.value }
		// const res = await sendMessage(param)
		const res = await sendConsult(content)
		// if (res.type === "llm") {
		// 	const response = ref<ChatContentType>({
		// 		type: 'responseLlm',
		// 		recordId: res.recordId,
		// 		content: ''
		// 	})
		// 	chatContent.value.push(response.value)
		// 	typewriter(response.value, res.consultInfo?.recommendations?.split(''), () => {
		// 		if (!res?.consultInfo?.departments?.length && !res?.consultInfo?.deptFlag) {
		// 			response.value.message = res?.consultInfo?.message || ''
		// 		} else {
		// 			response.value.departments = res?.consultInfo?.departments || []
		// 		}
		// 		question.value = false
		// 		nextTick(() => {
		// 			uni.pageScrollTo({
		// 				selector: '.page-bottom',
		// 				duration: 20
		// 			})
		// 		})
		// 	})
		// } else if (res.type === "rag") {
		// 	const response = ref<ChatContentType>({
		// 		type: 'responseRag',
		// 		recordId: res.recordId,
		// 		knowledgeBaseList: []
		// 	})
		// 	const start = (item) => {
		// 		if (item.type === 'btn') {
		// 			response.value.knowledgeBaseList.push(item)
		// 			const shift = res.knowledgeBaseList.shift()
		// 			if (shift) {
		// 				start(shift)
		// 			} else {
		// 				question.value = false
		// 			}
		// 			nextTick(() => {
		// 				uni.pageScrollTo({
		// 					selector: '.page-bottom',
		// 					duration: 20
		// 				})
		// 			})
		// 		} else {
		// 			response.value.knowledgeBaseList.push({
		// 				type: item.type,
		// 				content: "",
		// 				url: item.url,
		// 				newLineFlag: item.newLineFlag
		// 			})
		// 			const target = response.value.knowledgeBaseList[response.value.knowledgeBaseList.length - 1]
		// 			typewriter(target, item.content?.split(''), () => {
		// 				const shift = res.knowledgeBaseList.shift()
		// 				if (shift) {
		// 					start(shift)
		// 				} else {
		// 					question.value = false
		// 				}
		// 				uni.pageScrollTo({
		// 					selector: '.page-bottom',
		// 					duration: 20
		// 				})
		// 			})
		// 		}
		// 	}
		// 	const shift = res.knowledgeBaseList.shift()
		// 	shift && start(shift)
		// 	chatContent.value.push(response.value)
		// }

	} catch (e) {
		question.value = false
	}
}
function generate16CharUUID() {
	// 创建一个包含所有十六进制字符的数组
	const hexChars = '0123456789ABCDEF';
	let uuid = '';

	// 生成16个十六进制字符
	for (let i = 0; i < 16; i++) {
		const randomIndex = Math.floor(Math.random() * hexChars.length);
		uuid += hexChars[randomIndex];
	}
	return uuid;
}
// 页面往下滑倒 - 节流
const handleThrottleFn = throttle(() => {
	uni.pageScrollTo({
		selector: '.page-bottom',
		duration: 20
	})
}, 800);

// 文字逐字显示
const typewriter = (target, sourceArr, fn) => {
	console.log('target', target);
	console.log('sourceArr', sourceArr);

	if (sourceArr.length) {
		target.content += sourceArr.shift()
		handleThrottleFn()
		requestAnimationFrame(() => {
			typewriter(target, sourceArr, fn)
		})
		// setTimeout(()=>{typewriter(target, sourceArr,fn)}, 40)
	} else {
		fn()
	}
}
function throttle(fn, delay) {
	let valid = true
	return function () {
		if (!valid) return false
		valid = false
		fn()
		setTimeout(() => {
			valid = true;
		}, delay)
	}
}
</script>

<style lang="scss" scoped>
.consult-content {
	padding-bottom: 400rpx;
}

.page-bottom {
	// margin-top:150rpx;
	height: 2rpx;
}
</style>
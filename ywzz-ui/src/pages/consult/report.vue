<template>
	<view class="consult-content">
		<div class="consult-content__chat-list">
			<template v-for="(item, index) in chatContent" :key="index">
				<!-- 报告解读 -->
				<Report v-if="item.type === 'report'" :reportId="reportId" @check="handleSuggest" />
				<Question v-if="item.type === 'question'" :content="item.content" />
				<!-- 智慧分诊组件 -->
				<ResponseLlm v-else-if="item.type === 'responseLlm'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<ResponseRag v-else-if="item.type === 'responseRag'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<!-- 智能问药 提示语-->
				<ReportSuggest v-else-if="item.type === 'bgjd'" @check="handleSuggest" @chatRp="handleChatReport">
				</ReportSuggest>
				<!-- 报告解读中状态 -->
				 <Reporting v-else-if="item.type === 'reporting'" :reported="reported" />
				<!-- 报告解读 -->
				<ResponseReport v-else-if="item.type === 'responseReport'" :item="item"
					:question="question && index === chatContent.length - 1" />
			</template>
		</div>
	</view>
	<ChatInput ref="chatInput" @chat="handleChat" :tab="2" :question="question" :type="reportId ? 2 : 1" />
	<uv-toast ref="toast"></uv-toast>
	<Popup />
	<div class="page-bottom" />
</template>

<script lang="ts" setup>
import { nextTick, ref } from "vue"
import Report from './components/report.vue'
import Question from './components/question.vue'
import ResponseReport from './components/responseReport.vue'
import ResponseLlm from './components/responseLlm.vue'
import ResponseRag from './components/responseRag.vue'
import ReportSuggest from './components/reportSuggest.vue'
import Reporting from './components/reporting.vue'
import ChatInput from './components/chatInput.vue'
import Popup from './components/popup.vue'
import { sendConsult } from '@/api/consult'
import { onLoad } from "@dcloudio/uni-app"

type ChatContentType = {
	type: string,
	content?: string,
	recordId?: string,
	departments?: any[],
	message?: string,
	knowledgeBaseList?: any[],
}

const reportId = ref('')
onLoad((options) => {
	reportId.value = options?.reportId
	if (reportId.value) {
		chatContent.value.push({
			type: 'report',
		})
	}
	if (options?.type == 'znwy') {
		chatContent.value.push({
			type: 'znwy',
		})
	} else if (options?.type == 'bgjd') {
		chatContent.value.push({
			type: 'bgjd',
		})
	} else {
		if (options?.content && options?.type) {
			handleChat({
				type: options?.type,
				content: options?.content,
				templateId: options?.templateId
			})
		}
	}
});
const chatContent = ref<ChatContentType[]>([])
const chatInput = ref()

// 推荐问题
const handleSuggest = (message, templateId = null) => {
	chatInput.value.handleInput(message, templateId)
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
}
const question = ref(false)
const toast = ref()

const reported = ref(false)
//报告解读
const handleChatReport = async (res) => {
	// console.log('res.content', res.content);
	const response = ref<ChatContentType>({
		type: 'responseReport',
		recordId: '',
		content: ''
	})
	if(res.type == 'reporting'){
		chatContent.value.push(res)	
	}else {
		if (res.content) {
				//报告解读完成
				reported.value = true 
				// chatContent.value.pop();
				response.value.recordId = res.recordId
				chatContent.value.push(response.value)
				// response.value.content = res.content
				typewriter(response.value, res.content?.split(''), () => {
					question.value = false
					nextTick(() => {
						uni.pageScrollTo({
							selector: '.page-bottom',
							duration: 20
						})
					})
				})
			} else {
				chatContent.value.push(response.value)
			}
	}
	
	// 	response.value.content = ''
	// 	chatContent.value.push(response.value)
	// } else {

	// }




}
//流式输出测试
const handleChatReport1 = async (res) => {
	const response = ref<ChatContentType>({
		type: 'responseReport',
		recordId: res.recordId,
		content: ''
	})

	let content = ''
	let index = 0
	chatContent.value.push(response.value)
	console.log('res.content', res.content);

	let intervalId = setInterval(() => {
		// 获取当前索引对应的元素
		const currentItem = res.content[index].msg;
		// content += currentItem
		console.log('currentItem', currentItem);
		// console.log('content', content);
		index++;
		typewriter(response.value, currentItem?.split(''), () => {
			question.value = false
			nextTick(() => {
				uni.pageScrollTo({
					selector: '.page-bottom',
					duration: 20
				})
			})
		})
		if (index >= res.content.length) {
			clearInterval(intervalId)
		}
	}, 100);

}
// 问答
const handleChat = async (content) => {
	if (content.type == 'znwy') {
		chatContent.value.push({
			type: content.type,
		})
		return;
	}
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	try {
		uni.pageScrollTo({
			selector: '.page-bottom',
			duration: 20
		})
		question.value = true
		chatContent.value.push(content)
		if (!content.content.includes('.pdf')) {
			const res = await sendConsult(content);
			if (res.type === "llm") {
				const response = ref<ChatContentType>({
					type: 'responseLlm',
					recordId: res.recordId,
					content: ''
				})
				chatContent.value.push(response.value)
				typewriter(response.value, res.consultInfo?.recommendations?.split(''), () => {
					if (!res?.consultInfo?.departments?.length && !res?.consultInfo?.deptFlag) {
						response.value.message = res?.consultInfo?.message || ''
					} else {
						response.value.departments = res?.consultInfo?.departments || []
					}
					question.value = false
					nextTick(() => {
						uni.pageScrollTo({
							selector: '.page-bottom',
							duration: 20
						})
					})
				})
			} else if (res.type === "rag") {
				const response = ref<ChatContentType>({
					type: 'responseRag',
					recordId: res.recordId,
					knowledgeBaseList: []
				})
				const start = (item) => {
					if (item.type === 'btn') {
						response.value.knowledgeBaseList.push(item)
						const shift = res.knowledgeBaseList.shift()
						if (shift) {
							start(shift)
						} else {
							question.value = false
						}
						nextTick(() => {
							uni.pageScrollTo({
								selector: '.page-bottom',
								duration: 20
							})
						})
					} else {
						response.value.knowledgeBaseList.push({
							type: item.type,
							content: "",
							url: item.url,
							newLineFlag: item.newLineFlag
						})
						const target = response.value.knowledgeBaseList[response.value.knowledgeBaseList.length - 1]
						typewriter(target, item.content?.split(''), () => {
							const shift = res.knowledgeBaseList.shift()
							if (shift) {
								start(shift)
							} else {
								question.value = false
							}
							uni.pageScrollTo({
								selector: '.page-bottom',
								duration: 20
							})
						})
					}
				}
				const shift = res.knowledgeBaseList.shift()
				shift && start(shift)
				chatContent.value.push(response.value)
			}
		}


	} catch (e) {
		question.value = false
	}
}

// 页面往下滑倒 - 节流
const handleThrottleFn = throttle(() => {
	uni.pageScrollTo({
		selector: '.page-bottom',
		duration: 20
	})
}, 800);

// 文字逐字显示
const typewriter = (target, sourceArr, fn) => {
	if (sourceArr.length) {
		target.content += sourceArr.shift()
		handleThrottleFn()
		requestAnimationFrame(() => {
			typewriter(target, sourceArr, fn)
		})
		// setTimeout(()=>{typewriter(target, sourceArr,fn)}, 40)
	} else {
		fn()
	}
}
function throttle(fn, delay) {
	let valid = true
	return function () {
		if (!valid) return false
		valid = false
		fn()
		setTimeout(() => {
			valid = true;
		}, delay)
	}
}
</script>

<style lang="scss" scoped>
.consult-content {
	padding-bottom: 400rpx;
}

.page-bottom {
	// margin-top:150rpx;
	height: 2rpx;
}
</style>
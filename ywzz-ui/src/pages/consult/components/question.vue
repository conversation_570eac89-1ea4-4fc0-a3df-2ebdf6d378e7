<template>
	<view class="question-content">
		<div class="content" v-if="content.includes('.pdf')">
			<img class="pdf-icon" src="../../../static/pdf.png" alt="" />
			{{ content }}
		</div>

		<div class="content" v-else>{{ content }}</div>
	</view>
</template>

<script setup>
const porps = defineProps({
	content: {
		type: String,
		default: ''
	}
})
</script>

<style lang="less" scoped>
.question-content {
	display: flex;
	justify-content: flex-end;

	.pdf-icon {
		width: 40rpx;
		height: 45rpx;
		text-align: center;
		align-items: center;
		top: 5rpx;
		position: relative;

	}

	.pdf-content {
		max-width: 600rpx;
		margin-top: 24rpx;
		padding: 24rpx 24rpx 16rpx 24rpx;
		box-sizing: border-box;
		color: #FFFFFF;
		line-height: 44rpx;
		font-style: normal;
		text-transform: none;
		// background: linear-gradient( 90deg, #02C3C3 0%, #37D6AE 100%);
		background: #FFFFFF; //  linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
		box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(124, 154, 148, 0.2);
		border-radius: 40rpx 40rpx 0rpx 40rpx;
	}

	.content {
		max-width: 600rpx;
		margin-top: 24rpx;
		padding: 16rpx 44rpx;
		box-sizing: border-box;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 44rpx;
		font-style: normal;
		text-transform: none;
		// background: linear-gradient( 90deg, #02C3C3 0%, #37D6AE 100%);
		background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
		box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(124, 154, 148, 0.2);
		border-radius: 40rpx 40rpx 0rpx 40rpx;
	}
}
</style>

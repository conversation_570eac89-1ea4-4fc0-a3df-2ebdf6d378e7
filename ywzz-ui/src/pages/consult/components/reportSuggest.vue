<template>
	<view class="suggest-content">
		<div class="suggest-content__list">
			<div class="header">
				<img class="consultation_icon" src="../../../static/bgjd-icon1.png" alt="" />
				<div class="title">报告解读
				</div>
			</div>
			<div class="text">
				您可以选择已获取的体检报告，也可以选择上传新的体检报告进行解读。
				<div class="item" @click="openReports">
					<image class="item-icon" src="../../../static/advice-icon.png"></image>
					就诊人关联体检报告
					<div class="btn">查看</div>
				</div>
				<div class="item">
					<image class="item-icon" src="../../../static/pdf-icon.png"></image>
					上传PDF体检报告
					<uv-upload class="upload-btn" :fileList="[]" name="1" :maxCount="1" @afterRead="afterRead"
						accept="file">
						<div class="btn">上传</div>
					</uv-upload>
				</div>
			</div>
		</div>

	</view>
</template>
<script lang="ts" setup>
import { onMounted, ref, onUnmounted } from "vue"
import { getReportsDetail, uploadPdf, getReportsParse } from '@/api/report';
import { onUnload } from "@dcloudio/uni-app"

const emit = defineEmits(['check', 'chatRp'])
const props = defineProps({
	'reportId': {
		type: String,
		default: ''
	}
})
const report = ref({})
const openReports = () => {
	uni.navigateTo({
		url: "/pages/report/index"
	})
}
const fileList: any = ref([])

let intervalId: any = null
let timerId : any = null

onUnmounted(() => {
	console.log('移除timerId、intervalId')
	clearTimeout(timerId)
	clearInterval(intervalId)
});
const afterRead = (event) => {
	// file.type = "PDF"
	fileList.value = [event.file]
	const formData = new FormData();
	formData.append('file', event.file); // 文件字段
	let filePath = event.file.url
	uploadPdf(filePath).then(res => {
		if (res.code == 200) {
			emit('check', event.file.name)
			//先发送正在解析弹框
			emit('chatRp', { type: 'reporting' })
			getReportsParse({ parseId: res.data }).then(pres => {
				//有结果 可能是报告文件有误的提示信息
				if (pres.data && pres.data.parseResult) {
					const mockRes = {
						recordId: pres.data.recordId,
						content: pres.data.parseResult
						// content: pres.data
					}
					emit('chatRp', mockRes)
				} else {
					//上传后30s开始查询解析报告
					timerId = setTimeout(() => {
						//每5s轮询一次
						intervalId = setInterval(() => {
							console.log('5s 轮询intervalId');
							getReportsParse({ parseId: res.data }).then(pres => {
								if (pres.data && pres.data.parseResult) {
									const mockRes = {
										recordId: pres.data.recordId,
										content: pres.data.parseResult
										// content: pres.data
									}
									emit('chatRp', mockRes)
									clearInterval(intervalId)
								}
							}).catch(() => {
								clearInterval(intervalId)
							})
						}, 5000);
					}, 30000);
				}
			})
		}
	})
}
// onUnload(() => {
// 	console.log('onHide clearInterval');
// 	clearInterval(intervalId)
// });

onMounted(async () => {
	if (props.reportId) {
		const res = await getReportsDetail({ admId: props.reportId })
		report.value = res.data.summaryItemList
	}
})	
</script>

<style lang="less" scoped>
.suggest-content {
	padding-left: 12rpx;
	box-sizing: border-box;
	width: 620rpx;
	margin-top: 24rpx;

	&__list {
		width: 600rpx;
		// background: rgba(255, 255, 255, 0.36);
		// box-shadow: inset 2rpx 1 2rpx 0rpx #FFFFFF, inset 0rpx -1 2rpx 0rpx #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		position: relative;
		// padding: 0rpx 0 -24rpx;
		margin-top: 40rpx;
		// height: 144rpx;

		.text {
			top: -10px;
			width: 548rpx;
			position: relative;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			box-shadow: 0rpx -4rpx 10rpx 0rpx rgba(98, 124, 209, 0.2);
			padding: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;


		}

		.header {
			// padding: 10rpx;
			width: 100%;
			height: 135rpx;
			display: flex;
			align-items: center;
			background: linear-gradient(180deg, #D2E1FF 0%, #FFFFFF 85%);
			border: 2rpx solid #FFFFFF;
			// background: linear-gradient(90deg, #BBF4FF 48%, #D9FFA0 78%, #87EBD2 100%);
			border-radius: 20rpx 20rpx 5rpx 0rpx;
			// border-bottom: 1px;

			.title {
				margin: 0 0 10rpx 85rpx;
				padding: 0 20rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				font-size: 28rpx;
				line-height: 52rpx;
				color: #333333;

				span {
					color: #888888;
					font-size: 22rpx;
					font-family: Source Han Sans CN;
				}
			}

			.consultation_icon {
				position: absolute;
				top: 8rpx;
				left: 0;
				width: 88rpx;
				height: 88rpx;
				padding: 15rpx;
			}

			.ask {
				width: 336rpx;
				margin-left: 12rpx;
			}
		}

		.item {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			min-height: 74rpx;
			box-sizing: border-box;
			background: #ECF4FF;
			margin: 15rpx 0;
			border-radius: 18rpx;
			position: relative;

			.item-icon {
				width: 32rpx;
				height: 32rpx;
				margin: 0 15rpx;
			}

			.upload-btn {
				padding-bottom: 50rpx;
				// right: 20rpx;
				// position: absolute;
			}

			.btn {
				position: absolute;
				right: 40rpx;
				width: 124rpx;
				height: 48rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
				border: 2rpx solid #CDD7E6;
				text-align: center;
				// padding-top: 15rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.count {
				color: #557AF2;
				font-weight: 600;
				font-size: 26rpx;
				min-width: 32rpx;
				text-align: center;
				margin-left: 15rpx;
			}

			.content {
				margin-left: 20rpx;
				margin-right: 30rpx;
				flex: 1;
				font-size: 26rpx;
				color: #333333;
				box-sizing: border-box;
				// line-height: 70rpx;
				border-bottom: 2rpx solid #F8FDFD;
				display: flex;
				font-family: Source Han Sans, Source Han Sans;
				align-items: center;
				min-height: 74rpx;
				padding-top: 3rpx;

				span {
					flex: 1;
				}

				.right-arrow {
					width: 28rpx;
					height: 28rpx;
					text-align: center;
					border-radius: 50%;
					background-color: #FFFFFF;
				}
			}
		}

		.message {
			// width: 600rpx;
			// margin-top: 24rpx;
			// font-family: Source Han Sans, Source Han Sans;
			// font-weight: 400;
			// font-size: 24rpx;
			// color: #00C29D;
			// line-height: 32rpx;
			// background: rgba(87, 224, 198, 0.1);
			// border-radius: 12rpx 12rpx 12rpx 12rpx;
			// padding: 16rpx 28rpx;
			margin-top: 0rpx;
			padding-left: 10rpx;
			font-family: Source Han Sans,
				Source Han Sans;
			font-weight: 400;
			font-size: 24rpx;
			// color: #888888;
			color: #fb9242;
			line-height: 40rpx;
			display: flex;
		}
	}

}
</style>
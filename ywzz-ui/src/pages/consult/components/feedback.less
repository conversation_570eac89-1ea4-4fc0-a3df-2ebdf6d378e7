.thumb_class {
    width: 120rpx;
    display: flex;
    bottom: 6rpx;
    left: 15rpx;
    height: 48rpx;
    line-height: 46rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: <PERSON> <PERSON>, Source <PERSON>;
    font-weight: 500;
    font-size: 24rpx;
    color: #333333;
    // background: #E7F5F8;
    background: rgba(255, 255, 255, 0.5);
    // box-shadow: inset 2rpx 2rpx 2rpx 0rpx #FFFFFF, inset -2rpx -2rpx 2rpx 0rpx #FFFFFF;
    border-radius: 20rpx;
    border: 2rpx solid #FFFFFF;
    padding: 20rpx 5rpx 0 5rpx;
    box-sizing: border-box;
    position: relative;
    white-space: nowrap;

}

.thumb-icon {
    width: 25rpx;
    margin: 0 12rpx 17rpx 12rpx;
}

.thumb-icon-fill {
    width: 25rpx;
    margin: 0 12rpx 19rpx 12rpx;
}

.line {
    height: 100%;
    border-right: 1px solid #c5dee87d;
    border-left: 1px solid #c5dee87d;
    margin: 0 3rpx 18rpx 3rpx;
}

.option {
    padding: 10rpx;
    text-align: center;
    font-size: 26rpx;
    color: #333333;
    line-height: 38rpx;
    background: #EEF4F8;
    border-radius: 10rpx;
}

.sel_option {
    padding: 10rpx;
    text-align: center;
    font-size: 26rpx;
    line-height: 38rpx;
    border-radius: 10rpx;
    background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
    // background: linear-gradient(270deg, #02C3C3 58%, #37D6AE 100%);
    color: rgb(255, 255, 255);
}

.option-card {
    padding: 40rpx 30rpx;
    background-color: #ffffff;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
}

.other-card {
    padding: 20rpx;
    border-radius: 20rpx;
    background-color: #ffffff;
}

.feed-card {
    font-size: 14px;
    display: block;
    padding: 40rpx 35rpx 15rpx 35rpx;
    // background: linear-gradient(338deg, #EDF8FF 65%, #CBF4FC 77%, #BADEFB 86%, #55A0F8 100%);
    background-repeat: round;
    background-image: url('static/feedcard-new.png');
    border-radius: 30rpx 30rpx 0 0;
}

.text1 {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0 0 15rpx 0;
}

.text2 {
    margin: 0 0 25rpx 0;
    font-family: Source Han Sans, Source Han Sans;
    font-size: 24rpx;
    color: #666666;
    line-height: 34rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.other_text {
    font-family: Source Han Sans;
    // padding-bottom: 15rpx;
    font-weight: 500;
    font-size: 30rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
}


.close_icon {
    position: absolute;
    top: 5rpx;
    right: 10rpx;
}
<template>
	<view class="suggest-content">
		<div class="suggest-content__prompt">
			<div>
				<div class="title">自助健康咨询专家<div class="underline"></div>
				</div>

				<div class="desc">可以根据您的描述为您推荐就诊科室，帮您解读体检报告，回答您的问题，专注为您服务。</div>
			</div>
		</div>
		<div class="suggest-content__list">
			<div class="header">
				<img class="ask" src="../../../static/ask-new.png" alt="" />
			</div>
			<div class="scroll-wapper">
				<div class="scroll-list" style="margin-right: 120rpx;">
					<span class="item" v-for="item in suggestList" :key="item.templateId">
						<span @click="emit('check', item.content, item.templateId, item.pageName)">{{ item.content
						}}</span>
					</span>
				</div>
				<div class="scroll-list">
					<span class="item" v-for="item in suggestList" :key="item.templateId">
						<span @click="emit('check', item.content, item.templateId, item.pageName)">{{ item.content
						}}</span>
					</span>
				</div>
			</div>
			<!-- <uv-scroll-list>
				<view class="item" v-for="(item, index) in suggestList" :key="item.templateId">
					<span @click="emit('check', item.content, item.templateId)">{{ item.content }}</span>
				</view>
			</uv-scroll-list> -->
		</div>
		<!-- <video src="@/static/66.mp4" draggable="true" controls="controls" autoplay="autoplay" muted="muted" preload="auto" webkit-playsinline="true" playsinline="true" x5-video-player-type="h5" x5-video-player-fullscreen="true" x5-video-orientation="portraint" style="width: 100%"/> -->
		<div class="suggest-content__message">
			如您使用咨询功能，将代表您允许公众号服务使用您的相关体检信息。
		</div>
	</view>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue"
import { getQuestionnaire } from '@/api/consult.ts'

const emit = defineEmits(['check'])
const suggestList = ref([])
onMounted(async () => {
	const param = {}
	const res = await getQuestionnaire(param)
	suggestList.value = res
	// ?.slice(0, 3)
	// suggestList.value = [
	// 	{
	// 		"templateId": "1892033233302851586",
	// 		"content": "我发烧、咳嗽、去哪个科室？",
	// 		"seqNo": "1",
	// 		"pageName": "zhfz"
	// 	},
	// 	{
	// 		"templateId": "1892043464938360834",
	// 		"content": "体检报告中显示脂肪肝，应该去哪个科室",
	// 		"seqNo": "2",
	// 		"pageName": "zhfz"
	// 	},
	// 	{
	// 		"templateId": "1892043464938360835",
	// 		"content": "阿莫西林是什么药？",
	// 		"seqNo": "3",
	// 		"pageName": "znwy"
	// 	},
	// 	{
	// 		"templateId": "18920434649383608367",
	// 		"content": "曲普坦口腔崩解片怎么服用？",
	// 		"seqNo": "4",
	// 		"pageName": "znwy"
	// 	}
	// ]
})
</script>

<style lang="less" scoped>
.suggest-content {
	box-sizing: border-box;
	width: 100%;
	position: absolute;
	top: 24rpx;

	&__prompt {
		display: flex;
		align-items: center;
		background: url('static/zhzx-bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		height: 320rpx;

		>div {
			flex: 1;

			.self-consult {
				height: 36rpx;
			}

			.title {
				font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
				font-weight: 700;
				font-size: 40rpx;
				color: #333333;
				line-height: 56rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin: 80rpx 0 14rpx 24rpx;

				.underline {
					position: relative;
					top: -4px;
					left: 3px;
					width: 178rpx;
					height: 18rpx;
					background: linear-gradient(90deg, #B6C9FF 0%, rgba(182, 201, 255, 0) 100%);
					border-radius: 10rpx 10rpx 10rpx 10rpx;
				}
			}



			.desc {
				// margin-top: 4rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
				line-height: 38rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				width: 420rpx;
				margin: 10rpx 0 20rpx 24rpx;
			}
		}

		// .hospital {
		// 	width: 276rpx;
		// }
	}

	&__list {
		// width: 600rpx;
		// background: rgba(255, 255, 255, 0.36);
		// box-shadow: inset 2rpx 1 2rpx 0rpx #FFFFFF, inset 0rpx -1 2rpx 0rpx #FFFFFF;
		// border-radius: 20rpx 20rpx 20rpx 20rpx;
		position: relative;
		padding: 12rpx 0 24rpx;
		margin-top: 40rpx;

		.header {
			// height: 108rpx;
			// display: flex;
			// align-items: center;
			// position: absolute;
			// top: -28rpx;
			margin-bottom: 15rpx;

			.trumpet {
				width: 108rpx;
			}

			.ask {
				width: 336rpx;
				margin-left: 12rpx;
			}
		}

		.item {
			// display: flex;

			align-items: center;
			padding: 2rpx 12rpx;
			margin-top: 24rpx;
			// width: 400rpx;
			// .ask-icon {
			// 	width: 40rpx;
			// 	margin-right: 6rpx;
			// }

			span {
				// flex: 2;
				box-sizing: border-box;
				font-family: Source Han Sans, Source Han Sans;
				background: #F6FAFF;
				border-radius: 44rpx 44rpx 44rpx 44rpx;
				border: 2rpx solid #CFDEFF;
				font-weight: 400;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				line-height: 42rpx;
				text-align: left;
				// background: url('static/ask-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				padding: 10rpx 20rpx;
			}
		}
	}

	&__message {
		width: 90%;
		margin-top: 24rpx;
		margin-left: 12rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 24rpx;
		color: #FF9500;
		line-height: 32rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.scroll-wapper {
		display: flex;
		width: 380%;
		animation: slideLeft 18s linear infinite;
		// animation-delay: -3s;
	}

	.scroll-list {
		display: flex;
		justify-self: flex-start;
		flex-wrap: wrap;
		width: 200%;
		// animation: slideLeft 6s linear infinite;
		// animation-delay: -1s;
		// animation: slideLeft1 10s infinite;
	}

	@keyframes slideLeft {

		0% {
			transform: translateX(20%);
		}

		100% {
			transform: translateX(-100%);
		}

	}

}
</style>
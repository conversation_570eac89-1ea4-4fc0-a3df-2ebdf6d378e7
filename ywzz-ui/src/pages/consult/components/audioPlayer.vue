<template>
  <view class="audio-player">

    <img v-show="!isPlaying" style=" width: 34rpx;" class="play_icon" @click="togglePlay"
      src="../../../static/play-icon.png" alt="" />
    <img v-show="isPlaying" class="play_icon" @click="togglePlay" src="../../../static/pause-icon.png" alt="" />
    <img v-show="isPlaying" class="playing-gif" src="../../../static/playing.gif" alt="" />

  </view>
</template>

<script setup>
import { ref, onBeforeUnmount } from 'vue';
import { getAudio } from '@/api/consult';

const props = defineProps({
  text: {
    type: String,
    required: true
  }
});

// 响应式数据
const audioPath = ref('');
const isDownloading = ref(false);
const downloadProgress = ref(0);
const isPlaying = ref(false);
const audioContext = ref();
const isAudioReady = ref(false);


// 初始化音频播放器
const initAudioPlayer = () => {
  try {
    audioContext.value = uni.createInnerAudioContext();
    audioContext.value.src = audioPath.value;
    // 音频事件监听
    audioContext.value.onPlay(() => {
      isPlaying.value = true;
      isAudioReady.value = true;
    });

    audioContext.value.onPause(() => {
      isPlaying.value = false;
    });

    audioContext.value.onEnded(() => {
      isPlaying.value = false;
      audioContext.value.seek(0);
    });
    audioContext.value.onCanplay(() => {
      console.log('onCanplay');
      // audioContext.value.play();
    });
  } catch (error) {
    console.log('error', error);
  }
  audioContext.value.play();
  audioContext.value.onError((error) => {
    const err = error;
    // isPlaying.value = false;
    console.log('播放错误:', err);
    uni.showToast({ title: '播放失败', icon: 'none' });
  });
};

// 播放/暂停切换
const togglePlay = async () => {
  // if (!isAudioReady.value) return;
  if (isPlaying.value) {
    audioContext.value.pause();
  } else {
    isPlaying.value = true;
    try {
      isDownloading.value = true;
      const response = await getAudio({ text: props.text })

      const blob = new Blob([response], { type: 'audio/wav' });
      // 生成临时URL
      const url = window.URL.createObjectURL(blob);
      audioPath.value = URL.createObjectURL(blob);
      // audioPath.value = '/static/speech_1747202814048.mp3'
      // console.log('audioPath.value', audioPath.value);
      initAudioPlayer();
    } catch (error) {
      isPlaying.value = false;
      console.log('音频下载失败:', error);
      uni.showToast({ title: '音频下载失败', icon: 'none' });
    } finally {
      isDownloading.value = false;
    }
    audioContext.value.play();
  }
};

// 组件卸载时清理
onBeforeUnmount(() => {
  if (audioContext.value) {
    audioContext.value.destroy();
  }
});

// 下载音频（暂时不用）
const handleDownload = async () => {
  try {
    isDownloading.value = true;
    const response = await getAudio({ text: props.text })
    const blob = new Blob([response], { type: 'audio/wav' });
    // 生成临时URL
    const url = window.URL.createObjectURL(blob);
    audioPath.value = URL.createObjectURL(blob);
    initAudioPlayer();
  } catch (error) {
    console.error('下载失败:', error);
    uni.showToast({ title: '下载失败', icon: 'none' });
  } finally {
    isDownloading.value = false;
  }
};
</script>

<style scoped>
.audio-player {
  display: flex;
  align-items: left;
  padding-top: 10rpx;

  .play_icon {
    width: 32rpx;
  }

  .playing-gif {
    margin-left: 15rpx;
    width: 30px;
    height: 18px;
  }


}
</style>
<template>
	<view class="response-content">
		<view>
			<view style="display: flex;">
				<text class="pretext">正在解读报告...</text>
			</view>
			<uv-steps :current="current">
				<uv-steps-item :title="item.title" v-for="item in options">
					<template v-slot:icon>
						<uv-icon v-if="current >= item.current" size="24" name="checkmark-circle"
							color="rgb(0 0 0)"></uv-icon>
						<uv-icon v-if="current == item.current - 1" size="24" name="more-circle"
							color="rgb(0 0 0)"></uv-icon>
						<view v-if="current < item.current - 1" class="unexe"></view>
					</template>
				</uv-steps-item>
			</uv-steps>
		</view>
	</view>

</template>

<script setup>
import { ref, unref, watch, computed, watchEffect, onMounted } from 'vue';
import { useFeedBackState } from '@/store/modules/feedback';
const feedBackState = useFeedBackState();
const porps = defineProps({
	reported: {
		type: Boolean,
		default: false
	}
})
const current = ref(0)
const options = [{ title: '扫描文件', current: 1 }, { title: '身份脱敏', current: 2 }, { title: '分析报告', current: 3 }, { title: '整理结论', current: 4 }]
onMounted(() => {
	let intervalId = setInterval(() => {
		current.value++
		if (current.value >= 2) {
			clearInterval(intervalId)
		}
	}, 1000);
});
watch(
	() => porps.reported,
	(newdata) => {
		if (newdata) {
			current.value = 4
			setTimeout(() => {
				current.value = 5
			}, 500);
		}
	}
);
</script>

<style lang="less" scoped>
:deep(.uv-steps-item__wrapper) {
	background-color: #F1F7FE;
}

:deep(.uv-steps-item__line) {
	background-color: #c4c4c4 !important;
}

.unexe {
	width: 18px;
	height: 18px;
	border: 2px solid silver;
	border-radius: 50%;
}

.response-content {
	max-width: 610rpx;
	margin-top: 24rpx;
	margin-left: 12rpx;
	padding: 20rpx 28rpx 24rpx 5rpx;
	box-sizing: border-box;
	font-weight: 500;
	// font-family: Source Han Sans, Source Han Sans;
	font-size: 28rpx;
	color: #333333;
	line-height: 44rpx;
	font-style: normal;
	background: rgba(255, 255, 255, 0.55);
	border-radius: 0rpx 20rpx 20rpx 20rpx;
	word-break: break-all;
	position: relative;

	.pre {
		width: 6rpx;
		height: 36rpx;
		border-radius: 0 30rpx 30rpx 0;
		background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
		margin-right: 10rpx;
	}

	.pretext {
		color: #000000;
		margin-bottom: 15rpx;
		font-size: 28rpx;
		line-height: 44rpx;
		margin-left: 20rpx;
		margin-bottom: 20rpx;
		font-family: Source Han Sans, Source Han Sans;
		// font-family: '黑体'
		// position: absolute;
	}


}
</style>

<style lang="less" scoped>
@import './feedback.less';
</style>

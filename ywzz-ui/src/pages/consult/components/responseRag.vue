<template>
	<view class="response-content">
		<template v-for="item in item.knowledgeBaseList">
			<br v-if="item.newLineFlag" />
			<span v-if="item.type === 'str'">{{ item.content }}</span>
			<span class="pho" v-if="item.type === 'pho'" @click="handlePhoneCall(item.content)">{{ item.content
			}}</span>
			<a :href="item.url" class="url" v-if="item.type === 'url'">{{ item.content }}</a>
			<a :href="item.url" class="btn" v-if="item.type === 'btn'">{{ item.content }}</a>
		</template>
		<view v-if="!question">
			<AudioPlayer :text="item.text"></AudioPlayer>
		</view>
	</view>
	<view class="response-content" v-html="item.message" v-if="item.message"></view>

	<div class="message" v-if="!question">
		以上回复由人工智能自动生成，仅供参考。
		<div class="thumb_class">
			<img v-show="!isApprove" class="thumb-icon" src="../../../static/thumb-up-icon.png" @click="approve(true)"
				alt="" />
			<img v-show="isApprove" class="thumb-icon-fill" src="../../../static/thumb-up-icon-fill.png"
				@click="approve(false)" alt="" />
			<div class="line">
			</div>

			<img v-show="!isTread" class="thumb-icon" src="../../../static/thumb-down-icon.png" @click="tread(true)"
				alt="" />
			<img v-show="isTread" class="thumb-icon-fill" src="../../../static/thumb-down-icon-fill.png"
				@click="tread(false)" alt="" />
		</div>
	</div>
	<uv-popup ref="popup" mode="bottom" @change="change" closeOnClickOverlay="false" bgColor="none">
		<view class="feed-card">
			<uv-icon name="close" class="close_icon" color="#FFFFFF" size="16" @click="closepopup"></uv-icon>
			<view class="text1">很抱歉回答未能让您满意</view>
			<view class="text2">您的宝贵意见能帮我们优化产品，更好的为您服务。</view>
			<view class="option-card">
				<view :class="item.isCheck ? 'sel_option' : 'option'" v-for="item in feedbackOps" :key="item.optionId"
					@click="selFeedback(item)">{{
						item.optionName }}</view>
			</view>
			<view class="other-card">
				<view class="other_text">其他问题反馈</view>
				<uv-textarea v-model="feedbackContent" style="margin: 15rpx;" count maxlength="500"
					:placeholderStyle="{ fontSize: '24rpx' }" border="none" :customStyle="{
						background: '#EEF4F8',
						borderRadius: '12rpx', border: '2rpx solid #DEF1F1'
					}" placeholder=" 没有想要的选项，您也可以在这里输入反馈内容。"></uv-textarea>
			</view>
			<uv-button text="提交" @click="committread" style="margin: 20rpx;" shape="circle"
				color="linear-gradient( 135deg, #557AF2 0%, #55CFFF 100%)"></uv-button>
		</view>
	</uv-popup>
</template>

<script setup>
import { ref, unref, watch, onMounted, nextTick } from 'vue';
import { gatFeedOptions, feedback, updateFeedback, delFeedBack } from '@/api/consult'
import AudioPlayer from './audioPlayer.vue'

import { useFeedBackState } from '@/store/modules/feedback';
const feedBackState = useFeedBackState();
const porps = defineProps({
	item: {
		type: Object,
		default: () => {
			return {
				knowledgeBaseList: [],
				text: ''
			}
		}
	},
	question: {
		type: Boolean,
		default: false
	}
})

const handlePhoneCall = (phone) => {
	uni.makePhoneCall({
		phoneNumber: phone //仅为示例
	});
};
//对话记录Id
const recordId = ref(porps.item?.recordId)
//点踩反馈组件
const popup = ref(null);
//反馈内容
let feedbackContent = ref('')
//反馈选项
const feedbackOps = ref([])
//选中的反馈
const selFeedbackOps = ref([])
//选中反馈项
function selFeedback(selitem) {
	feedbackOps.value.map(item => {
		if (selitem.optionId == item.optionId) {
			item.isCheck = !item.isCheck
		}
	})
}
//获取反馈选项
feedbackOps.value = feedBackState.feedbackOPS
feedbackOps.value.map(item => {
	item.isCheck = false
})
// gatFeedOptions().then(res => {
// 	feedbackOps.value = res
// 	feedbackOps.value.map(item => {
// 		item.isCheck = false
// 	})
// })
//是否点赞
let isApprove = ref(false);
//是否点踩
let isTread = ref(false);
//点赞
function approve(flag) {
	if (flag == true) {
		const param = {
			recordId: recordId.value,
			optionType: 1,//0-点踩；1-点赞
			recordScene: 0,//1-智慧分诊；0-信息咨询
			optionId: '',
			otherContent: ''
		}
		//变更
		if (isTread.value) {
			updateFeedback(recordId.value, param)
				.then(res => {
					isApprove.value = flag
					isTread.value = !flag
				})
		} else {
			feedback(param).then(res => {
				isApprove.value = flag
				isTread.value = !flag
			})
		}
	} else {
		//取消点赞
		delFeedBack(recordId.value).then(res => {
			isApprove.value = flag
		})
	}
}
//点踩
function tread(flag) {
	if (flag == true) {
		//初始化反馈项目
		feedbackOps.value.map(item => {
			item.isCheck = false
		})
		feedbackContent.value = ''
		popup.value.open();
	} else {
		//取消点踩
		delFeedBack(recordId.value).then(res => {
			isTread.value = flag
		})

	}
}
function closepopup() {
	popup.value.close();
}
//提交点踩反馈
function committread() {
	const optionIds = [];
	feedbackOps.value.map(item => {
		if (item.isCheck) {
			optionIds.push(item.optionId)
		}
	})
	//如果已经点赞了 直接变更
	if (isApprove.value) {
		const param = {
			optionType: 0,//0-点踩；1-点赞
			optionId: optionIds ? optionIds.join(',') : '',
			otherContent: feedbackContent.value
		}
		updateFeedback(recordId.value, param)
			.then(res => {
				popup.value.close();
				isTread.value = true
				isApprove.value = false
			})
	} else {
		const param1 = {
			recordId: recordId.value,
			optionType: 0,//0-点踩；1-点赞
			recordScene: 0,//1-智慧分诊；0-信息咨询
			optionId: optionIds ? optionIds.join(',') : '',
			otherContent: feedbackContent.value
		}
		feedback(param1).then(res => {
			popup.value.close();
			isTread.value = true
			isApprove.value = false
		})
	}

}
</script>

<style lang="less" scoped>
.response-content {
	max-width: 610rpx;
	margin-top: 24rpx;
	margin-left: 12rpx;
	padding: 20rpx 28rpx;
	box-sizing: border-box;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 28rpx;
	color: #333333;
	line-height: 44rpx;
	font-style: normal;
	background: rgba(255, 255, 255, 0.55);
	border-radius: 0rpx 20rpx 20rpx 20rpx;
	word-break: break-all;

	.btn {
		display: inline-block;
		margin-top: 18rpx;
		margin-bottom: 18rpx;
		width: 538rpx;
		text-align: center;
		height: 64rpx;
		line-height: 64rpx;
		font-weight: 500;
		font-size: 28rpx;
		// color: #00C29D;
		color: #557AF2;
		text-decoration: none;
		// background: linear-gradient(347deg, rgba(39, 236, 169, 0.2) 0%, rgba(143, 197, 255, 0.2) 100%);
		border-radius: 32rpx 32rpx 32rpx 32rpx;
		// border: 2rpx solid rgba(39.20656979084015, 236.27978771924973, 168.89347404241562, 1);
		background: linear-gradient(135deg, rgba(85, 122, 242, 0.14) 0%, rgba(85, 207, 255, 0.14) 100%);
		border: 2rpx solid;
		border-image: #557AF2;
		// linear-gradient(135deg, rgba(85.0000025331974, 122.00000032782555, 242.00000077486038, 1), rgba(85.00009372830391, 206.83337420225143, 255, 1));
	}

	.pho,
	.url {
		color: #557AF2;
		text-decoration: none;
	}
}

.message {
	margin-top: 15rpx;
	padding-left: 20rpx;
	font-family: Source Han Sans,
		Source Han Sans;
	font-weight: 400;
	font-size: 24rpx;
	color: #888888;
	line-height: 40rpx;
	display: flex;
}
</style>
<style lang="less" scoped>
@import './feedback.less';
</style>

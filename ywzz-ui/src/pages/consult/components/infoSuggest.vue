<template>
	<view class="suggest-content">
		<div class="suggest-content__list">
			<div class="header">
				<img class="consultation_icon" src="../../../static/xxzx.png" alt="" />
				<div class="title">信息咨询</div>
			</div>
			<div class="text">我可以回答您挂号退号、医保信息、医院地址、服务热线和科普知识等问题，您可以这样问我：

				<div class="item" v-for="(item, index) in suggestList" :key="item.templateId">
					<span @click="emit('check', item.content, item.templateId)">
						<text class="index_class">{{ index + 1 }}</text>{{ item.content }}</span>
					<uv-icon class="right_icon" name="arrow-right" color="#557AF2" size="14"></uv-icon>
				</div>
				<!-- <div class="item">
					<span>...</span>
				</div> -->
			</div>

		</div>

	</view>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"
import { getQuestionnaire } from '@/api/consult.ts'

const emit = defineEmits(['check'])
const suggestList = ref([])
onMounted(async () => {
	const param = {
		pageName: 'xxzx',
		count: 5
	}
	const res = await getQuestionnaire(param)
	suggestList.value = res?.slice(0, 5)
	nextTick(() => {
		// uni.pageScrollTo({
		// 	selector: '.page-bottom',
		// 	duration: 20
		// })
	})
})
</script>
<style lang="less" scoped>
.suggest-content {
	padding-left: 12rpx;
	box-sizing: border-box;
	width: 620rpx;
	margin-top: 24rpx;

	&__prompt {
		display: flex;
		align-items: center;

		>div {
			flex: 1;

			.self-consult {
				height: 36rpx;
			}

			.desc {
				margin-top: 4rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				font-size: 28rpx;
				color: #555555;
				line-height: 40rpx;
				padding-right: 10rpx;
			}
		}

		.hospital {
			width: 276rpx;
		}
	}

	&__list {
		width: 600rpx;
		// background: rgba(255, 255, 255, 0.36);
		// box-shadow: inset 2rpx 1 2rpx 0rpx #FFFFFF, inset 0rpx -1 2rpx 0rpx #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		position: relative;
		// padding: 0rpx 0 -24rpx;
		margin-top: 40rpx;
		// height: 144rpx;

		.text {
			top: -10px;
			width: 548rpx;
			position: relative;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			box-shadow: 0rpx -4rpx 10rpx 0rpx rgba(98, 124, 209, 0.2);
			padding: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.header {
			// padding: 10rpx;
			width: 100%;
			height: 135rpx;
			display: flex;
			align-items: center;
			background: linear-gradient(180deg, #D2E1FF 0%, #FFFFFF 85%);
			border: 2rpx solid #FFFFFF;
			// background: linear-gradient(90deg, #BBF4FF 48%, #D9FFA0 78%, #87EBD2 100%);
			border-radius: 20rpx 20rpx 5rpx 0rpx;
			// border-bottom: 1px;

			.title {
				margin: 0 0 10rpx 85rpx;
				padding: 0 20rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				font-size: 28rpx;
				line-height: 52rpx;
				color: #333333;
			}

			.consultation_icon {
				position: absolute;
				top: -3rpx;
				left: 0;
				width: 88rpx;
				height: 88rpx;
				padding: 15rpx;
			}

			.ask {
				width: 336rpx;
				margin-left: 12rpx;
			}
		}

		.item {
			display: flex;
			align-items: center;
			padding: 0 20rpx 0 12rpx;
			margin-top: 18rpx;
			// width: 544rpx;
			// height: 72rpx;
			background: #ECF4FF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;

			// .ask-icon {
			// 	width: 40rpx;
			// 	margin-right: 6rpx;
			// }
			.index_class {
				font-family: PangMenZhengDao-Regular;
				color: #557AF2;
				position: relative;
				margin-right: 12rpx;
			}

			.right_icon {
				position: absolute;
				right: 40rpx;
				width: 30rpx;
				height: 30rpx;
				padding: 2rpx 0rpx 2rpx 4rpx;
				background: #FFFFFF;
				border-radius: 50%;
			}

			span {
				flex: 1;
				// border-bottom: 1px solid #FFFFFF;
				box-sizing: border-box;
				// font-family: Source Han Sans, Source Han Sans;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 34rpx;
				padding: 20rpx;
			}
		}
	}

	&__message {
		width: 600rpx;
		margin-top: 24rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 24rpx;
		color: #FF9500;
		line-height: 32rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}
</style>
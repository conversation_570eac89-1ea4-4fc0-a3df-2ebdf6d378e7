<template>
	<div class="header">
		<img class="consultation_icon" src="../../../static/kstj-card.png" alt="" />
		<div class="title">科室推荐
			<div class="text">根据您的描述，为您推荐以下科室：</div>
		</div>

	</div>
	<view class="offices-content">
		<div class="item" v-for="(item, index) in departments">
			<img class="praise" v-if="index === 0" src="../../../static/praise-new.png" alt="" />
			<span class="praise" v-else />
			<div class="schedule" v-if="item.deptCode !== '0000' && item.deptCode !== '0001'"
				:class="item.scheduleFlag ? '' : 'no'">{{ item.scheduleFlag ? '有号' : '无号' }}</div>
			<div class="name">{{ item.department }}</div>
			<div class="btn offline" v-if="item.deptCode === '0000'">
				<img class="register" src="../../../static/register1.png" alt="" />
				<span>线下挂号</span>
			</div>
			<div class="btn examination" v-else-if="item.deptCode === '0001'" @click="handleExamination(item)">
				<img class="register" src="../../../static/examination.png" alt="" />
				<span>预约体检</span>
			</div>
			<div class="btn" v-else @click="handleRegister(item)">
				<img class="register" src="../../../static/register.png" alt="" />
				<span>预约挂号</span>
			</div>
		</div>

	</view>
	<uv-toast ref="toast"></uv-toast>
</template>

<script setup>
import { onMounted, ref } from "vue"
const porps = defineProps({
	departments: {
		type: String,
		default: () => []
	}
})

const toast = ref()
const handleRegister = (item) => {
	window.open(item.registerUrl)
	// 使用UrlEncode编码
	// let data = `deptCode=${item.deptCode}&curDeptName=${item.curDeptName}&dataSource=&hospitalID=${item.hospitalID}&titleName=${item.titleName}`
	// let encodeURL = encodeURIComponent(data)
	// window.open(`https://fwcbj.linkingcloud.cn/Account/LoginIn/d793a331-4cd8-4981-9090-0dad5080c15f?nextUrl=https%3A%2F%2Ffwcbj.linkingcloud.cn%2Fapp%2Funattended%2F%23%2FoutpatientService%2Fguahao%2Fregister%3F${encodeURL}`)
	window.open(item.registerUrl)
	// window.open("https://fwcbj.linkingcloud.cn/Account/LoginIn/d793a331-4cd8-4981-9090-0dad5080c15f?nextUrl=https%3A%2F%2Ffwcbj.linkingcloud.cn%2Fapp%2Funattended%2F%23%2FoutpatientService%2Fguahao%2Fregister%3FdeptCode%3D0001_1_183%26curDeptName%3D%E5%86%85%E7%A7%91%E7%B3%BB%E7%BB%9F%26dataSource%3D%26hospitalID%3D0001%26titleName%3D%E5%91%BC%E5%90%B8%E5%86%85%E7%A7%91%E9%97%A8%E8%AF%8A")
	// toast.value.show({
	// 	type: 'default',
	// 	message: "该功能正在建设中"
	// })
}
const handleExamination = (item) => {
	window.open(item.registerUrl)
	// window.open(`https://fwcbj.linkingcloud.cn/Account/Loginin/d793a331-4cd8-4981-9090-0dad5080c15f?nextUrl=https%3a%2f%2ffwcbj.linkingcloud.cn%2fapp%2fmicroofficialsite%2findex.html%23%2f%3fid%3df86d071d-6014-48e1-a6bf-4eb1089c3184`)
}
</script>

<style lang="less" scoped>
.header {
	background: linear-gradient(180deg, #D2E1FF 0%, #FFFFFF 98%);
	border: 2rpx solid #FFFFFF;
	width: 608rpx;
	height: 155rpx;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	top: 50rpx;
	position: relative;
	z-index: 1;
	display: flex;
	align-items: center;
	margin-left: 12rpx;

	.consultation_icon {
		position: absolute;
		top: 5rpx;
		left: 0;
		width: 88rpx;
		height: 88rpx;
		padding: 15rpx;
	}

	.title {
		margin: 0 0 28rpx 95rpx;
		padding: 0 20rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 28rpx;
		line-height: 52rpx;
		color: #333333;
	}

	.text {
		// margin-left: 85rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 24rpx;
		color: #666666;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}

.offices-content {
	z-index: 11;
	position: relative;
	width: 610rpx;
	margin-top: 24rpx;
	margin-left: 12rpx;
	padding: 28rpx 32rpx;
	box-sizing: border-box;
	background: rgba(255, 255, 255);
	// box-shadow: inset 2rpx 2rpx 2rpx 0rpx #FFFFFF, inset 0rpx -2rpx 2rpx 0rpx #FFFFFF;
	box-shadow: 0rpx -4rpx 10rpx 0rpx rgba(98, 124, 209, 0.2);
	border-radius: 20rpx 20rpx 20rpx 20rpx;

	.title {
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
	}

	.item {
		margin-top: 10rpx;
		padding: 10rpx;
		// height: 84rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		background: #ECF4FF;
		// background: rgba(0, 194, 157, 0.051);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		position: relative;

		.schedule {
			position: absolute;
			left: -16rpx;
			top: -6rpx;
			font-size: 24rpx;
			color: #FFFFFF;
			width: 96rpx;
			text-align: center;
			height: 36rpx;
			line-height: 36rpx;
			background: #21CEB9;
			border-radius: 0rpx 0rpx 18rpx 18rpx;
			transform: scale3d(0.66, 0.66, 0.66);

			&.no {
				color: #999;
				background: #E3E3E3;
			}
		}

		.praise {
			width: 24rpx;
			margin: 0 10rpx;
		}

		.name {
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			flex: 1;
		}

		.btn {
			padding: 0 10rpx;
			margin-right: 10rpx;
			box-sizing: border-box;
			height: 52rpx;
			display: flex;
			align-items: center;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 400;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: left;
			font-style: normal;
			text-transform: none;
			background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
			border-radius: 16rpx 16rpx 16rpx 16rpx;

			.register {
				width: 32rpx;
				margin-right: 6rpx;
			}

			&.offline {
				background: linear-gradient(135deg, #20CCE6 0%, #29EFB7 100%);
				//  #aaa;
			}

			&.examination {
				background: linear-gradient(135deg, #F7B94E 0%, #FF8666 100%);
			}
		}
	}

}

.message {
	margin-top: 24rpx;
	padding-left: 26rpx;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 24rpx;
	color: #888888;
	line-height: 40rpx;
}
</style>
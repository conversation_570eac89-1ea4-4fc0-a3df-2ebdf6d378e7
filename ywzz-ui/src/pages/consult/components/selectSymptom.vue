<template>
    <view class="suggest-content">
        <div class="item_wapper" :style="isconfirmed ? { pointerEvents: 'none' } : ''">
            <div class="please">症状描述：</div>
            <br />
            <div :class="item.checked ? 'checked_item' : item.disabled ? 'disabled_item' : 'item'"
                v-for="(item, index) in symptomList" :key="item?.name" @click="checked(item, !item.checked)">
                <span>{{ item?.name }}</span>
            </div>
            <view class="other-card">
                <view class="other_text">如有其他症状，请您在下面输入框中具体描述:</view>
                <!-- <uv-input placeholder="请输入其他症状" border="surround" v-model="inputValue" :customStyle="{
                    borderColor: '#E0EDFF !important', background: '#FFFFFF'
                }" @change=""></uv-input> -->
                <uv-textarea v-model="inputValue" :customStyle="{
                    borderColor: '#E0EDFF !important', background: '#FFFFFF'
                }" :maxlength="100" autoHeight placeholder="请输入其他症状"></uv-textarea>
            </view>
            <uv-button text="确定" @click="committread" style="margin-top: 20rpx;width: 100%;"
                :style="isconfirmed ? { pointerEvents: 'none' } : ''" shape="circle"
                :color="isconfirmed ? '#cbcccc' : 'linear-gradient( 135deg, #557AF2 0%, #55CFFF 100%)'">
            </uv-button>
        </div>

    </view>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"

const props = defineProps({
    symptomLists: {
        type: Array,
        default: []
    },
})
const emit = defineEmits(['check'])
const inputValue = ref('')
const symptomList = ref<any>([]);
symptomList.value = props.symptomLists.map(item => {
    const obj = {
        name: item,
        checked: false,
        disabled: false,
    }
    return obj;
})
symptomList.value = [...symptomList.value, ...[{ name: '以上都没有', disabled: false, checked: false }]]
const checked = (sel, flag) => {
    symptomList.value.map(item => {
        if (sel.name == item.name) {
            item.checked = flag
        }
    })
    if (sel.name != '以上都没有') {
        symptomList.value.map(item => {
            if (item.name == '以上都没有') {
                //如果选了某一个症状，则禁用以上都没有
                if (flag) {
                    item.disabled = flag
                } else {
                    //判断剩余选中数量，如果为0 就取消置灰，如果>0 则继续置灰
                    const count = symptomList.value.filter(item => item.name != '以上都没有' && item.checked).length;
                    if (count == 0) {
                        item.disabled = false
                    } else {
                        item.disabled = true
                    }
                }
            }
        })
    } else {
        symptomList.value.map(item => {
            if (item.name != '以上都没有') {
                //如果选了以上都没有，则禁用所有
                item.disabled = flag
            }
        })
    }
}
const isconfirmed = ref(false)
function committread() {
    const optionIds = [];
    symptomList.value.map(item => {
        if (item.checked) {
            optionIds.push(item.name)
        }
    })
    let result = optionIds ? optionIds.join(',') : ''
    if (result == '以上都没有') {
        result = inputValue.value ? inputValue.value : '以上都没有'
    } else {
        if (inputValue.value) {
            result = result + ',' + inputValue.value
        }
    }
    isconfirmed.value = true
    if (!result) result = '以上都没有'
    emit('check', result)
    console.log('result', result);
}
onMounted(async () => {

    nextTick(() => {
        uni.pageScrollTo({
            selector: '.page-bottom',
            duration: 20
        })
    })
})
</script>

<style lang="less" scoped>
.suggest-content {
    padding-left: 12rpx;
    box-sizing: border-box;
    width: 620rpx;
    margin-top: 24rpx;



    .cure_wapper {
        // width: 620rpx;
        // height: 60px;
        background: rgba(255, 255, 255, 0.55);
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        word-break: break-all;

        .audio {
            padding: 2rpx 28rpx 15rpx;
        }

        .text {
            font-weight: 400;
            font-size: 28rpx;
            font-family: Source Han Sans, Source Han Sans;
            color: #333333;
            line-height: 44rpx;
            text-align: left;
            padding: 25rpx 25rpx 5rpx;
        }
    }

    .other-card {
        padding: 5rpx 5rpx 5rpx 15rpx;
        border-radius: 20rpx;
        // background-color: #ffffff;
    }

    .other_text {
        // font-family: Source Han Sans;
        padding-bottom: 15rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #000000;
        line-height: 30rpx;
        text-align: left;
    }

    :deep(.uv-input--radius,
        .uv-input--square) {
        border-radius: 20rpx;
    }

    :deeo(.uv-border) {
        border-color: #E0EDFF !important;
    }

    .item_wapper {
        margin-top: 20rpx;
        // width: 620rpx;
        // height: 60px;
        background: rgba(255, 255, 255, 0.55);
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        word-break: break-all;
        padding: 25rpx;
        display: flex;
        flex-wrap: wrap;

        .please {
            width: 610rpx;
            font-size: 28rpx;
            color: #000000;
            margin: 0 0 25rpx 8rpx;
        }

        .item {
            // width: 130rpx;
            margin: 0 8rpx 20rpx;
            // height: 48rpx;
            background: #ECF4FF;
            border-radius: 22rpx;
            border: 1px solid;
            padding: 10rpx 25rpx 10rpx 27rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 25rpx;
            color: #333333;
            line-height: 34rpx;
            text-align: center;
            border-color: #55b0f2 //  #557af2;
                // border-image: linear-gradient(135deg, rgba(85.0000025331974, 122.00000032782555, 242.00000077486038, 1), rgba(85.00009372830391, 206.83337420225143, 255, 1));
        }

        .disabled_item {
            margin: 0 8rpx 20rpx;
            // height: 48rpx;
            background: #cbcccc;
            border-radius: 22rpx;
            border: 1px solid;
            padding: 10rpx 25rpx 10rpx 27rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 25rpx;
            color: #ffffff;
            line-height: 34rpx;
            text-align: center;
            border-color: #ffffff; //  #557af2;
            pointer-events: none
        }

        .checked_item {
            // width: 130rpx;
            margin: 0 8rpx 20rpx;
            // height: 48rpx;
            background: #ECF4FF;
            border-radius: 22rpx;
            border: 1px solid;
            padding: 10rpx 25rpx 10rpx 27rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            color: #ffffff;
            font-size: 25rpx;
            line-height: 34rpx;
            background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
            text-align: center;
            border-color: #55b0ff // #55CFFF;
        }
    }
}
</style>

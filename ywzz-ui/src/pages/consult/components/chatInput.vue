<template>
	<view class="chat-input" :style="projectSetting.isDeepSeek ? '' : { height: '154rpx', padding: '32rpx 44rpx' }">
		<view v-if="projectSetting.isDeepSeek" :class="deepSeekState.dsFlag ? 'ds-btn' : 'ds-btn-no'"
			@click="switchDeep()">
			<img v-if="deepSeekState.dsFlag" class="deep-icon" src="../../../static/deepseek.png" alt="" />
			<img v-else class="deep-icon" src="../../../static/nodeep.png" alt="" />
			深度思考
		</view>

		<div style="display: flex;">
			<!-- 语音切换按钮 -->
			<img v-if="!iskeyword" :style="tab === 2 ? { pointerEvents: 'none' } : ''" class="changekey"
				@click="changekey" src="../../../static/keywords.png" alt="" />
			<img v-else :style="tab === 2 ? { pointerEvents: 'none' } : ''" class="changekey" @click="changekey"
				src="../../../static/records.png" alt="" />

			<!-- 正在录音效果弹框 -->
			<div v-show="isRecording" class="recording">
				<img class="recording" src="../../../static/playing-bg3.png" alt="" />
				<div class="listening">
					<text>您说，我在听</text>
					<img class="recording-gif" src="../../../static/recording.gif" alt="" />
				</div>
				<div class="tip">松开手，文字进入输入框</div>
				<!-- <img v-show="isRecording" class="recording" src="../../../static/recording.png" alt="" /> -->
			</div>
			<!-- <div class="reverting" v-if="isReverting">
				<uv-loading-icon text="语音转换中~" textSize="26rpx" color="#559fff" textColor="#559fff"></uv-loading-icon>
			</div> -->
			<div v-if="iskeyword" class="uni-input">
				<div :class="tab === 2 ? 'uni-input-disable' : 'uni-input-in'">

					<input :disabled="tab === 2 ? true : false" v-model="inputValue" placeholder="有什么需要问我的吗~" />
					<img v-if="question" class="send" src="../../../static/send.gif" alt="" />
					<img v-else class="send" @click="handleSend" src="../../../static/send-new.png" alt="" />
				</div>
			</div>

			<div v-else class="uni-input">
				<div class="uni-record-in">
					<div class="record-btn" v-if="!isReverting" @longpress="toggleRecording" @touchend="handleRelease">
						按住说话</div>
					<div class="record-btn" v-if="isReverting">
						<uv-loading-icon text=" 语音转换中~" size="30rpx" textSize="28rpx" color="#559fff"
							textColor="#559fff">
						</uv-loading-icon>
					</div>

					<!-- <input v-model="inputValue" placeholder="有什么需要问我的吗~" />
					<img v-if="question" class="send" src="../../../static/send.gif" alt="" />
					<img v-else class="send" @click="handleSend" src="../../../static/send-new.png" alt="" /> -->
				</div>
			</div>

		</div>

		<div class="tabs" :class="tab === 1 || tab === 2 || tab === 3 || tab === 6 ? 'tabScorll' : ''">
			<!-- <img class="robot" src="../../../static/robot.png" alt="" /> -->
			<div class="btn" :style="item.disabled ? { pointerEvents: 'none' } : ''" v-for="item in shortcutList"
				:key="item.templateId" @click="handleConsult(item)">
				<img :class="tab === 1 ? '' : 'gray'" v-if="item.ico === '1'" src="../../../static/zhfz-icon.png"
					alt="" />
				<img :class="tab === 2 ? '' : 'gray'" v-if="item.ico === '2'" src="../../../static/bgjd-icon.png"
					alt="" />
				<img :class="tab === 6 ? '' : 'gray'" v-if="item.ico === '6'" src="../../../static/xxzx-icon.png"
					alt="" />
				<img :class="tab === 3 ? '' : 'gray'" v-if="item.ico === '3'" src="../../../static/znwy-icon.png"
					alt="" />
				<!-- <img class="consultation" v-if="item.ico === '4'" src="../../../static/4.png" alt="" />
				<img class="consultation" v-if="item.ico === '5'" src="../../../static/5.png" alt="" />
				<img class="consultation" v-if="item.ico === '6'" src="../../../static/6.png" alt="" /> -->
				<span :class="tab == 1 && item.name == '智慧分诊' || tab == 2 && item.name == '报告解读' || tab == 3 &&
					item.name == '智能问药' || tab == 6 && item.name == '信息咨询' ? '' : 'gray'">{{
						item.name }}</span>
				<!-- <span v-if="type !== 1" class="mask"></span> -->

			</div>

		</div>
	</view>
	<uv-toast ref="toast"></uv-toast>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import projectSetting from '@/setting/projectSetting';
import { useDeepSeekStore } from '@/store/modules/deepseek';
import { AudioRecorder } from '@/utils/audioRecorder';
import { uploadRecord } from '@/api/consult';

//录音状态
const isRecording = ref(false);
const statusText = ref('');
let recorder = null;
const deepSeekState = useDeepSeekStore();

const route = useRoute()
const emit = defineEmits(['chat'])
const props = defineProps({
	type: {
		type: Number,
		default: 1
	},
	question: {
		type: Boolean,
		default: ''
	},
	tab: {
		type: Number,
		default: 0
	}
})
const shortcutList = ref([])
const iskeyword = ref(true)
const changekey = () => {
	iskeyword.value = !iskeyword.value
}
onMounted(async () => {
	// const res = await gatShortcutKeys()
	shortcutList.value = [
		{
			"templateId": "1",
			"name": "智慧分诊",
			"ico": "1",
			"type": "1",
			"url": null,
			"seqNo": "1"
		},
		{
			"templateId": "2",
			"name": "报告解读",
			"ico": "2",
			"type": "1",
			"url": null,
			"seqNo": "2",
			// "disabled": true
		},
		{
			"templateId": "7",
			"name": "信息咨询",
			"ico": "6",
			"type": "6",
			"url": null,
			"seqNo": "7"
		},
		{
			"templateId": "3",
			"name": "智能问药",
			"ico": "3",
			"type": "6",
			"url": null,
			"seqNo": "3"
		}
	]
})
// 切换深度思考
const switchDeep = () => {
	deepSeekState.setDeepSeek(!deepSeekState.dsFlag)
}
const inputValue = ref('')
const toast = ref()
const handleSend = () => {
	if (inputValue.value.trim() === '') {
		return toast.value.show({
			type: 'default',
			message: "请输入问题"
		})
	}
	emit('chat', {
		type: 'question',
		content: inputValue.value
	})
	inputValue.value = ''
}

const handleConsult = (item) => {
	if (item.type === '1' && item.templateId === '1') {
		if (route.path === '/pages/consult/index') {
			emit('chat', {
				type: 'zhfz',
			})
			return
		}
		uni.redirectTo({
			url: "/pages/consult/index?type=zhfz"
		})

	} else if (item.type === '1' && item.templateId === '2') {
		if (route.path === '/pages/consult/report') {
			emit('chat', {
				type: 'bgjd',
			})
			return
		}
		uni.redirectTo({
			url: "/pages/consult/report?type=bgjd"
		})
	} else if (item.type === '6' && item.templateId === '3') {
		if (route.path === '/pages/consult/medication') {
			emit('chat', {
				type: 'znwy',
			})
			return
		}
		uni.redirectTo({
			url: "/pages/consult/medication?type=znwy"
		})
	}
	else if (item.type === '2') {
		window.location.href = item.url
	} else if (item.type === '3') {
		emit('chat', {
			type: 'question',
			content: item.name,
		})
	} else if (item.type === '6' && item.templateId === '7') {
		if (route.path === '/pages/consult/info') {
			emit('chat', {
				type: 'xxzx',
			})
			return
		}
		uni.redirectTo({
			url: "/pages/consult/info?type=xxzx"
		})
	}
}
const handleInput = (message, templateId, pageName) => {
	// inputValue.value = message
	// handleSend()
	emit('chat', {
		type: 'question',
		content: message,
		templateId,
		pageName
	})
}
//语音输入相关（长按）
const toggleRecording = async (event) => {
	try {
		event.preventDefault();
		if (!isRecording.value) {
			await startRecording();
		} else {
			await stopRecording();
		}
	} catch (error) {
		handleError(error);
	}
};
//松开
const handleRelease = async (event) => {
	try {
		event.preventDefault();
		if (isRecording.value) {
			await stopRecording();
		}
	} catch (error) {
		handleError(error);
	}
};
const startRecording = async () => {
	recorder = new AudioRecorder({ mimeType: '' });
	await recorder.start();
	isRecording.value = true;
	statusText.value = '正在录音...';
};
const isReverting = ref(false)
const stopRecording = async () => {
	statusText.value = '处理中...';
	const audioBlob = await recorder.stop();
	// console.log('录音结束 audioBlob', audioBlob);
	isRecording.value = false;
	isReverting.value = true;//转换中
	try {
		const result = await recorder.upload(audioBlob, '/api/tts/speech-to-text');
		isReverting.value = false
		if (result) {
			emit('upload-success', result);
			iskeyword.value = true
			inputValue.value = result
		} else {
			uni.showToast({
				title: '请录入语音内容后重试！',
				icon: 'none'
			})
		}
	} catch (error) {
		emit('upload-error', error);
		throw error;
	} finally {
		isRecording.value = false;
		setTimeout(() => statusText.value = '', 2000);
	}
};

const handleError = (error) => {
	console.error(error);
	statusText.value = error.message || '发生错误';
	isRecording.value = false;
	setTimeout(() => statusText.value = '', 3000);
};
defineExpose({
	handleInput,
});
</script>

<style lang="less" scoped>
.chat-input {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 194rpx;
	padding: 15rpx 44rpx;
	box-sizing: border-box;
	background: #F0F7FF;
	box-shadow: inset 2rpx 1 2rpx 0rpx #FFFFFF;
	border-radius: 20rpx 20rpx 0rpx 0rpx;
	border-top: 1px solid #fff;
	z-index: 11;

	.ds-btn {
		margin-bottom: 10rpx;
		width: 180rpx;
		// box-shadow: inset 2rpx 2rpx 2rpx 0rpx #FFFFFF;
		font-size: 24rpx;
		color: #4D6BFE;
		line-height: 34rpx;
		border: 1px solid rgba(0, 122, 255, 0.15);
		border-radius: 32rpx;
		background: #F0F7FF;
		text-align: center;
		padding: 8rpx 12rpx;
	}

	.ds-btn-no {
		margin-bottom: 10rpx;
		width: 180rpx;
		color: #474747;
		font-size: 24rpx;
		line-height: 34rpx;
		border: 1px solid #e3e2e2;
		border-radius: 32rpx;
		background: #ffffff;
		text-align: center;
		padding: 8rpx 12rpx;
	}

	.deep-icon {
		position: relative;
		width: 27rpx;
		height: 27rpx;
		top: 5rpx;
		text-align: center;
	}

	.uni-input {
		width: 100%;
		background-image: linear-gradient(135deg, rgba(85.0000025331974, 122.00000032782555, 242.00000077486038, 1), rgba(85.00009372830391, 206.83337420225143, 255, 1));
		// background-image: linear-gradient(90deg, rgba(58.03973861038685, 153.23718935251236, 255, 1), rgba(77.13665887713432, 255, 210.5341476202011, 1));
		padding: 2rpx;
		border-radius: 44rpx 44rpx 44rpx 44rpx;

		.uni-input-disable {
			padding: 24rpx 20rpx;
			box-sizing: border-box;
			display: flex;
			background: #f6f5f5;
			border-radius: 44rpx 44rpx 44rpx 44rpx;

			input {
				height: 40rpx;
				line-height: 40rpx;
				font-size: 24rpx;
				flex: 1;
			}

			.send {
				width: 36rpx;
				height: 36rpx;
				margin-left: 20rpx;
			}
		}

		.uni-input-in {
			padding: 24rpx 20rpx;
			box-sizing: border-box;
			display: flex;
			background: #FFFFFF;
			border-radius: 44rpx 44rpx 44rpx 44rpx;

			input {
				height: 40rpx;
				line-height: 40rpx;
				font-size: 24rpx;
				flex: 1;
			}

			.send {
				width: 36rpx;
				height: 36rpx;
				margin-left: 20rpx;
			}

		}

		.uni-record-in {
			padding: 22rpx 20rpx;
			box-sizing: border-box;
			// display: flex;
			background: #FFFFFF;
			border-radius: 44rpx 44rpx 44rpx 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			// height: 99%;

			.record-btn {

				font-size: 28rpx;
				color: #559fff;
				// background-color: linear-gradient(135deg, rgba(85.0000025331974, 122.00000032782555, 242.00000077486038, 1), rgba(85.00009372830391, 206.83337420225143, 255, 1));
				text-align: center;
			}
		}
	}

	.changekey {
		width: 86rpx;
		height: 86rpx;
		margin-right: 20rpx;
	}

	.tabs {
		position: absolute;
		left: 1rpx;
		top: -70rpx;
		justify-content: space-around;
		width: 100%;
		padding: 0 10rpx;
		// height: 110rpx;
		box-sizing: border-box;
		display: flex;
		// justify-content: flex-start;
		align-items: flex-end;
		flex-wrap: wrap;

		// .robot {
		// 	width: 116rpx;
		// 	height: 116rpx
		// }

		.btn {
			height: 60rpx;
			line-height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			font-size: 24rpx;
			color: #333333;
			// background: #E7F5F8;
			background: linear-gradient(180deg, #DAEBFF 0%, #FFFFFF 95%);
			box-shadow: inset 2rpx 2rpx 2rpx 0rpx #FFFFFF, inset -2rpx -2rpx 2rpx 0rpx #FFFFFF;
			border-radius: 36rpx;
			border: 2rpx solid #FFFFFF;
			// margin-right: 4rpx;
			margin-bottom: 14rpx;
			padding: 0 18rpx;
			box-sizing: border-box;
			position: relative;
			white-space: nowrap;

			.gray {
				opacity: 0.6;
				color: #888787;
			}

			.mask {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background: rgba(255, 255, 255, 0.4);
				border: 2rpx solid #EEF8FC;
				border-radius: 36rpx;
			}

			img {
				width: 36rpx;
				margin-right: 4rpx;
			}


		}
	}

	.tabScorll {
		flex-wrap: nowrap;
		overflow-x: auto;
		left: 1rpx;
		top: -70rpx;
		justify-content: space-around;
	}
}

.reverting {
	position: absolute;
	bottom: 45rpx;
	left: 42%;
	margin: auto;
	background-color: #fff;
}

.recording {
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 1;
	width: 100%;
	// height: 500rpx;
	background-color: rgb(230, 236, 241);
	color: #ffffff;
	text-align: center;
	display: grid;

	.tip {
		font-weight: 400;
		font-size: 26rpx;
		color: #666666;
		text-align: center;
		display: block;
		// margin: 25px auto 145px;
		z-index: 1;
		margin-bottom: 230rpx;
		// position: absolute;
	}

	.listening {
		display: block;
		margin: 25px auto 15px;
		justify-content: center;
		align-items: center;
		z-index: 1;
		width: 302rpx;
		height: 116rpx;
		background: rgba(255, 255, 255, 0.6);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		border: 2rpx solid #FFFFFF;

		text {
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 40rpx;
			text-align: center;
			display: block;
			margin-top: 10px;
		}

		.recording-gif {
			// margin: auto;
			// justify-content: center;
			// align-items: center;
			z-index: 1;
			width: 40px;
			height: 36px;
		}
	}
}
</style>
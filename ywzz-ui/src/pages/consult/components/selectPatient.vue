<template>
    <view class="suggest-content">
        <div class="cure_wapper">
            <div class="text"> 请选择您要咨询的就诊人：</div>
            <AudioPlayer class="audio" text="请选择您要咨询的就诊人"></AudioPlayer>
        </div>
        <div class="item_wapper">
            <div class="please">请选择就诊人：</div>
            <br />
            <div :class="item.checked ? 'checked_item' : 'item'" v-for="(item, index) in patientList"
                :key="item?.userId" @click="checked(item)">
                <span v-if="item?.userId === '0000'">{{ item?.userName }}</span>
                <span v-else> {{
                    item?.userName }}，{{
                        item?.gender }}，{{
                        item?.age }}岁 </span>

            </div>
        </div>

    </view>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"
import AudioPlayer from './audioPlayer.vue'

const props = defineProps({
    memberList: {
        type: Array,
        default: []
    },
})
const emit = defineEmits(['check'])

const patientList = ref<any>([])
patientList.value = props.memberList.map(item => {
    item.checked = false
    return item;
})
patientList.value = [...patientList.value, ...[{ userName: '其他人', userId: '0000', checked: false }]]
const checked = (sel) => {
    // console.log(index);
    patientList.value.map(item => {
        if (item.userId == sel.userId) {
            item.checked = !item.checked
            if (item.checked) {
                const mes = item?.userId === '0000' ? item.userName : item.userName + '，' + item.gender + '，' + item.age + '岁'
                emit('check', mes)
            }
        } else {
            item.checked = false
        }
    })
    // patientList.value[index].checked = !patientList.value[index].checked
}
onMounted(async () => {
    nextTick(() => {
        uni.pageScrollTo({
            selector: '.page-bottom',
            duration: 20
        })
    })
})
</script>

<style lang="less" scoped>
.suggest-content {
    padding-left: 12rpx;
    box-sizing: border-box;
    width: 620rpx;
    margin-top: 24rpx;



    .cure_wapper {
        // width: 620rpx;
        // height: 60px;
        background: rgba(255, 255, 255, 0.55);
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        word-break: break-all;

        .audio {
            padding: 2rpx 28rpx 15rpx;
        }

        .text {
            font-weight: 400;
            font-size: 28rpx;
            font-family: Source Han Sans, Source Han Sans;
            color: #333333;
            line-height: 44rpx;
            text-align: left;
            padding: 25rpx 25rpx 5rpx;
        }
    }

    .item_wapper {
        margin-top: 20rpx;
        // width: 620rpx;
        // height: 60px;
        background: rgba(255, 255, 255, 0.55);
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        word-break: break-all;
        padding: 25rpx;
        display: flex;
        flex-wrap: wrap;

        .please {
            width: 610rpx;
            font-size: 25rpx;
            color: #333333;
            margin: 0 0 25rpx 8rpx;
        }

        .item {
            width: 220rpx;
            margin: 0 12rpx 26rpx;
            // height: 48rpx;
            background: #ECF4FF;
            border-radius: 25rpx;
            border: 1px solid;
            padding: 10rpx 15rpx 10rpx 16rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            line-height: 34rpx;
            text-align: center;
            border-color: #55b0f2 //  #557af2;
                // border-image: linear-gradient(135deg, rgba(85.0000025331974, 122.00000032782555, 242.00000077486038, 1), rgba(85.00009372830391, 206.83337420225143, 255, 1));
        }

        .checked_item {
            width: 220rpx;
            margin: 0 12rpx 26rpx;
            // height: 48rpx;
            background: #ECF4FF;
            border-radius: 25rpx;
            border: 1px solid;
            padding: 10rpx 15rpx 10rpx 15rpx;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            color: #ffffff;
            font-size: 24rpx;
            line-height: 34rpx;
            background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
            text-align: center;
            border-color: #55b0ff // #55CFFF;
        }
    }
}
</style>

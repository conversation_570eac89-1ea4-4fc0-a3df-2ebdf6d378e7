<template>
	<view class="agreement-content" v-if="agreementShow">
		<div class="wrap">
			<!-- <div class="left"></div> -->
			<div class="bg">
				<div class="title"> 用户协议</div>
				<div class="content">您一旦使用本服务，即表示同意我们在本次自助健康咨询服务中，获取并使用您的就诊信息，专为提升本次自助健康咨询体验。我们郑重承诺，您的数据将严格保密，仅服务于本次咨询。
				</div>
				<div class="content">健康路上，我们携手前行，让每一份信任都转化为安心与便捷！</div>
				<div class="btns">
					<div class="agree" @click="handleClose">同意</div>
					<div class="close" @click="handleClose">取消</div>
				</div>
			</div>
		</div>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, computed } from "vue"
import { AGREEMENT_KEY } from '@/enums/cacheEnum';
import { setAuthCache, getAgreementKey } from '@/utils/auth';
const agreementShow = ref(false)
const handleClose = () => {
	setAuthCache(AGREEMENT_KEY, true)
	agreementShow.value = false
}
onMounted(() => {
	// uni.pageScrollTo({
	// 	selector: '.page-bottom'
	// })
	if (!getAgreementKey()) {
		agreementShow.value = true
	}
})
</script>

<style lang="scss" scoped>
.agreement-content {
	position: fixed;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;

	.wrap {
		width: 720rpx;
		display: flex;
		// .left {
		// 	width: 60rpx;
		// }

		.bg {
			flex: 1;

			background: url('static/agreement-newbg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			height: 752rpx;
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			align-items: center;
			padding: 44rpx;

			.title {
				position: relative;
				top: -80rpx;
				left: -220rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				font-size: 36rpx;
				color: #ffffff;
				line-height: 60rpx;
			}

			.content {
				margin-top: 36rpx;
				// margin-bottom: 10rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-size: 26rpx;
				color: #666666;
				line-height: 42rpx;
				text-indent: 2em;
				padding: 0 10rpx;
			}

			.btns {

				display: block;
				margin-bottom: 18rpx;

				div {
					background: #F2F3F7;
					border-radius: 32rpx 32rpx 32rpx 32rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 400;
					font-size: 32rpx;
					color: #707070;
					width: 600rpx;
					text-align: center;
					height: 88rpx;
					line-height: 88rpx;
					margin: 20rpx 0;
				}

				.agree {
					background: linear-gradient(137deg, #557AF2 0%, #55CFFF 76%);
					color: #FFFFFF;
					// margin-left: 32rpx;
				}
			}
		}
	}

}
</style>
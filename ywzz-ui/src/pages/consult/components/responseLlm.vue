<template>
	<view class="response-content">
		<view v-if="item.thinkContent" class="think-title">已深度思考（用时{{ item.thinkTime }}秒）</view>
		<view class="borderblock">
			<view class="left"></view>
			<view class="think-content" v-if="item.thinkContent" v-html="item.thinkContent">
			</view>
		</view>
		<view v-if="item.content" v-html="item.content"></view>
		<view v-if="!question">
			<AudioPlayer :text="item.content"></AudioPlayer>
		</view>
	</view>
	<Offices :departments="item.departments" v-if="item.departments && item.departments.length > 0" />
	<view class="response-content" v-html="item.message" v-else-if="item.message"></view>
	<div class="message" v-if="!question && item.departments && item.departments.length > 0">
		以上回复由人工智能自动生成，仅供参考。
		<div v-if="item.departments && item.departments.length > 0" class="thumb_class">
			<img v-show="!isApprove" class="thumb-icon" src="../../../static/thumb-up-icon.png" @click="approve(true)"
				alt="" />
			<img v-show="isApprove" class="thumb-icon-fill" src="../../../static/thumb-up-icon-fill.png"
				@click="approve(false)" alt="" />
			<div class="line">
			</div>

			<img v-show="!isTread" class="thumb-icon" src="../../../static/thumb-down-icon.png" @click="tread(true)"
				alt="" />
			<img v-show="isTread" class="thumb-icon-fill" src="../../../static/thumb-down-icon-fill.png"
				@click="tread(false)" alt="" />
		</div>
	</div>
	<uv-popup ref="popup" mode="bottom" @change="change" closeOnClickOverlay="false" bgColor="none">
		<view class="feed-card">
			<uv-icon name="close" class="close_icon" color="#FFFFFF" size="16" @click="closepopup"></uv-icon>
			<view class="text1">很抱歉回答未能让您满意</view>
			<view class="text2">您的宝贵意见能帮我们优化产品，更好的为您服务。</view>
			<view class="option-card">
				<view :class="item.isCheck ? 'sel_option' : 'option'" v-for="item in feedbackOps" :key="item.optionId"
					@click="selFeedback(item)">{{
						item.optionName }}</view>
			</view>
			<view class="other-card">
				<view class="other_text">其他问题反馈</view>
				<uv-textarea v-model="feedbackContent" style="margin: 15rpx;" count maxlength="500"
					:placeholderStyle="{ fontSize: '24rpx' }" border="none" :customStyle="{
						background: '#EEF4F8',
						borderRadius: '12rpx', border: '2rpx solid #DEF1F1'
					}" placeholder=" 没有想要的选项，您也可以在这里输入反馈内容。"></uv-textarea>
			</view>
			<uv-button text="提交" @click="committread" style="margin: 20rpx;" shape="circle"
				color="linear-gradient( 135deg, #557AF2 0%, #55CFFF 100%)"></uv-button>
		</view>
	</uv-popup>
</template>

<script setup>
import Offices from './offices.vue';
import { ref, unref, watch } from 'vue';
import { gatFeedOptions, feedback, updateFeedback, delFeedBack } from '@/api/consult'
import { useFeedBackState } from '@/store/modules/feedback';
import AudioPlayer from './audioPlayer.vue'

const feedBackState = useFeedBackState();
const porps = defineProps({
	item: {
		type: Object,
		default: () => {
			return {
				content: '',
				thinkContent: '',
				departments: [],
				message: '',
				thinkTime: ''
			}
		}
	},
	question: {
		type: Boolean,
		default: false
	}
})

//对话记录Id
const recordId = ref(porps.item?.recordId)
//点踩反馈组件
const popup = ref(null);
//反馈内容
let feedbackContent = ref('')
//反馈选项
const feedbackOps = ref([])
//选中的反馈
const selFeedbackOps = ref([])
//选中反馈项
function selFeedback(selitem) {
	feedbackOps.value.map(item => {
		if (selitem.optionId == item.optionId) {
			item.isCheck = !item.isCheck
		}
	})
}
//获取反馈选项
feedbackOps.value = feedBackState.feedbackOPS
feedbackOps.value.map(item => {
	item.isCheck = false
})

//是否点赞
let isApprove = ref(false);
//是否点踩
let isTread = ref(false);
//点赞
function approve(flag) {
	if (flag == true) {
		const param = {
			recordId: recordId.value,
			optionType: 1,//0-点踩；1-点赞
			recordScene: 0,//1-智慧分诊；0-信息咨询
			optionId: '',
			otherContent: ''
		}
		//变更
		if (isTread.value) {
			updateFeedback(recordId.value, param)
				.then(res => {
					isApprove.value = flag
					isTread.value = !flag
				})
		} else {
			feedback(param).then(res => {
				isApprove.value = flag
				isTread.value = !flag
			})
		}
	} else {
		//取消点赞
		delFeedBack(recordId.value).then(res => {
			isApprove.value = flag
		})
	}
}
//点踩
function tread(flag) {
	if (flag == true) {
		//初始化反馈项目
		feedbackOps.value.map(item => {
			item.isCheck = false
		})
		feedbackContent.value = ''
		popup.value.open();
	} else {
		//取消点踩
		delFeedBack(recordId.value).then(res => {
			isTread.value = flag
		})

	}
}
function closepopup() {
	popup.value.close();
}
//提交点踩反馈
function committread() {
	const optionIds = [];
	feedbackOps.value.map(item => {
		if (item.isCheck) {
			optionIds.push(item.optionId)
		}
	})
	//如果已经点赞了 直接变更
	if (isApprove.value) {
		const param = {
			optionType: 0,//0-点踩；1-点赞
			optionId: optionIds ? optionIds.join(',') : '',
			otherContent: feedbackContent.value
		}
		updateFeedback(recordId.value, param)
			.then(res => {
				popup.value.close();
				isTread.value = true
				isApprove.value = false
			})
	} else {
		const param1 = {
			recordId: recordId.value,
			optionType: 0,//0-点踩；1-点赞
			recordScene: 0,//1-智慧分诊；0-信息咨询
			optionId: optionIds ? optionIds.join(',') : '',
			otherContent: feedbackContent.value
		}
		feedback(param1).then(res => {
			popup.value.close();
			isTread.value = true
			isApprove.value = false
		})
	}

}
</script>

<style lang="less" scoped>
.response-content {
	max-width: 610rpx;
	margin-top: 24rpx;
	margin-left: 12rpx;
	padding: 20rpx 28rpx;
	box-sizing: border-box;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 28rpx;
	color: #333333;
	line-height: 44rpx;
	font-style: normal;
	background: rgba(255, 255, 255, 0.55);
	border-radius: 0rpx 20rpx 20rpx 20rpx;
	word-break: break-all;

	.think-title {
		font-weight: 500;
		font-size: 28rpx;
		line-height: 44rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		color: #557AF2;
	}

	.borderblock {
		width: 100%;
		height: 100%;
		display: flex;
		position: relative;

		.left {
			width: 2px;
			height: calc(100% - 32rpx);
			position: absolute;
			top: 15rpx;
			left: 0;
			background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
		}
	}

	.think-content {
		max-width: 580rpx;
		margin: 15rpx 0;
		padding: 18rpx;
		box-sizing: border-box;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 400;
		font-size: 24rpx;
		color: #666666;
		line-height: 44rpx;
		font-style: normal;
		background: #F1F7FE;
		// border-left: 2px solid #02C3C3;
		// box-shadow: inset 2rpx 2rpx 2rpx 0rpx #FFFFFF, inset 0rpx -2rpx 2rpx 0rpx #FFFFFF;
		border-radius: 10rpx;
		word-break: break-all;



	}
}

.message {
	margin-top: 15rpx;
	padding-left: 20rpx;
	font-family: Source Han Sans,
		Source Han Sans;
	font-weight: 400;
	font-size: 24rpx;
	color: #888888;
	line-height: 40rpx;
	display: flex;
}
</style>

<style lang="less" scoped>
@import './feedback.less';
</style>

<template>
	<view :style="isconfirmed ? { pointerEvents: 'none' } : ''" class="response-content">
		请选择：
		<view v-if="imageName == '头部' || imageName == '面部' || imageName == '耳部'" class="btngroup">
			<view :class="side == '左' ? 'check' : 'uncheck'"
				style="width: 149rpx;border-right: 1px solid #fff;border-radius: 15rpx 0 0 15rpx;padding-left:2rpx;"
				@click="selectSide('左')">左侧</view>
			<view :class="side == '右' ? 'check' : 'uncheck'" @click="selectSide('右')"
				style=" border-radius:0 15rpx 15rpx 0;">右侧</view>
		</view>
		<view v-if="imageName == '头部'">
			<headSvgLeft v-if="side == '左'" @select-body="clickBody" width="100%" height="100%"></headSvgLeft>
			<headSvgRight v-if="side == '右'" @select-body="clickBody" width="100%" height="100%">
			</headSvgRight>
		</view>
		<view v-if="imageName == '面部'">
			<faceSvgLeft v-if="side == '左'" @select-body="clickBody" width="100%" height="100%">
			</faceSvgLeft>
			<faceSvgRight v-if="side == '右'" @select-body="clickBody" width="100%" height="100%">
			</faceSvgRight>
		</view>
		<view v-if="imageName == '耳部'">
			<earsSvgLeft v-if="side == '左'" @select-body="clickBody" width="100%" height="100%">
			</earsSvgLeft>
			<earsSvgRight v-if="side == '右'" @select-body="clickBody" width="100%" height="100%">
			</earsSvgRight>
		</view>
		<view v-if="imageName == '鼻咽喉'">
			<noseSvg @select-body="clickBody" width="100%" height="100%">
			</noseSvg>
		</view>
		<view v-if="imageName == '腹部'">
			<abdomenSvg @select-body="clickBody" width="100%" height="100%">
			</abdomenSvg>
		</view>
		<view v-if="imageName == '后背'">
			<dorsumSvg @select-body="clickBody" width="100%" height="100%">
			</dorsumSvg>
		</view>
		<view v-if="imageName == '颈部'">
			<neckSvg @select-body="clickBody" width="100%" height="100%">
			</neckSvg>
		</view>
		<view v-if="imageName == '会阴-男'">
			<perineumSvgMale @select-body="clickBody" width="100%" height="100%">
			</perineumSvgMale>
		</view>
		<view v-if="imageName == '会阴-女'">
			<perineumSvgFemale @select-body="clickBody" width="100%" height="100%">
			</perineumSvgFemale>
		</view>
		<view v-if="imageName == '口腔'">
			<oralCavitySvg @select-body="clickBody" width="100%" height="100%">
			</oralCavitySvg>
		</view>
		<view v-if="imageName == '上肢'">
			<upperLimbsSvg @select-body="clickBody" width="100%" height="100%">
			</upperLimbsSvg>
		</view>
		<view v-if="imageName == '下肢'">
			<lowerLimbsSvg @select-body="clickBody" width="100%" height="100%">
			</lowerLimbsSvg>
		</view>
		<view v-if="imageName == '胸部-女'">
			<breastSvg @select-body="clickBody" width="100%" height="100%">
			</breastSvg>
		</view>
		<view v-if="imageName == '眼部'">
			<eyesSvg @select-body="clickBody" width="100%" height="100%">
			</eyesSvg>
		</view>
		<view :class="isconfirmed ? 'confirmed' : 'confirm'" @click="confirm">确认</view>
	</view>
	<uv-toast ref="toast"></uv-toast>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue"
import headSvgLeft from '../bodyComp/headSvgLeft.vue'
import headSvgRight from '../bodyComp/headSvgRight.vue'
import faceSvgLeft from '../bodyComp/faceSvgLeft.vue'
import faceSvgRight from '../bodyComp/faceSvgRight.vue'
import earsSvgLeft from '../bodyComp/earsSvgLeft.vue'
import earsSvgRight from '../bodyComp/earsSvgRight.vue'
import noseSvg from '../bodyComp/noseSvg.vue'
import abdomenSvg from '../bodyComp/abdomenSvg.vue'
import dorsumSvg from '../bodyComp/dorsumSvg.vue'
import neckSvg from '../bodyComp/neckSvg.vue'
import perineumSvgMale from '../bodyComp/perineumSvgMale.vue'
import perineumSvgFemale from '../bodyComp/perineumSvgFemale.vue'
import oralCavitySvg from '../bodyComp/oralCavitySvg.vue'
import upperLimbsSvg from '../bodyComp/upperLimbsSvg.vue'
import lowerLimbsSvg from '../bodyComp/lowerLimbsSvg.vue'
import breastSvg from '../bodyComp/breastSvg.vue'
import eyesSvg from '../bodyComp/eyesSvg.vue'

const props = defineProps({
	imageName: {
		type: String,
		default: ''
	},
})
const emit = defineEmits(['selectBody'])

const side = ref('左')
//切换左右侧
const selectSide = (val) => {
	side.value = val
	currbody.value = ''
}

const currbody = ref('')
const clickBody = (body) => {
	currbody.value = body
}

const toast = ref()
const isconfirmed = ref(false)
const confirm = () => {
	if (currbody.value) {
		let msg = ''
		if (props.imageName == '头部' || props.imageName == '面部' || props.imageName == '耳部') {
			msg = side.value + '侧' + currbody.value
		} else {
			msg = currbody.value
		}
		isconfirmed.value = true
		emit('selectBody', msg)
	} else {
		return toast.value.show({
			type: 'default',
			message: "请选择部位！"
		})
	}
}
onMounted(async () => {
	nextTick(() => {
		uni.pageScrollTo({
			selector: '.page-bottom',
			duration: 20
		})
	})
})
</script>

<style lang="less" scoped>
.response-content {
	max-width: 610rpx;
	margin-top: 24rpx;
	margin-left: 12rpx;
	padding: 20rpx 28rpx;
	box-sizing: border-box;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 28rpx;
	color: #333333;
	line-height: 44rpx;
	font-style: normal;
	background: rgba(255, 255, 255, 0.55);
	border-radius: 0rpx 20rpx 20rpx 20rpx;
	word-break: break-all;
	// position: relative;

	.head-body {
		width: 95%;
		// height: 200rpx;
	}

	.confirm {
		width: 538rpx;
		height: 64rpx;
		background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
		border-radius: 32rpx 32rpx 32rpx 32rpx;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.confirmed {
		width: 538rpx;
		height: 64rpx;
		background: #cbcccc;
		border-radius: 32rpx 32rpx 32rpx 32rpx;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		pointer-events: none
	}

	.btngroup {
		margin-left: 100rpx;
		// text-align: center;
		width: 305rpx;
		height: 52rpx;
		background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
		// border: 2rpx solid;
		border-radius: 15rpx;
		display: flex;
		font-size: 24rpx;
		align-items: center;
		justify-content: center;

		.check {
			height: 47rpx;
			width: 150rpx;
			// background: unset;
			background: linear-gradient(135deg, #557AF2 0%, #55CFFF 100%);
			text-align: center;
			color: #ffffff;
			// padding-top: 2rpx;
		}

		.uncheck {
			height: 47rpx;
			width: 149rpx;
			background: #ECF4FF;
			text-align: center;
			color: #000000;
			// padding-top: 2rpx;
		}


	}
}
</style>

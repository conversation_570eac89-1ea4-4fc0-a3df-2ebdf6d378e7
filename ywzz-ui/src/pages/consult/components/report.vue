<template>
	<!-- <view class="report-content">
		<div class="report-content__header">
			<div class="title">总检情况<span>(点击可询问)</span></div>
			<img class="robot" src="../../../static/robot.png" alt="" />
		</div>
		<div class="report-content__list">

			<div class="item" v-for="(item, index) in report" :key="index" >
				<span class="count">{{ index + 1 }}</span>
				<span class="content" @click="emit('check', `体检情况异常指标为${item.item}，该去哪个科室?`)">
					<span>{{ item.item }}</span>
					<img class="right-arrow" src="../../../static/right-arrow.png" alt="" />

				</span>
			</div>

			<div class="message">
				为方便您的报告解读咨询，以上您的体检结果帮您提前列出来，您可以点击向AI提问，或自行输入需要提问的内容。
			</div>
		</div>
	</view> -->
	<view class="suggest-content">
		<div class="suggest-content__list">
			<div class="header">
				<img class="consultation_icon" src="../../../static/bgjd-icon1.png" alt="" />
				<div class="title">总检情况
					<!-- <span> （ 点击可询问 ）</span> -->
				</div>
			</div>
			<div class="text">

				<div class="item" v-for="(item, index) in report" :key="index">
					<span class="count">{{ index + 1 }}</span>
					<span class="content" @click="emit('check', `体检情况异常指标为${item.item}，该去哪个科室?`)">
						<span>{{ item.item }}</span>
						<!-- <img class="right-arrow" src="../../../static/right-arrow.png" alt="" /> -->
						<div class="right-arrow">
							<uv-icon name="arrow-right" color="#557AF2" size="12"
								style="padding: 3rpx 0 0 3rpx;"></uv-icon>
						</div>

					</span>
				</div>
				<div class="message">
					为方便您的报告解读咨询，以上您的体检结果帮您提前列出来，您可以点击向AI提问，或自行输入需要提问的内容。
				</div>

			</div>

			<!-- <div class="text">请描述您当前的具体症状，我将为您提供健康建议、推荐就诊科室。</div> -->
		</div>

	</view>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue"
import { getReportsDetail } from '@/api/report';

const emit = defineEmits(['check'])
const props = defineProps({
	'reportId': {
		type: String,
		default: ''
	}
})
const report = ref({})
onMounted(async () => {
	if (props.reportId) {
		const res = await getReportsDetail({ admId: props.reportId })
		report.value = res.data.summaryItemList
	}
})
</script>

<style lang="less" scoped>
.suggest-content {
	padding-left: 12rpx;
	box-sizing: border-box;
	width: 620rpx;
	margin-top: 24rpx;

	&__list {
		width: 600rpx;
		// background: rgba(255, 255, 255, 0.36);
		// box-shadow: inset 2rpx 1 2rpx 0rpx #FFFFFF, inset 0rpx -1 2rpx 0rpx #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		position: relative;
		// padding: 0rpx 0 -24rpx;
		margin-top: 40rpx;
		// height: 144rpx;

		.text {
			top: -10px;
			width: 548rpx;
			position: relative;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			box-shadow: 0rpx -4rpx 10rpx 0rpx rgba(98, 124, 209, 0.2);
			padding: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;


		}

		.header {
			// padding: 10rpx;
			width: 100%;
			height: 135rpx;
			display: flex;
			align-items: center;
			background: linear-gradient(180deg, #D2E1FF 0%, #FFFFFF 85%);
			border: 2rpx solid #FFFFFF;
			// background: linear-gradient(90deg, #BBF4FF 48%, #D9FFA0 78%, #87EBD2 100%);
			border-radius: 20rpx 20rpx 5rpx 0rpx;
			// border-bottom: 1px;

			.title {
				margin: 0 0 10rpx 85rpx;
				padding: 0 20rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 400;
				font-size: 28rpx;
				line-height: 52rpx;
				color: #333333;

				span {
					color: #888888;
					font-size: 22rpx;
					font-family: Source Han Sans CN;
				}
			}

			.consultation_icon {
				position: absolute;
				top: 8rpx;
				left: 0;
				width: 88rpx;
				height: 88rpx;
				padding: 15rpx;
			}

			.ask {
				width: 336rpx;
				margin-left: 12rpx;
			}
		}

		.item {
			display: flex;
			align-items: center;
			min-height: 74rpx;
			box-sizing: border-box;
			background: #ECF4FF;
			margin-bottom: 15rpx;
			border-radius: 18rpx;

			.count {
				color: #557AF2;
				font-weight: 600;
				font-size: 26rpx;
				min-width: 32rpx;
				text-align: center;
				margin-left: 15rpx;
			}

			.content {
				margin-left: 20rpx;
				margin-right: 30rpx;
				flex: 1;
				font-size: 26rpx;
				color: #333333;
				box-sizing: border-box;
				// line-height: 70rpx;
				border-bottom: 2rpx solid #F8FDFD;
				display: flex;
				font-family: Source Han Sans, Source Han Sans;
				align-items: center;
				min-height: 74rpx;
				padding-top: 3rpx;

				span {
					flex: 1;
				}

				.right-arrow {
					width: 28rpx;
					height: 28rpx;
					text-align: center;
					border-radius: 50%;
					background-color: #FFFFFF;
				}
			}
		}

		.message {
			// width: 600rpx;
			// margin-top: 24rpx;
			// font-family: Source Han Sans, Source Han Sans;
			// font-weight: 400;
			// font-size: 24rpx;
			// color: #00C29D;
			// line-height: 32rpx;
			// background: rgba(87, 224, 198, 0.1);
			// border-radius: 12rpx 12rpx 12rpx 12rpx;
			// padding: 16rpx 28rpx;
			margin-top: 0rpx;
			padding-left: 10rpx;
			font-family: Source Han Sans,
				Source Han Sans;
			font-weight: 400;
			font-size: 24rpx;
			// color: #888888;
			color: #fb9242;
			line-height: 40rpx;
			display: flex;
		}
	}

}
</style>
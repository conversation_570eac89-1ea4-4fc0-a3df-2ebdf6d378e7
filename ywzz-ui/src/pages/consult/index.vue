<template>
	<view class="consult-content">
		<div class="consult-content__chat-list">
			<template v-for="(item, index) in chatContent" :key="index">
				<!-- 报告解读 -->
				<Report v-if="item.type === 'report'" :reportId="reportId" @check="handleSuggest" />
				<Question v-else-if="item.type === 'question'" :content="item.content" />
				<!-- 智慧分诊组件 -->
				<ResponseLlm v-else-if="item.type === 'responseLlm'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<ResponseRag v-else-if="item.type === 'responseRag'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<!-- 智慧分诊 提示语-->
				<AdviceSuggest v-else-if="item.type === 'zhfz'" @check="handleSuggest">
				</AdviceSuggest>

				<SelectPatient v-else-if="item.type === 'selectPatient'" :memberList="memberList"
					@check="handleSuggest">
				</SelectPatient>
				<SelectSymptom v-else-if="item.type === 'selectSymptom'" :symptomLists="symptomLists"
					@check="handleSuggest" />
				<BodyImage v-else-if="item.type === 'body'" :imageName="item.imageName" @selectBody="handleBody">
				</BodyImage>

			</template>
		</div>
	</view>
	<ChatInput ref="chatInput" @chat="handleChat" :tab="1" :question="question" :type="reportId ? 2 : 1" />
	<uv-toast ref="toast"></uv-toast>
	<Popup />

	<uv-picker :showToolbar="true" :closeOnClickOverlay="false" ref="picker" round="true" title="请选择就诊人性别和年龄"
		activeColor="#FFFFFF" :columns="columns" @confirm="confirm"></uv-picker>

	<div class="page-bottom" />
</template>

<script lang="ts" setup>
import { nextTick, ref } from "vue"
import Report from './components/report.vue'
import Question from './components/question.vue'
import ResponseLlm from './components/responseLlm.vue'
import ResponseRag from './components/responseRag.vue'
import InfoSuggest from './components/infoSuggest.vue'
import AdviceSuggest from './components/adviceSuggest.vue'
import SelectPatient from './components/selectPatient.vue'
import SelectSymptom from './components/selectSymptom.vue'
import BodyImage from './components/bodyImage.vue'
import ChatInput from './components/chatInput.vue'
import Popup from './components/popup.vue'
import { sendConsult, gatRecordId, multiDialogue } from '@/api/consult'
import { onLoad } from "@dcloudio/uni-app"
import { getUserList } from '@/api/report';
import { getUserId } from '@/utils/auth';
import { useDeepSeekStore } from '@/store/modules/deepseek';
const deepSeekState = useDeepSeekStore();

type ChatContentType = {
	type: 'suggest' | 'report' | 'question' | 'responseLlm' | 'responseRag' | 'zhfz' | 'selectPatient',
	content?: string,
	recordId?: string,
	thinkContent?: string,
	thinkTime?: string,
	departments?: any[],
	message?: string,
	knowledgeBaseList?: any[],
	requirePatient?: Boolean
}

const reportId = ref('')
//多轮对话记录Id
const recordId = ref('')
onLoad((options) => {
	//测试其他人
	// setTimeout(() => {
	// 	handleSuggest('其他人')
	// }, 2000);
	gatRecordId().then(res => {
		recordId.value = res
		routeMenu(options)
	})
});


function routeMenu(options) {
	reportId.value = options?.reportId
	if (reportId.value) {
		chatContent.value.push({
			type: 'report',
		})
	}
	else if (options?.type == 'zhfz') {
		chatContent.value.push({
			type: 'zhfz',
		})
	} else {
		if (options?.content && options?.type) {
			handleChat({
				type: options?.type,
				content: options?.content,
				recordId: recordId.value
				// templateId: options?.templateId
			})
		}
	}
}


const chatContent: any = ref([])
const chatInput = ref()
const picker = ref()
let columns: any = ref([['男', '女']])
const ages: any = ref([])
for (let i = 1; i < 120; i++) {
	const a = {
		age: i,
		ageName: i + '岁',
		checked: false
	}
	ages.value.push(i + '岁')
}
columns.value.push(ages.value)
//确认其他人选择的情况
const confirm = (e) => {
	let msg = e.value.toString()
	chatInput.value.handleInput(msg)
}
//发送人体患痛部位选择的结果
const handleBody = (result) => {
	chatInput.value.handleInput(result)
}
// 推荐问题
const handleSuggest = (message, templateId = null) => {
	// console.log('message', message)
	if (message == '其他人') {
		// downpopup.value.open()
		picker.value.open()
		setTimeout(() => {
			if (document.getElementsByName('content')) {
				document.getElementsByName('content')[0].style.backgroundColor = 'rgb(255 255 255 / 0%)'
			}
		}, 500);
	} else {
		if (question.value) {
			return toast.value.show({
				type: 'default',
				message: "正在问答中"
			})
		}
		chatInput.value.handleInput(message, templateId)
	}

}
const question = ref(false)
const toast = ref()
const memberList = ref([])//就诊人列表
const symptomLists: any = ref([])//伴随症状列表
// 问答
const handleChat = async (content) => {
	// console.log('content', content);
	if (!content.recordId) {
		content.recordId = recordId.value
	}
	if (content.type == 'zhfz') {
		// if (content.type == 'xxzx' || content.type == 'zhfz') {
		chatContent.value.push({
			type: content.type,
		})
		return;
	}
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	try {
		uni.pageScrollTo({
			selector: '.page-bottom',
			duration: 20
		})
		question.value = true
		chatContent.value.push(content)
		const params = { ...content, dsFlag: deepSeekState.dsFlag }
		const res = await multiDialogue(params)
		// const res = await sendConsult(params)
		// if (res.type === "llm") {
		const response = ref<ChatContentType>({
			type: 'responseLlm',
			recordId: res.recordId,
			content: '',
			thinkContent: '',
			thinkTime: res?.thinkTime,
			requirePatient: res?.consultInfo?.requirePatient
		})

		if (response.value.requirePatient) {
			//查询就诊人列表
			getUserList({ openId: getUserId() }).then((result: any) => {
				memberList.value = result.data
				chatContent.value.push({
					type: 'selectPatient',
				})
				question.value = false
				nextTick(() => {
					uni.pageScrollTo({
						selector: '.page-bottom',
						duration: 20
					})
				})
			})
		} else {
			//科室推荐或继续询问
			chatContent.value.push(response.value)
			typewriter(response.value, res?.thinkContent?.split(''), res.consultInfo?.recommendations?.split(''), () => {
				if (!res?.consultInfo?.departments?.length && !res?.consultInfo?.deptFlag) {
					response.value.message = res?.consultInfo?.message || ''
				} else {
					response.value.departments = res?.consultInfo?.departments || []
					//判断是否有科室，有科室推荐则结束本轮对话(重新创建会话id)，无则继续。
					recordId.value = ''
					gatRecordId().then(res => {
						recordId.value = res
						// console.log('重新产生多轮对话Id', res);
					})
				}
				question.value = false
				nextTick(() => {
					uni.pageScrollTo({
						selector: '.page-bottom',
						duration: 20
					})
				})
			})
		}
		if (res?.consultInfo?.symptomList && res?.consultInfo?.symptomList.length > 0) {
			chatContent.value.push({
				type: 'selectSymptom',
			})
			// symptomLists.value = ['紧张性头痛', '偏头痛', '创伤性头疼', '超级超级无敌头痛啊啊啊啊']
			symptomLists.value = res?.consultInfo?.symptomList
		}
		//判断是否展示人体图
		if (res?.consultInfo?.imageName && res?.consultInfo?.imageName !== '胸部-男') {
			setTimeout(() => {
				const response = ref({
					type: 'body',
					recordId: res.recordId,
					imageName: res?.consultInfo?.imageName
				})
				chatContent.value.push(response.value)
			}, 1000);//文字内容出完再出人体图
		}
	} catch (e) {
		question.value = false
	}
}

// 页面往下滑倒 - 节流
const handleThrottleFn = throttle(() => {
	uni.pageScrollTo({
		selector: '.page-bottom',
		duration: 20
	})
}, 800);

// 文字逐字显示
const typewriter = (target, thinkArr, sourceArr, fn) => {
	if (thinkArr && thinkArr.length) {
		target.thinkContent += thinkArr.shift()
		handleThrottleFn()
		requestAnimationFrame(() => {
			typewriter(target, thinkArr, sourceArr, fn)
		})
		// setTimeout(()=>{typewriter(target, sourceArr,fn)}, 40)
	} else if (sourceArr.length) {
		target.content += sourceArr.shift()
		handleThrottleFn()
		requestAnimationFrame(() => {
			typewriter(target, thinkArr, sourceArr, fn)
		})
		// setTimeout(()=>{typewriter(target, sourceArr,fn)}, 40)
	} else {
		fn()
	}
}
function throttle(fn, delay) {
	let valid = true
	return function () {
		if (!valid) return false
		valid = false
		fn()
		setTimeout(() => {
			valid = true;
		}, delay)
	}
}
</script>

<style lang="scss" scoped>
.consult-content {
	padding-bottom: 400rpx;
}

.page-bottom {
	// margin-top:150rpx;
	height: 2rpx;
}

// :deep(.uv-overlay) {
// 	background-color: rgb(0 0 0 / 0%) !important;
// }
.title {
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 28rpx;
	color: #333333;
	line-height: 62rpx;
	margin: 15rpx 0;
}

:deep(.uv-radio-group--row) {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: space-around;
}

.ages-area {
	height: 300rpx;
	overflow-y: scroll;
	text-align: center;
	align-items: center;
	display: block;
}

.zhanwei {
	height: 90rpx;
	width: 100%;
}

.ages-item {
	width: 100%;
	font-weight: 400;
	font-size: 28rpx;
	color: #C6C6C6;
	line-height: 32rpx;
	height: 80rpx;
	z-index: 1000;
	position: relative;
}

.ages-item-check {
	font-weight: 500;
	height: 80rpx;
	font-size: 28rpx;
	color: #FFFFFF;
	line-height: 32rpx;
	position: relative;
	z-index: 1000;
}

.ages-item-blue {
	position: fixed;
	bottom: 180rpx;
	width: 95%;
	// margin-left: 20rpx;
	height: 80rpx;
	background: #557AF2;
	border-radius: 44rpx 44rpx 44rpx 44rpx;
	// z-index: 997;
}

:deep(.uv-picker) {
	// border: 1px solid #ffffff;
	border-radius: 30rpx 30rpx;
	background: #fff;
}

:deep(.uv-toolbar__title) {
	font-size: 28rpx;
}

:deep(.uni-picker-view-indicator) {
	background: #557AF2;
	z-index: 0;
	// border-radius: 20rpx;
}

:deep(.uv-popup__content) {
	background-color: rgb(255 255 255 / 0%) !important;
}
</style>
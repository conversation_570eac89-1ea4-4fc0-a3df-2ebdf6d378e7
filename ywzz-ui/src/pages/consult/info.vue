<template>
	<view class="consult-content">
		<div class="consult-content__chat-list">
			<template v-for="(item, index) in chatContent" :key="index">
				<Question v-if="item.type === 'question'" :content="item.content" />
				<!-- 智慧分诊组件 -->
				<ResponseLlm v-else-if="item.type === 'responseLlm'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<!-- 信息咨询对话组件 -->
				<ResponseRag v-else-if="item.type === 'responseRag'" :item="item"
					:question="question && index === chatContent.length - 1" />
				<!-- 信息咨询 提示语-->
				<InfoSuggest v-else-if="item.type === 'xxzx'" @check="handleSuggest">
				</InfoSuggest>
			</template>
		</div>
	</view>
	<ChatInput ref="chatInput" @chat="handleChat" :tab="6" :question="question" :type="reportId ? 2 : 1" />
	<uv-toast ref="toast"></uv-toast>
	<Popup />
	<div class="page-bottom" />
</template>

<script lang="ts" setup>
import { nextTick, ref } from "vue"
import Question from './components/question.vue'
import ResponseLlm from './components/responseLlm.vue'
import ResponseRag from './components/responseRag.vue'
import InfoSuggest from './components/infoSuggest.vue'

import ChatInput from './components/chatInput.vue'
import Popup from './components/popup.vue'
import { sendConsult } from '@/api/consult'
import { onLoad } from "@dcloudio/uni-app"

type ChatContentType = {
	type: string,
	content?: string,
	recordId?: string,
	departments?: any[],
	message?: string,
	knowledgeBaseList?: any[],
	text?: string,
}

const reportId = ref('')
onLoad((options) => {
	reportId.value = options?.reportId
	if (reportId.value) {
		chatContent.value.push({
			type: 'report',
		})
	}
	if (options?.type == 'xxzx') {
		chatContent.value.push({
			type: 'xxzx',
		})
	} else {
		if (options?.content && options?.type) {
			handleChat({
				type: options?.type,
				content: options?.content,
				templateId: options?.templateId
			})
		}
	}
});
const chatContent = ref<ChatContentType[]>([])
const chatInput = ref()

// 推荐问题
const handleSuggest = (message, templateId = null) => {
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	chatInput.value.handleInput(message, templateId)
}
const question = ref(false)
const toast = ref()

// 问答
const handleChat = async (content) => {
	if (content.type == 'xxzx' || content.type == 'zhfz') {
		chatContent.value.push({
			type: content.type,
		})
		return;
	}
	if (question.value) {
		return toast.value.show({
			type: 'default',
			message: "正在问答中"
		})
	}
	try {
		uni.pageScrollTo({
			selector: '.page-bottom',
			duration: 20
		})
		question.value = true
		chatContent.value.push(content)
		const params = { ...content }
		const res = await sendConsult(params)
		if (res.type === "llm") {
			const response = ref<ChatContentType>({
				type: 'responseLlm',
				recordId: res.recordId,
				content: '',
			})
			chatContent.value.push(response.value)
			typewriter(response.value, res.consultInfo?.recommendations?.split(''), () => {
				if (!res?.consultInfo?.departments?.length && !res?.consultInfo?.deptFlag) {
					response.value.message = res?.consultInfo?.message || ''
				} else {
					response.value.departments = res?.consultInfo?.departments || []
				}
				question.value = false
				nextTick(() => {
					uni.pageScrollTo({
						selector: '.page-bottom',
						duration: 20
					})
				})
			})

		} else if (res.type === "rag") {
			const response = ref<ChatContentType>({
				type: 'responseRag',
				recordId: res.recordId,
				knowledgeBaseList: [],
				text: ''
			})
			// console.log('查看信息咨询结果res', res);
			let text = ''
			res.knowledgeBaseList.map(item => {
				text += item.content
			})
			response.value.text = text;
			const start = (item) => {
				if (item.type === 'btn') {
					response.value.knowledgeBaseList.push(item)
					const shift = res.knowledgeBaseList.shift()
					if (shift) {
						start(shift)
					} else {
						question.value = false
					}
					nextTick(() => {
						uni.pageScrollTo({
							selector: '.page-bottom',
							duration: 20
						})
					})
				} else {
					response.value.knowledgeBaseList.push({
						type: item.type,
						content: "",
						url: item.url,
						newLineFlag: item.newLineFlag
					})
					const target = response.value.knowledgeBaseList[response.value.knowledgeBaseList.length - 1]
					typewriter(target, item.content?.split(''), () => {
						const shift = res.knowledgeBaseList.shift()
						if (shift) {
							start(shift)
						} else {
							question.value = false
						}
						uni.pageScrollTo({
							selector: '.page-bottom',
							duration: 20
						})
					})
				}
			}
			const shift = res.knowledgeBaseList.shift()
			shift && start(shift)
			chatContent.value.push(response.value)
		}

	} catch (e) {
		question.value = false
	}
}

// 页面往下滑倒 - 节流
const handleThrottleFn = throttle(() => {
	uni.pageScrollTo({
		selector: '.page-bottom',
		duration: 20
	})
}, 800);

// 文字逐字显示
const typewriter = (target, sourceArr, fn) => {
	if (sourceArr.length) {
		target.content += sourceArr.shift()
		handleThrottleFn()
		requestAnimationFrame(() => {
			typewriter(target, sourceArr, fn)
		})
		// setTimeout(()=>{typewriter(target, sourceArr,fn)}, 40)
	} else {
		fn()
	}
}
function throttle(fn, delay) {
	let valid = true
	return function () {
		if (!valid) return false
		valid = false
		fn()
		setTimeout(() => {
			valid = true;
		}, delay)
	}
}
</script>

<style lang="scss" scoped>
.consult-content {
	padding-bottom: 400rpx;
}

.page-bottom {
	// margin-top:150rpx;
	height: 2rpx;
}
</style>
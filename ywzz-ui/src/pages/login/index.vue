<template>
    <view class="content" >
      <u-button type="success" @click="handleLogin">登录</u-button>
    </view>
  </template>
  
  <script lang="ts" setup>
	  import { login } from '@/api/login.ts'
	 
	  const handleLogin = () => {
		login({
			account: 'lisi',
			id: '2',
			timestamp: '**********'
		})
	  };
  </script>
  
  <style lang="scss">
  .content {
    padding: 20px;
    box-sizing: border-box;
    background-color: $uv-bg-color;
    width: 100%;
    // #ifdef H5
    height: calc(100vh - var(--window-top) - var(--window-bottom));
    // #endif
    // #ifndef H5
    height: 100vh;
    // #endif
  }
  </style>
  
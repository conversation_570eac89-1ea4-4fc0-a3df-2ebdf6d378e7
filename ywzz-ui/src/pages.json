{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uv-(.*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue",
			"^u-([^-].*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue",
			"^u--(.*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue"
		}
	},
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path": "pages/consult/index",
			"style": {
				"navigationBarTitleText": "智慧分诊"
			}
		},
		{
			"path": "pages/consult/report",
			"style": {
				"navigationBarTitleText": "报告解读"
			}
		},
		{
			"path": "pages/consult/medication",
			"style": {
				"navigationBarTitleText": "智能问药"
			}
		},
		{
			"path": "pages/consult/info",
			"style": {
				"navigationBarTitleText": "信息咨询"
			}
		},
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/report/index",
			"style": {
				"navigationBarTitleText": "报告"
			}
		},
		{
			"path": "pages/report-info/index",
			"style": {
				"navigationBarTitleText": "报告解读"
			}
		}
	],
	"globalStyle": {
		"rpxCalcMaxDeviceWidth": 8640, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "transparent",
		"app-plus": {
			"titleNView": false
		}
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#3cc51f",
		"list": [
			{
				// 	"pagePath": "pages/consult/index",
				// 	"text": "健康咨询"
				// }, {
				// 	"pagePath": "pages/index/index",
				// 	"text": "验证加密"
			}
		]
	}
}
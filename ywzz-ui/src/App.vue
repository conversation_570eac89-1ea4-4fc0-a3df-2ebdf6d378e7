<script setup lang="ts">
import { gatewayKey, getEncryptConfig, getSecret } from '@/api/login.ts'
import {
	TOKEN_KEY, USER_ID_KEY, PRIVAYE_KEY, PUBLICKEY_KEY, EXPIRETIME_KEY
} from '@/enums/cacheEnum';
import { setAuthCache } from '@/utils/auth';
import { sm4Decrypt } from '@/utils/sm/index';
import { onLaunch, onShow, onHide, onThemeChange } from "@dcloudio/uni-app";
import { useFeedBackState } from '@/store/modules/feedback';
const feedBackState = useFeedBackState();
onLaunch(async (options) => {

	try {
		const { token, openId } = options?.query
		if (token && openId) {
			setAuthCache(TOKEN_KEY, token)
			setAuthCache(USER_ID_KEY, openId)
			// const res = await getSecret()
			// setAuthCache(PRIVAYE_KEY, sm4Decrypt(res.privateKey))
			// setAuthCache(PUBLICKEY_KEY, sm4Decrypt(res.publicKey))
			// setAuthCache(EXPIRETIME_KEY, res.expireTime + new Date().getTime())
		} else {
			// uni.showModal({
			// 	title: '温馨提示',
			// 	showCancel: false,
			// 	content: '请重新进入智慧分诊平台。',
			// 	success: function (res) {
			// 		window.history.back()
			// 	}
			// });
		}
	} catch (e) {
		// uni.showModal({
		// 		title: '温馨提示',
		// 		showCancel: false,
		// 		content: '请重新进入智慧分诊平台。',
		// 		success: function (res) {
		// 			window.history.back()
		// 		}
		// 	});
	}

});
onShow(() => {
	feedBackState.setFeedback()
	console.log("App Show");
});
onHide(() => {
	console.log("App Hide");
});
onThemeChange(() => {
	console.log("onThemeChange 主题变化了");
})
</script>
<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import "@climblee/uv-ui/index.scss";

@font-face {
	font-family: Source Han Sans;
	src: url('@/static/font/SourceHanSansCN.ttf')
}

body {
	padding: 0 12px;
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	background: url('static/bg-new.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-attachment: fixed;
}
</style>
<template>
    <view class="empty" :style="{ height: height }">
        <view v-if="type === '401' || type === '404' || type === '500'" class="status-img-box">
            <image :src="statusInfo[type]" class="empty-img" />
             <!-- 插槽部分：给组件底部传入slot内容 -->
             <slot></slot>
        </view>
        <uv-empty v-else :mode="type" :icon="`https://cdn.uviewui.com/uview/empty/${type}.png`" :text="text" :textColor="textColor" :textSize="textSize">
            <template v-slot>
                <!-- 插槽部分：给组件底部传入slot内容 -->
                <slot></slot>
            </template>
        </uv-empty>
    </view>
</template>

<script lang="ts" setup>
/**
 * empty 内容为空时展示
 * @description 组件展示内容为空时，可使用该组件进行展示
 * @tutorial https://www.uvui.cn/components/empty.html
 * @property {String} type 展示类型，默认值为"data"，可选值："data"、"401"、"404"、"500"
 * @property {String} height 组件高度，默认值为"100%"
 * 
 * @example <CTEmpty text="没有内容" type="401"></CTEmpty>
 */
defineProps({
    // 展示类型，默认值为"data"，可选值："data"、"401"、"404"、"500"
    type: {
        type: String,
        default: 'data'
    },
    // 组件高度，默认值为"100%"
    height: {
        type: String,
        default: '100%'
    },
    // 底部内容
    text: {
        type: String,
        default: '暂无数据'
    },
    // 底部内容颜色
    textColor: {
        type: String,
        default: '#c0c4cc'
    },
    // 底部内容字体大小
    textSize: {
        type: String,
        default: '14'
    }
})
const statusInfo = {
    '401': '../../static/status/401.png',
    '404': '../../static/status/404.png',
    '500': '../../static/status/500.png',
}
</script>

<style lang="scss" scoped>
.empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-img {
        width: 450rpx;
        height: 372rpx;
    }
}
</style>
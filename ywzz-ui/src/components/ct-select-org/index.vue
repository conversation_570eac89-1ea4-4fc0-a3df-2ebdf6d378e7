<!--  -->
<template>
    <view class="org">
        <view class="org-search">
            <uv-search inputAlign="center" bgColor="#fff" shape="square" placeholder="搜索"></uv-search>
        </view>
        <!-- <view class="org-layer">中电翼康有限公司>兰州研发中心>曹**</view> -->
        <view class="org-list">
            <uv-index-list :index-list='indexList'>
                <template v-for="(item, index) in orgList" :key="index">
                    <!-- #ifdef APP-NVUE -->
                    <u-index-anchor bgColor="#f3f4f8" :text="indexList[index]"></u-index-anchor>
                    <!-- #endif -->
                    <uv-index-item>
                        <!-- #ifndef APP-NVUE -->
                        <uv-index-anchor bgColor="#f3f4f8" :text="indexList[index]"></uv-index-anchor>
                        <!-- #endif -->
                        <view style="margin: 0px 10px;">
                            <uv-checkbox-group v-model="selectUsers[index]" placement="column">
                                <uv-checkbox v-for="(cb, idx) in item" :key="idx" :name="cb">
                                    <view class="org-list-item">
                                        <view class="org-list-item-avatar">
                                            <uv-avatar shape="square"
                                                src="https://via.placeholder.com/200x200.png/2878ff"></uv-avatar>
                                        </view>
                                        <span class="org-list-item-name">{{ cb }}</span>
                                    </view>
                                </uv-checkbox>
                            </uv-checkbox-group>
                        </view>
                    </uv-index-item>
                </template>
            </uv-index-list>
        </view>

        <view class="org-footer">
            <view class="org-footer-avatars">
                <view class="org-footer-avatars-item" @click="removeUser(index)" v-for="(item, index) in selectUsers"
                    :key='item'>
                    <uv-avatar shape="square" src="https://via.placeholder.com/200x200.png/2878ff"></uv-avatar>
                </view>
            </view>
            <uv-button type="primary" class="submit">
                确&nbsp;&nbsp;定
                <span>({{ selectUsers.length || 0}})</span>
            </uv-button>
        </view>
    </view>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, getCurrentInstance, computed } from "vue";
import { useAppInject } from '@/hooks/web/useAppInject';
export default defineComponent({
    name: "CTSelectOrg",
    setup() {
        const proxy = getCurrentInstance();
        const appInject = useAppInject();
        const searchDomInfo = ref<any>(null);
        const selectUsers = ref<any[]>([]);
        const indexList = ref<string[]>(["A", "B", "C", "D", "E", "F", "G"]);
        const orgList = ref<any[]>([
            ['列表A1', '列表A2', '列表A3'],
            ['列表B1', '列表B2', '列表B3'],
            ['列表C1', '列表C2', '列表C3'],
            ['列表D1', '列表D2', '列表D3'],
            ['列表E1', '列表E2', '列表E3'],
            ['列表F1', '列表F2', '列表F3'],
            ['列表G1', '列表G2', '列表G3']
        ])

        onMounted(async () => {
            searchDomInfo.value = await proxy?.ctx.$uv.getRect('.org-search')
        })
        
        //Todo Code
        const handleClick = (e: number) => {
            selectUsers.value.push(e);
        };

        const removeUser = (e: number) => {
            console.log(e);
            selectUsers.value.splice(e, 1);
        };


        return {
            handleClick,
            selectUsers,
            removeUser,
            orgList,
            indexList,
            searchDomInfo
        };
    },
});
</script>

<style lang="scss" scoped>

$footer-height: 64px;
.org {
    
    position: relative;

    &-search {
        padding: 15px;
        background-color: #f3f4f8;
    }

    &-layer {
        height: 40px;
        padding: 0 15px;
        font-size: 16px;
        color: $uv-primary;
    }

    &-list {
        position: relative;

        // #ifdef H5
        height: calc(100vh - 110px - $footer-height);
        // #endif
        // #ifndef H5
        height: calc(100vh - 75px - $footer-height);
        // #endif
        background-color: #fff;
        overflow-y: scroll;
        transition: height 0.2s ease-in;
        position: relative;

        &-item {
            display: flex;
            align-items: center;
            padding: 10px 0px;
            border-bottom: 1px solid #e6e6e6;

            // &-avatar {}

            &-name {
                margin-left: 20px;
            }

        }
    }

    &-footer {
        position: fixed;
        bottom: 0px;
        left: 0px;
        right: 0px;
        height: $footer-height;

        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;

        background-color: #fff;

        padding: 0px 15px;
        // #ifndef H5
        height: calc($footer-height + 10px);
        padding-bottom: 20px;
        // #endif
        transition: height 0.2s ease-in;
        border-top: 1px solid $uv-border-color;

        &-avatars {
            display: flex;
            align-items: center;
            flex: 1;
            margin-right: 20px;
            overflow-x: scroll;

            &::-webkit-scrollbar {
                display: none;
            }

            &-item {

                &:not(:last-child) {
                    margin-right: 10px;
                }
            }
        }

        .submit {
            flex-shrink: 0;
        }
    }
}
</style>
export interface AppInfo {
    SDKVersion?: string,
    appId?: string,
    appLanguage?: string,
    appName?: string,
    appVersion?: string,
    appVersionCode?: string,
    browserName?: string,
    browserVersion?: string,
    deviceId?: string,
    deviceModel?: string,
    deviceOrientation?: "portrait" ,
    devicePixelRatio?: number,
    deviceType?: string,
    hostLanguage?: string,
    hostName?: string,
    hostTheme?: "light" | "dark",
    hostVersion?: string,
    language?: string,
    model?: string,
    osName?: string,
    osVersion?: string,
    pixelRatio?: number,
    platform?: string,
    safeArea?: {
        left: number,
        right: number,
        top: number,
        bottom: number,
        width: number,
        height: number
    },
    safeAreaInsets: {
        top: number,
        right: number,
        bottom: number,
        left: number
    },
    screenHeight: number,
    screenWidth: number,
    statusBarHeight: number,
    system: string,
    ua: string,
    uniCompileVersion: string,
    uniPlatform: "web" | "mp-weixin" | "app-plus" | "h5" | "app" | string,
    uniRuntimeVersion: string,
    version: string,
    windowBottom: number;
    windowHeight: number,
    windowTop: number,
    windowWidth: number
  }
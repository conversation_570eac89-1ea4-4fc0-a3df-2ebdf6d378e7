export type ErrorMessageMode = 'none' | 'modal' | 'message' | undefined;
export type SuccessMessageMode = ErrorMessageMode;

export interface RequestOptions {
  // 将请求参数拼接到url 
  // GET和DELETE默认进行拼接
  joinParamsToUrl?: boolean;
  // 格式化请求参数时间
  formatDate?: boolean;
  // 是否处理请求结果
  isTransformResponse?: boolean;
  // Whether to return native response headers
  // For example: use this attribute when you need to get the response headers
  isReturnNativeResponse?: boolean;
  // 是否加入url
  joinPrefix?: boolean;
  // 重置接口地址，如果保留为空，请使用默认的apiUrl
  // config.baseURL = apiUrl
  apiUrl?: string;
  // 拼接路径，请求地址前缀
  // config.url = `${urlPrefix}${config.url}`;
  urlPrefix?: string;
  // 错误消息提示类型
  errorMessageMode?: ErrorMessageMode;
  // 成功消息提示类型
  successMessageMode?: SuccessMessageMode;
  // 是否添加时间戳
  joinTime?: boolean;
  // 是否添加Token
  withToken?: boolean;
  
  // authentication schemes，e.g: Bearer
  // authenticationScheme: 'Bearer',
  authenticationScheme?: string;
}

export interface ResponseResult<T = any> {
  type: 'success' | 'error' | 'warning';
  errMsg: string;
  config: RequestOptions;
  statusCode: number;
  data:T
}

export interface Result<T = any> {
  type: 'success' | 'error' | 'warning';
  msg: string;
  data:T
}

// multipart/form-data: upload file
export interface UploadFileParams {
  // Other parameters
  data?: Recordable;
  // File parameter interface field name
  name?: string;
  // file name
  file: File | Blob;
  // file name
  filename?: string;
  [key: string]: any;
}


export type Method = 'get' | "GET"
  | "post" | "POST"
  | "put" | "PUT"
  | "delete" | "DELETE" | "connect" | "CONNECT" | "head" | "HEAD" | "options" | "OPTIONS" | "trace" | "TRACE" | "upload" | "UPLOAD" | "download" | "DOWNLOAD";

type ContentType = 'text/html' | 'text/plain' | 'multipart/form-data' | 'application/json' | 'application/x-www-form-urlencoded' | 'application/octet-stream';

export interface RequestConfig<D = any> {
  /** 请求服务器接口地址 */
  url?: string;
  /** 请求方式 */
  method?: Method;
  /** 请求基地址 */
  baseURL?: string;
  header?: any;
  params?: any;
  sourceData?: any;
  data?: D;
  timeout?: number;
  /** 如果设为 json，会尝试对返回的数据做一次 JSON.parse */
  dataType?: string;
  /** DNS解析时优先使用ipv4，仅 App-Android 支持 (HBuilderX 2.8.0+) */
  firstIpv4?: boolean;
  /** 验证 ssl 证书 仅5+App安卓端支持（HBuilderX 2.3.3+） */
  sslVerify?: boolean;
  /** 跨域请求时是否携带凭证（cookies）仅H5支持（HBuilderX 2.6.15+） */
  withCredentials?: boolean;
  /** 返回当前请求的task, options。请勿在此处修改options。 */
  getTask?: (task: T, options: RequestConfig<T>) => void;
  /**  全局自定义验证器 */
  validateStatus?: (statusCode: number) => boolean | void;

  /** 文件对应的 key */
  name?: string;
  /** HTTP 请求中其他额外的 form data */
  formData?: AnyObject;
  /** 要上传文件资源的路径。 */
  filePath?: string;
  /** 需要上传的文件列表。使用 files 时，filePath 和 name 不生效，App、H5（ 2.6.15+） */
  files?: Array<{
    name?: string;
    file?: File;
    uri: string;
  }>;
  /** 要上传的文件对象，仅H5（2.6.15+）支持 */
  file?: File;
  /** 设置响应的数据类型，支付宝小程序不支持 */
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text';

}

{"$schema": "https://json.schemastore.org/tsconfig", "extends": ["@vue/tsconfig/tsconfig.json", "@yk/ts-config/vue-app.json"], "compilerOptions": {"target": "es2020", "sourceMap": true, "baseUrl": ".", "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "vite/client"], "paths": {"@/*": ["./src/*"], "#/*": ["./types/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.ts", "types/**/*.d.ts", "types/**/*.ts", "vite.config.ts", "mock", "src/components/ctykOrgSelect/index.nvue"], "exclude": ["node_modules", "dist", "**/*.js", "unpackage", "src/**/*.nvue"]}
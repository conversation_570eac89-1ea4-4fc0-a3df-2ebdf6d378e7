{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"bootstrap": "pnpm install", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "preinstall": "npx only-allow pnpm", "postinstall": "turbo run stub", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "lint": "turbo run lint"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["prettier --write", "stylelint --fix"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@climblee/uv-ui": "^1.1.20", "@dcloudio/uni-app": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-app-harmony": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-components": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-h5": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4020320240703001", "@noble/curves": "^1.4.2", "audio-buffer": "^5.0.0", "clipboard": "^2.0.11", "dayjs": "^1.11.10", "gm-crypt": "^0.0.2", "js-base64": "^3.7.7", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "path-to-regexp": "^6.2.2", "pinia": "2.0.27", "showdown": "^2.1.0", "sm-crypto-v2": "^1.9.1", "vue": "^3.4.31", "vue-i18n": "^9.13.1", "vue-types": "^5.1.1", "wav-encoder": "1.3.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4020320240703001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4020320240703001", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@vue/runtime-core": "^3.4.31", "@vue/tsconfig": "^0.1.3", "@yk/eslint-config": "workspace:*", "@yk/ts-config": "workspace:*", "@yk/types": "workspace:*", "@yk/vite-config": "workspace:*", "cz-git": "^1.9.1", "lint-staged": "15.2.2", "prettier": "^3.2.5", "prettier-plugin-packagejson": "^2.4.11", "rimraf": "^5.0.5", "turbo": "^1.12.4", "typescript": "^5.3.3", "unbuild": "^2.0.0", "unocss": "0.58.5", "vite": "5.2.8", "vite-plugin-mock": "^2.9.6", "vue-tsc": "^1.0.24"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.6.0"}}
{"folders": [{"name": "@yk/eslint-config", "path": "internal/eslint-config"}, {"name": "@yk/prettier-config", "path": "internal/prettier-config"}, {"name": "@yk/stylelint-config", "path": "internal/stylelint-config"}, {"name": "@yk/ts-config", "path": "internal/ts-config"}, {"name": "@yk/vite-config", "path": "internal/vite-config"}, {"name": "@yk/hooks", "path": "packages/hooks"}, {"name": "@yk/types", "path": "packages/types"}, {"name": "root", "path": "."}], "settings": {"typescript.tsdk": "root\\node_modules\\typescript\\lib"}}
import { defineApplicationConfig } from "@yk/vite-config";
import projectSetting from './src/setting/projectSetting';
export default defineApplicationConfig({
  overrides: {
    optimizeDeps: {
      // include: ["@iconify/iconify"],
    },
    server: {
      port: 8080,
      proxy: {
        "/basic-api": {
          target: projectSetting.prefix,
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(new RegExp(`^/basic-api`), ""),
          // only https
          // secure: false
        },
      },
      warmup: {
        clientFiles: ["./index.html", "./src/{pages,components}/*"],
      },
    },
    experimental: {
      renderBuiltUrl(filename) {
        return projectSetting.prefix + filename;
      },
    },
  },
});

